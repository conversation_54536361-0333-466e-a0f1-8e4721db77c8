package net.summerfarm.tms.service.common.impl;

import com.alibaba.excel.util.FileUtils;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.client.enums.DownloadCenterEnum;
import net.summerfarm.tms.base.car.dto.CarDTO;
import net.summerfarm.tms.base.car.entity.CarEntity;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.excel.pojo.CarExcelPojo;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.common.DownloadCenterFacade;
import net.summerfarm.tms.local.base.car.CarDtoConverter;
import net.summerfarm.tms.service.common.DownloadCenterService;
import net.summerfarm.tms.util.ExecutorUtil;
import net.xianmu.download.support.core.DownloadCenterHelper;
import net.xianmu.download.support.dto.DownloadCenterOssRespDTO;
import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import net.xianmu.oss.common.util.OssUploadUtil;
import net.xianmu.oss.result.OssUploadResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.security.ProviderException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:下载中心服务接口实现
 * date: 2024/2/20 17:11
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DownloadCenterServiceImpl implements DownloadCenterService {

    private final DownloadCenterFacade downloadCenterFacade;

    @Override
    public <P, R> Long asyncUpload(DownloadCenterRecordDTO recordDTO, P paramOrData, Function<P, List<R>> queryFunction, Class<R> excelClass) {
        //参数校验
        TmsAssert.notNull(queryFunction, ErrorCodeEnum.PARAM_ERROR, "queryFunction");
        TmsAssert.notNull(excelClass, ErrorCodeEnum.PARAM_ERROR, "excelClass");
        this.validateParam(recordDTO);
        return DownloadCenterHelper.build(ExecutorUtil.downloadCenterExecutor, recordDTO).asyncWrite(paramOrData, queryFunction, excelClass);
    }

    @Override
    public <T> Long asyncUploadAndRecordResult(DownloadCenterRecordDTO recordDTO, T paramOrData, BiConsumer<File, T> writeDataToTempFileConsumer) {
        //参数校验
        TmsAssert.notNull(writeDataToTempFileConsumer, ErrorCodeEnum.PARAM_ERROR, "writeDataToTempFileConsumer");
        this.validateParam(recordDTO);
        return DownloadCenterHelper.build(ExecutorUtil.downloadCenterExecutor, recordDTO).asyncWriteWithOssResp(paramOrData, (x) -> {
            // 1、生成临时文件
            String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + UUID.randomUUID() + "_" + recordDTO.getFileName();
            File tempFile = new File(tempFilePath);

            // 2、写入数据
            writeDataToTempFileConsumer.accept(tempFile, x);

            // 3、文件上传至OSS
            OssUploadResult uploadResult;
            try {
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(tempFile), recordDTO.getExpiredDayLabel());
            } catch (Exception e) {
                log.error("下载中心-上传文件到OSS失败，recordDTO:{}", JSON.toJSONString(recordDTO), e);
                throw new ProviderException("下载中心-上传文件到OSS失败");
            }
            // 4、返回OSS文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    @Override
    public <T> Long asyncUploadAndRecordResult(DownloadCenterRecordDTO recordDTO, T paramOrData, Function<T, Workbook> writeDataToTempFileFunction) {
        //参数校验
        TmsAssert.notNull(writeDataToTempFileFunction, ErrorCodeEnum.PARAM_ERROR, "writeDataToTempFileFunction");
        this.validateParam(recordDTO);

        return DownloadCenterHelper.build(ExecutorUtil.downloadCenterExecutor, recordDTO).asyncWriteWithOssResp(paramOrData, (x) -> {
            // 1、生成临时文件
            String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + UUID.randomUUID() + "_" + recordDTO.getFileName();
            File tempFile = new File(tempFilePath);

            // 2、获取业务自定义Workbook
            // 3、Workbook写入临时文件
            // 4、文件上传至OSS
            OssUploadResult uploadResult;
            try (Workbook workbook = writeDataToTempFileFunction.apply(paramOrData);
                 FileOutputStream fos = new FileOutputStream(tempFile)) {
                if (workbook != null){
                    workbook.write(fos);
                }
                uploadResult = OssUploadUtil.upload(recordDTO.getFileName(), FileUtils.openInputStream(tempFile), recordDTO.getExpiredDayLabel());
            }catch (Exception e) {
                log.error("下载中心-上传文件到OSS失败，recordDTO:{}", JSON.toJSONString(recordDTO), e);
                throw new ProviderException("下载中心-上传文件到OSS失败");
            }

            // 5、返回OSS文件地址
            DownloadCenterOssRespDTO downloadCenterOssRespDTO = new DownloadCenterOssRespDTO();
            downloadCenterOssRespDTO.setStatus(DownloadCenterEnum.Status.UPLOADED);
            downloadCenterOssRespDTO.setOssBucketKey(uploadResult.getObjectOssKey());
            return downloadCenterOssRespDTO;
        });
    }

    private void validateParam(DownloadCenterRecordDTO downloadCenterRecordDTO) {
        TmsAssert.notNull(downloadCenterRecordDTO, ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO");
        TmsAssert.notNull(downloadCenterRecordDTO.getSource(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.Source");
        TmsAssert.notNull(downloadCenterRecordDTO.getBizType(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.BizType");
        TmsAssert.notNull(downloadCenterRecordDTO.getTenantId(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.TenantId");
        if (DownloadCenterEnum.RequestSource.XIANMU.equals(downloadCenterRecordDTO.getSource())) {
            TmsAssert.notNull(downloadCenterRecordDTO.getUserId(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.UserId");
        }
        TmsAssert.notEmpty(downloadCenterRecordDTO.getFileName(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.FileName");
        TmsAssert.notNull(downloadCenterRecordDTO.getExpiredDayLabel(), ErrorCodeEnum.PARAM_ERROR, "downloadCenterRecordDTO.ExpiredDayLabel");
    }

}
