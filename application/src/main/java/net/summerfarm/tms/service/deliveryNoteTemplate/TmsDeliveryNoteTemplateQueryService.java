package net.summerfarm.tms.service.deliveryNoteTemplate;


import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.*;

import java.io.IOException;
import java.util.List;

/**
 *
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
public interface TmsDeliveryNoteTemplateQueryService {

    /**
     * @description: 新增
     * @return TmsDeliveryNoteTemplateEntity
     **/
    PageInfo<TmsDeliveryNoteTemplateEntity> getPage(TmsDeliveryNoteTemplateQueryInput input);

    /**
     * @description: 更新
     * @return: java.lang.Boolean
     **/
    TmsDeliveryNoteTemplateEntity getDetail(Long id);

    /**
     * @description: 下载打印配送单
     * @return: java.lang.Long
     **/
    Long downloadPrintDeliveryNoteInfo(DeliveryNotePrintInput input) throws IOException;

    /**
     * @description: 订单维度下载打印订单配送单
     * @return: java.lang.Long
     **/
    Long downloadOrderPrintDeliveryNoteInfo(List<String> orderNos, Boolean printDirection, String downLoadFileName) throws IOException;

    /**
     * @description: 自提订单维度下载打印订单配送单
     * @return: java.lang.Long
     **/
    Long downloadSelfPickupOrderPrintDeliveryNoteInfo(SelfPickupOrderDeliveryNotePrintInput input) throws IOException;
}