package net.summerfarm.tms.service.backdoor.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.client.message.in.DistOrderChangeMessage;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.command.DeliveryBatchCommandDomainService;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.gray.old2new.DistOrderSyncService;
import net.summerfarm.tms.inbound.controller.backdoor.input.DistOrderDeliveryTimeChangeCommandInput;
import net.summerfarm.tms.inbound.controller.backdoor.input.DistOrderUkQueryInput;
import net.summerfarm.tms.local.delivery.DeliveryBatchDistanceHandleService;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.dist.DistOrderMark;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.service.backdoor.BackDoorService;
import net.summerfarm.tms.util.VRPSolverUtil;
import net.xianmu.common.user.UserInfoHolder;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:后门接口实现
 * date: 2024/2/4 18:43
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BackDoorServiceImpl implements BackDoorService {

    private final DistOrderSyncService distOrderSyncService;
    private final DistOrderRepository distorderRepository;
    private final DeliverySiteRepository deliverySiteRepository;
    private final VRPSolverUtil vrpsolverUtil;
    private final DeliveryBatchDistanceHandleService deliveryBatchDistanceHandleService;
    private final DeliveryBatchCommandDomainService deliveryBatchCommandDomainService;
    private final DeliveryBatchRepository deliveryBatchRepository;


    @Override
    public Map<String, String> batchChangeDeliveryTime(DistOrderDeliveryTimeChangeCommandInput input) {
        List<String> normalOuterOrderIds = input.getNormalOuterOrderIds();

        Map<String, String> distResultMap = Maps.newHashMap();
        //鲜沐、SAAS普通订单集合(订单号唯一)
        if (!CollectionUtils.isEmpty(normalOuterOrderIds)){
            List<DistOrderEntity> normalDistOrders = distorderRepository.queryList(DistOrderQuery.builder()
                    .outerOrderIds(normalOuterOrderIds)
                    .sources(DistOrderSourceEnum.getCityCode()).build());
            normalDistOrders = Optional.ofNullable(normalDistOrders).orElse(Lists.newArrayList());

            Map<String/*outerOrderId*/, List<DistOrderEntity>> distOrderMap = normalDistOrders.stream().collect(Collectors.groupingBy(e -> {
                DistClientVO distClientVO = e.getDistClientVO();
                return distClientVO == null ? "" : distClientVO.getOutOrderId();
            }));
            //前置校验普通订单有效性
            normalDistOrders.forEach(e -> {
                String outOrderId = e.getDistClientVO().getOutOrderId();
                List<DistOrderEntity> distOrdersWithSameOrderId =  distOrderMap.get(outOrderId);
                if (CollectionUtils.isEmpty(distOrdersWithSameOrderId)){
                    throw new TmsRuntimeException(String.format("%s,委托单不存在", outOrderId));
                }
                if (distOrdersWithSameOrderId.size() > 1){
                    List<DistOrderMark> distOrderMarks = distOrdersWithSameOrderId.stream().map(DistOrderEntity::getDistOrderMark).collect(Collectors.toList());
                    throw new TmsRuntimeException(String.format("%s,匹配多条委托单,数据:%s", outOrderId, JSON.toJSONString(distOrderMarks)));
                }
            });
            for (DistOrderEntity normalDistOrder : normalDistOrders) {
                String outOrderId = normalDistOrder.getDistClientVO().getOutOrderId();
                try {
                    DistOrderChangeMessage changeMessage = new DistOrderChangeMessage();
                    changeMessage.setSource(normalDistOrder.getSource().getCode());
                    changeMessage.setOuterOrderId(outOrderId);
                    changeMessage.setOuterContactId(normalDistOrder.getDistClientVO().getOutContactId());
                    changeMessage.setExpectBeginTime(normalDistOrder.getDistFlowVO().getExpectBeginTime());

                    changeMessage.setNewExpectBeginTime(input.getNewExpectBeginTime());
                    changeMessage.setUpdater(UserInfoHolder.getUserRealName());
                    changeMessage.setUpdaterId(UserInfoHolder.getUserRealName());
                    distOrderSyncService.changeDistOrder(changeMessage);
                } catch (Exception e){
                    String message = e.getMessage();
                    distResultMap.put(outOrderId, StrUtil.isNotBlank(message) && message.length() > 100 ? message.substring(0, 100) : message);
                }

            }
        }

        //其他订单集合(订单号不唯一)
        List<DistOrderUkQueryInput> otherDistOrders = input.getOtherDistOrders();
        if (!CollectionUtils.isEmpty(otherDistOrders)){
            //由于需要改至同一配送日期 前置校验出省心送订单单号、地址ID一致，配送日期不一致的情况 避免违反UK
            List<DistOrderUkQueryInput> timingDistOrders = otherDistOrders.stream().filter(e -> Objects.equals(e.getSource(), DistOrderSourceEnum.XM_MALL_TIMING.getCode())).collect(Collectors.toList());
            //进行省心送订单单号、地址ID一致 去重
            Map<String/*outerOrderId#outerContactId*/, Long/*count*/> timingDistOrderMap = timingDistOrders.stream().collect(Collectors.groupingBy(e -> e.getOuterOrderId() + Constants.Symbol.POUND_SIGN + e.getOuterContactId(), Collectors.counting()));
            timingDistOrders.forEach(e -> {
                Long count = timingDistOrderMap.get(e.getOuterOrderId() + Constants.Symbol.POUND_SIGN + e.getOuterContactId());
                if (count != null && count > 1){
                    throw new TmsRuntimeException(String.format("%s,存在多个省心送订单单号、地址ID一致的记录，请调整后再进行修改", e.getOuterOrderId()));
                }
            });

            for (DistOrderUkQueryInput otherDistOrder : otherDistOrders) {
                try {
                    DistOrderChangeMessage changeMessage = new DistOrderChangeMessage();
                    changeMessage.setSource(otherDistOrder.getSource());
                    changeMessage.setOuterOrderId(otherDistOrder.getOuterOrderId());
                    changeMessage.setOuterContactId(otherDistOrder.getOuterContactId());
                    changeMessage.setExpectBeginTime(otherDistOrder.getExpectBeginTime());

                    changeMessage.setNewExpectBeginTime(input.getNewExpectBeginTime());
                    changeMessage.setUpdater(UserInfoHolder.getUserRealName());
                    changeMessage.setUpdaterId(UserInfoHolder.getUserRealName());
                    distOrderSyncService.changeDistOrder(changeMessage);
                }catch (Exception e){
                    String message = e.getMessage();
                    distResultMap.put(JSON.toJSONString(otherDistOrder), StrUtil.isNotBlank(message) && message.length() > 100 ? message.substring(0, 100) : message);
                }
            }
        }
        log.info("批量更新委托单配送时间操作失败结果:{}", JSON.toJSONString(distResultMap));
        return distResultMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDeliverySiteSequenceByOrTools(List<Long> batchIds) {
        if(CollectionUtils.isEmpty(batchIds)){
            return;
        }
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder().batchIdList(batchIds).build());
        if(CollectionUtils.isEmpty(deliverySiteEntities)){
            return;
        }
        Map<Long, List<DeliverySiteEntity>> batchId2DeliverySitesMap = deliverySiteEntities.stream().collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));

        List<DeliverySiteEntity> needUpdateDeliverySites = new ArrayList<>();
        batchId2DeliverySitesMap.forEach((batchId, deliverySites) -> {
            deliverySites.sort(Comparator.comparing(DeliverySiteEntity::getSequence));

            List<Long> deliverySiteIdList = new ArrayList<>();
            List<String> poiList = new ArrayList<>();
            for (DeliverySiteEntity deliverySite : deliverySites) {
                deliverySiteIdList.add(deliverySite.getId());
                poiList.add(deliverySite.getSiteEntity().getPoi());
            }

            Map<Long, DeliverySiteEntity> id2DeliverySiteMap = deliverySites.stream().collect(Collectors.toMap(DeliverySiteEntity::getId, Function.identity()));
            // 根据batchId分组计算
            List<Long> deliverySiteIdSequenceList = vrpsolverUtil.solve(convertToDoubleArray(poiList), deliverySiteIdList, deliverySiteIdList.get(0));

            for (int i = 0; i < deliverySiteIdSequenceList.size(); i++) {
                DeliverySiteEntity deliverySiteEntity = id2DeliverySiteMap.get(deliverySiteIdSequenceList.get(i));
                if(deliverySiteEntity == null){
                    continue;
                }
                deliverySiteEntity.setAntSequence(i);
                needUpdateDeliverySites.add(deliverySiteEntity);
            }
        });

        // 更新
        deliverySiteRepository.batchUpdateAntSequences(needUpdateDeliverySites);
    }

    private List<double[]> convertToDoubleArray(List<String> poi) {
        return poi.stream()
                .map(s -> {
                    String[] parts = s.split(",");
                    return new double[]{
                            Double.parseDouble(parts[0].trim()), // 经度
                            Double.parseDouble(parts[1].trim())  // 纬度
                    };
                })
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateDeliveryBatchSequenceByOrTools(Long batchId) {
        if (batchId == null) {
            return;
        }
        DeliveryBatchEntity currentBatch = deliveryBatchRepository.query(batchId);
        if(currentBatch == null){
            return;
        }
        Long deliveryBatchId = currentBatch.getId();
        List<WaypointsInput> waypointsInputList = deliveryBatchDistanceHandleService.buildCompleteWaypointsInputList(currentBatch);
        List<Long> siteIdList = waypointsInputList.stream().map(WaypointsInput::getSiteId).collect(Collectors.toList());
        deliveryBatchCommandDomainService.orToolsCalcDistance(deliveryBatchId,siteIdList);
    }
}
