package net.summerfarm.tms.service.deliveryNoteTemplate.handler;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class DeliveryNoteExcelMerger {

    public static Pattern pattern = Pattern.compile("^([A-Z]+[0-9]*-\\d+)");

    /**
     * 合并多个Excel文件，跳过每个文件的第一个Sheet页
     *
     * @param excelFilePaths 要合并的Excel文件路径列表
     * @param outputFilePath 输出文件的路径
     * @throws IOException 如果文件操作发生错误
     */
    public static void mergeExcelFiles(List<String> excelFilePaths, String outputFilePath) throws IOException {
        try (Workbook mergedWorkbook = new XSSFWorkbook()) {
            Map<FontKey, Font> fontCache = new HashMap<>();
            Map<CellStyleKey, CellStyle> styleCache = new HashMap<>();

            for (String filePath : excelFilePaths) {
                processExcelFile(filePath, mergedWorkbook, fontCache, styleCache);
            }

            // 添加自定义排序逻辑
            sortSheetsWithCustomRule(mergedWorkbook);

            try (FileOutputStream outputStream = new FileOutputStream(outputFilePath)) {
                mergedWorkbook.write(outputStream);
            }
        }
    }

    /**
     * 对Workbook中的Sheet进行排序
     * @param workbook Workbook对象
     */
    public static void sortSheetsWithCustomRule(Workbook workbook) {
        // 获取所有Sheet名称
        List<String> sheetNames = new ArrayList<>();
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            sheetNames.add(workbook.getSheetName(i));
        }

        // 分类Sheet
        List<String> nonMatchingSheets = new ArrayList<>();
        List<String> matchingSheets = new ArrayList<>();

        for (String sheetName : sheetNames) {
            if (pattern.matcher(sheetName).find()) {
                matchingSheets.add(sheetName);
            } else {
                nonMatchingSheets.add(sheetName);
            }
        }

        // 对两组Sheet分别排序
        nonMatchingSheets.sort(String::compareTo);
        matchingSheets.sort(new CustomComparator());

        // 合并排序后的Sheet名称
        List<String> sortedSheetNames = new ArrayList<>();
        sortedSheetNames.addAll(nonMatchingSheets);
        sortedSheetNames.addAll(matchingSheets);

        // 调整Sheet顺序
        for (int i = 0; i < sortedSheetNames.size(); i++) {
            workbook.setSheetOrder(sortedSheetNames.get(i), i);
        }
    }

    static class CustomComparator implements Comparator<String> {
        @Override
        public int compare(String s1, String s2) {
            // 按照“-”分割字符串
            String[] parts1 = s1.split("-", 2); // 限制分割次数为2
            String[] parts2 = s2.split("-", 2);

            // 比较前缀部分
            int prefixCompare = parts1[0].compareTo(parts2[0]);
            if (prefixCompare != 0) {
                return prefixCompare;
            }

            // 提取数字部分并比较
            try {
                // 提取完整的数字部分（可能出现在第二个部分的任意位置）
                String numPart1 = extractNumber(parts1[1]);
                String numPart2 = extractNumber(parts2[1]);

                int num1 = Integer.parseInt(numPart1);
                int num2 = Integer.parseInt(numPart2);
                return Integer.compare(num1, num2);
            } catch (NumberFormatException e) {
                // 如果没有数字部分，按照原始字符串比较
                return s1.compareTo(s2);
            }
        }

        // 提取字符串中的第一个数字部分
        private String extractNumber(String str) {
            StringBuilder sb = new StringBuilder();
            for (char c : str.toCharArray()) {
                if (Character.isDigit(c)) {
                    sb.append(c);
                } else if (sb.length() > 0) {
                    // 如果已经提取了数字，遇到非数字字符则停止
                    break;
                }
            }
            return sb.toString();
        }
    }

    /**
     * 处理单个Excel文件：跳过第一个Sheet页，复制剩余的Sheet页到目标Workbook
     *
     * @param filePath       当前文件路径
     * @param mergedWorkbook 合并的目标Workbook
     * @param fontCache      字体缓存
     * @param styleCache     样式缓存
     * @throws IOException 如果文件操作发生错误
     */
    public static void processExcelFile(String filePath, Workbook mergedWorkbook,
                                        Map<FontKey, Font> fontCache,
                                        Map<CellStyleKey, CellStyle> styleCache) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            log.info("File not found: " + filePath);
            return;
        }

        log.info("当前已执行到文件:{}",file.getName());
        try (FileInputStream fis = new FileInputStream(file)) {
            Workbook workbook = WorkbookFactory.create(fis);// NOSONAR

            // 跳过第一个Sheet页
            if (workbook.getNumberOfSheets() > 1) {
                for (int i = 1; i < workbook.getNumberOfSheets(); i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    String originalName = sheet.getSheetName();
                    String uniqueName = generateUniqueName(mergedWorkbook, originalName);
                    Sheet newSheet = mergedWorkbook.createSheet(uniqueName);
                    copySheet(sheet, newSheet, workbook, mergedWorkbook, fontCache, styleCache);
                }
            } else {
                log.info("File " + filePath + " has only one sheet. Skipping.");
            }
        }
    }

    public static String generateUniqueName(Workbook workbook, String baseName) {
        String newName = baseName;
        int counter = 1;
        while (workbook.getSheet(newName) != null) {
            newName = baseName + "_" + counter++;
        }
        return newName;
    }

    /**
     * 复制Sheet内容、样式、合并区域、列宽和行高
     *
     * @param oldSheet       源Sheet
     * @param newSheet       目标Sheet
     * @param oldWorkbook    源Workbook
     * @param newWorkbook    目标Workbook
     * @param fontCache      字体缓存
     * @param styleCache     样式缓存
     */
    public static void copySheet(Sheet oldSheet, Sheet newSheet, Workbook oldWorkbook, Workbook newWorkbook,
                                 Map<FontKey, Font> fontCache,
                                 Map<CellStyleKey, CellStyle> styleCache) {
        // 复制行和单元格
        for (Row oldRow : oldSheet) {
            Row newRow = newSheet.createRow(oldRow.getRowNum());
            newRow.setHeight(oldRow.getHeight()); // 复制行高

            for (Cell oldCell : oldRow) {
                Cell newCell = newRow.createCell(oldCell.getColumnIndex());
                copyCell(oldCell, newCell, oldWorkbook, newWorkbook, fontCache, styleCache);
            }
        }

        // 复制列宽：计算最大列索引
        int maxColumnIndex = 0;
        for (Row row : oldSheet) {
            if (row != null) {
                int lastCellNum = row.getLastCellNum();
                if (lastCellNum > maxColumnIndex) {
                    maxColumnIndex = lastCellNum;
                }
            }
        }
        // 检查合并区域的列范围
        for (int i = 0; i < oldSheet.getNumMergedRegions(); i++) {
            CellRangeAddress region = oldSheet.getMergedRegion(i);
            int lastColumn = region.getLastColumn();
            if (lastColumn > maxColumnIndex) {
                maxColumnIndex = lastColumn;
            }
        }
        // 复制列宽
        for (int i = 0; i <= maxColumnIndex; i++) {
            newSheet.setColumnWidth(i, oldSheet.getColumnWidth(i));
        }

        // 复制合并区域
        for (int i = 0; i < oldSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = oldSheet.getMergedRegion(i);
            newSheet.addMergedRegion(mergedRegion);
        }

        // 复制打印配置
        copyPrintSetup(oldSheet, newSheet);
    }

    /**
     * 复制打印配置
     *
     * @param oldSheet 源Sheet
     * @param newSheet 目标Sheet
     */
    public static void copyPrintSetup(Sheet oldSheet, Sheet newSheet) {
        // 复制打印标题行和列
        newSheet.setRepeatingRows(oldSheet.getRepeatingRows());
        newSheet.setRepeatingColumns(oldSheet.getRepeatingColumns());

        // 复制打印设置
        PrintSetup oldPrintSetup = oldSheet.getPrintSetup();
        PrintSetup newPrintSetup = newSheet.getPrintSetup();
        newPrintSetup.setPaperSize(oldPrintSetup.getPaperSize());
        newPrintSetup.setScale(oldPrintSetup.getScale());
        newPrintSetup.setLandscape(oldPrintSetup.getLandscape());
    }

    /**
     * 复制单元格内容和样式
     *
     * @param oldCell        源单元格
     * @param newCell        目标单元格
     * @param oldWorkbook    源Workbook
     * @param newWorkbook    目标Workbook
     * @param fontCache      字体缓存
     * @param styleCache     样式缓存
     */
    public static void copyCell(Cell oldCell, Cell newCell, Workbook oldWorkbook, Workbook newWorkbook,
                                Map<FontKey, Font> fontCache,
                                Map<CellStyleKey, CellStyle> styleCache) {
        // 复制单元格内容
        switch (oldCell.getCellType()) {
            case STRING:
                newCell.setCellValue(oldCell.getStringCellValue());
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(oldCell)) {
                    newCell.setCellValue(oldCell.getDateCellValue());
                } else {
                    newCell.setCellValue(oldCell.getNumericCellValue());
                }
                break;
            case BOOLEAN:
                newCell.setCellValue(oldCell.getBooleanCellValue());
                break;
            case FORMULA:
                newCell.setCellFormula(oldCell.getCellFormula());
                break;
            case BLANK:
                newCell.setBlank();
                break;
            case ERROR:
                newCell.setCellErrorValue(oldCell.getErrorCellValue());
                break;
        }

        // 复制单元格样式
        if (oldCell.getCellStyle() != null) {
            CellStyle oldStyle = oldCell.getCellStyle();
            Font oldFont = oldWorkbook.getFontAt(oldStyle.getFontIndex());

            // 生成字体键并获取/创建新字体
            FontKey fontKey = new FontKey(oldFont);
            Font newFont = fontCache.computeIfAbsent(fontKey, k -> createFont(newWorkbook, oldFont));

            // 生成样式键并获取/创建新样式
            CellStyleKey styleKey = new CellStyleKey(oldStyle, fontKey);
            CellStyle newStyle = styleCache.computeIfAbsent(styleKey, k -> createCellStyle(newWorkbook, oldStyle, newFont));

            newCell.setCellStyle(newStyle);
        }
    }

    private static Font createFont(Workbook workbook, Font oldFont) {
        Font newFont = workbook.createFont();
        newFont.setFontName(oldFont.getFontName());
        newFont.setFontHeight(oldFont.getFontHeight());
        newFont.setBold(oldFont.getBold());
        newFont.setItalic(oldFont.getItalic());
        newFont.setUnderline(oldFont.getUnderline());
        newFont.setStrikeout(oldFont.getStrikeout());
        newFont.setColor(oldFont.getColor());
        return newFont;
    }

    private static CellStyle createCellStyle(Workbook workbook, CellStyle oldStyle, Font font) {
        CellStyle newStyle = workbook.createCellStyle();
        newStyle.setFont(font);
        newStyle.setAlignment(oldStyle.getAlignment());
        newStyle.setVerticalAlignment(oldStyle.getVerticalAlignment());
        newStyle.setBorderTop(oldStyle.getBorderTop());
        newStyle.setBorderBottom(oldStyle.getBorderBottom());
        newStyle.setBorderLeft(oldStyle.getBorderLeft());
        newStyle.setBorderRight(oldStyle.getBorderRight());
        newStyle.setTopBorderColor(oldStyle.getTopBorderColor());
        newStyle.setBottomBorderColor(oldStyle.getBottomBorderColor());
        newStyle.setLeftBorderColor(oldStyle.getLeftBorderColor());
        newStyle.setRightBorderColor(oldStyle.getRightBorderColor());
        newStyle.setFillPattern(oldStyle.getFillPattern());
        newStyle.setFillForegroundColor(oldStyle.getFillForegroundColor());
        newStyle.setFillBackgroundColor(oldStyle.getFillBackgroundColor());
        newStyle.setDataFormat(workbook.createDataFormat().getFormat(oldStyle.getDataFormatString()));
        newStyle.setWrapText(oldStyle.getWrapText());
        newStyle.setIndention(oldStyle.getIndention());
        newStyle.setRotation(oldStyle.getRotation());
        return newStyle;
    }

    // Font键类
    private static class FontKey {
        private final String fontName;
        private final short fontHeight;
        private final boolean bold;
        private final boolean italic;
        private final byte underline;
        private final boolean strikeout;
        private final short color;

        FontKey(Font font) {
            this.fontName = font.getFontName();
            this.fontHeight = font.getFontHeight();
            this.bold = font.getBold();
            this.italic = font.getItalic();
            this.underline = font.getUnderline();
            this.strikeout = font.getStrikeout();
            this.color = font.getColor();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            FontKey fontKey = (FontKey) o;
            return fontHeight == fontKey.fontHeight &&
                    bold == fontKey.bold &&
                    italic == fontKey.italic &&
                    underline == fontKey.underline &&
                    strikeout == fontKey.strikeout &&
                    color == fontKey.color &&
                    Objects.equals(fontName, fontKey.fontName);
        }

        @Override
        public int hashCode() {
            return Objects.hash(fontName, fontHeight, bold, italic, underline, strikeout, color);
        }
    }

    // CellStyle键类
    private static class CellStyleKey {
        private final HorizontalAlignment alignment;
        private final VerticalAlignment verticalAlignment;
        private final BorderStyle borderTop;
        private final BorderStyle borderBottom;
        private final BorderStyle borderLeft;
        private final BorderStyle borderRight;
        private final short topBorderColor;
        private final short bottomBorderColor;
        private final short leftBorderColor;
        private final short rightBorderColor;
        private final FillPatternType fillPattern;
        private final short fillForegroundColor;
        private final short fillBackgroundColor;
        private final FontKey fontKey;
        private final String dataFormat;
        private final boolean wrapText;
        private final short indention;
        private final short rotation;

        CellStyleKey(CellStyle style, FontKey fontKey) {
            this.alignment = style.getAlignment();
            this.verticalAlignment = style.getVerticalAlignment();
            this.borderTop = style.getBorderTop();
            this.borderBottom = style.getBorderBottom();
            this.borderLeft = style.getBorderLeft();
            this.borderRight = style.getBorderRight();
            this.topBorderColor = style.getTopBorderColor();
            this.bottomBorderColor = style.getBottomBorderColor();
            this.leftBorderColor = style.getLeftBorderColor();
            this.rightBorderColor = style.getRightBorderColor();
            this.fillPattern = style.getFillPattern();
            this.fillForegroundColor = style.getFillForegroundColor();
            this.fillBackgroundColor = style.getFillBackgroundColor();
            this.fontKey = fontKey;
            this.dataFormat = style.getDataFormatString();
            this.wrapText = style.getWrapText();
            this.indention = style.getIndention();
            this.rotation = style.getRotation();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            CellStyleKey that = (CellStyleKey) o;
            return alignment == that.alignment &&
                    verticalAlignment == that.verticalAlignment &&
                    borderTop == that.borderTop &&
                    borderBottom == that.borderBottom &&
                    borderLeft == that.borderLeft &&
                    borderRight == that.borderRight &&
                    topBorderColor == that.topBorderColor &&
                    bottomBorderColor == that.bottomBorderColor &&
                    leftBorderColor == that.leftBorderColor &&
                    rightBorderColor == that.rightBorderColor &&
                    fillPattern == that.fillPattern &&
                    fillForegroundColor == that.fillForegroundColor &&
                    fillBackgroundColor == that.fillBackgroundColor &&
                    wrapText == that.wrapText &&
                    indention == that.indention &&
                    rotation == that.rotation &&
                    Objects.equals(fontKey, that.fontKey) &&
                    Objects.equals(dataFormat, that.dataFormat);
        }

        @Override
        public int hashCode() {
            return Objects.hash(alignment, verticalAlignment, borderTop, borderBottom, borderLeft, borderRight,
                    topBorderColor, bottomBorderColor, leftBorderColor, rightBorderColor, fillPattern,
                    fillForegroundColor, fillBackgroundColor, fontKey, dataFormat, wrapText, indention, rotation);
        }
    }
}