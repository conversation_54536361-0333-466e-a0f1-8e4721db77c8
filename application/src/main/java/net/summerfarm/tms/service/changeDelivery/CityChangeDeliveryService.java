package net.summerfarm.tms.service.changeDelivery;

import net.summerfarm.tms.inbound.controller.delivery.input.command.RouteReassignmentDriverCommandInput;
import net.summerfarm.tms.inbound.controller.delivery.input.command.SiteReassignmentRouteCommand;

/**
 * Description: 城配改派<br/>
 * date: 2025/4/25 16:42<br/>
 *
 * <AUTHOR> />
 */
public interface CityChangeDeliveryService {

    /**
     * 路线改派司机
     * @param input 入参
     */
    void routeReassignmentDriver(RouteReassignmentDriverCommandInput input);

    /**
     * 站点改派路线
     * @param input 入参
     */
    void siteReassignmentRoute(SiteReassignmentRouteCommand input);
}
