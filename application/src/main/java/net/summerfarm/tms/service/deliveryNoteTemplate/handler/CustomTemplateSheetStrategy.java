package net.summerfarm.tms.service.deliveryNoteTemplate.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import net.summerfarm.contexts.RegConstant;
import org.apache.poi.ss.usermodel.Workbook;

public class CustomTemplateSheetStrategy implements SheetWriteHandler {
    private Integer sheetNo;
    private String sheetName;

    public CustomTemplateSheetStrategy(String sheetName) {
        this.sheetName = sheetName;
    }

    public CustomTemplateSheetStrategy(Integer sheetNo, String sheetName) {
        this.sheetNo = sheetNo;
        this.sheetName = sheetName;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        // 在创建 Sheet 之前的操作（通常不需要实现）
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (sheetName == null) {
            return;
        }
        if (sheetNo == null) {
            sheetNo = 0;
        }
        // 特殊字符处理替换
        sheetName = sheetName.replaceAll(RegConstant.SHEET_REGEX, "");
        // 非法字符处理
        sheetName = sheetName.replaceAll("[\\x00-\\x08\\x0B-\\x0C\\x0E-\\x1F]", "");
        // 动态修改 Sheet 名称
        Workbook cachedWorkbook = writeWorkbookHolder.getCachedWorkbook();
        cachedWorkbook.setSheetName(sheetNo, DeliveryNoteExcelMerger.generateUniqueName(cachedWorkbook, sheetName));
    }
}