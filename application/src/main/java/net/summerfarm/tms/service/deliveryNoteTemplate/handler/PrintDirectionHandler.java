package net.summerfarm.tms.service.deliveryNoteTemplate.handler;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Sheet;

public class PrintDirectionHandler implements SheetWriteHandler {

    private final boolean isLandscape; // 是否横向打印

    public PrintDirectionHandler(boolean isLandscape) {
        this.isLandscape = isLandscape;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        PrintSetup printSetup = sheet.getPrintSetup();
        if (isLandscape) {
            printSetup.setLandscape(false); // 设置纵向打印
            printSetup.setScale((short) 75);// 纵向设置默认75的缩放比例
        } else {
            printSetup.setLandscape(true); // 设置横向打印
        }
    }
}