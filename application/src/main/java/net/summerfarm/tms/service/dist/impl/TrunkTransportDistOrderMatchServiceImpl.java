package net.summerfarm.tms.service.dist.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.path.PathDomainService;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.config.TmsNacosConfig;
import net.summerfarm.tms.delivery.DeliveryBatchDomainService;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliveryOrderRepository;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliveryOrderSaveByDistCommand;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.inbound.converter.delivery.trunk.TrunkDeliveryBatchVOConverter;
import net.summerfarm.tms.local.delivery.converter.DeliveryBatchDTOConverter;
import net.summerfarm.tms.path.TmsPathService;
import net.summerfarm.tms.path.dto.PathCarDTO;
import net.summerfarm.tms.path.dto.TmsPathDTO;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.service.dist.TrunkTransportDistOrderMatchService;
import net.summerfarm.tms.local.delivery.DeliveryBatchServiceImpl;
import net.summerfarm.tms.inbound.controller.delivery.vo.trunk.TrunkDeliveryBatchBySiteVO;
import net.summerfarm.tms.inbound.controller.delivery.vo.trunk.TrunkDeliveryBatchVO;
import net.xianmu.common.exception.BizException;
import net.xianmu.robot.feishu.FeishuBotUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 干线转运委托单匹配服务实现类
 * date: 2025/7/9 18:57<br/>
 *
 * <AUTHOR> />
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class TrunkTransportDistOrderMatchServiceImpl implements TrunkTransportDistOrderMatchService {

    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryBatchServiceImpl deliveryBatchService;
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;
    @Resource
    private DistOrderService distOrderService;
    @Resource
    private PathDomainService pathDomainService;

    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private TmsPathService tmsPathService;
    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private TmsNacosConfig tmsNacosConfig;

    @Override
    public void matchAndCreateBatch() {
        try {
            log.info("开始执行干线转运委托单匹配创建批次任务");

            // 1. 先查询第二天有干线转运的起点站点
            List<Long> trunkTransportBeginSiteIds = this.queryTrunkTransportBeginSites();

            if (CollectionUtils.isEmpty(trunkTransportBeginSiteIds)) {
                log.info("第二天没有干线转运的起点站点");
                return;
            }

            log.info("查询到{}个第二天有干线转运的起点站点", trunkTransportBeginSiteIds.size());

            // 2. 检查这些起点的城配仓截单时间是否已到
            List<Long> eligibleBeginSiteIds = filterSitesByCloseTime(trunkTransportBeginSiteIds);

            if (CollectionUtils.isEmpty(eligibleBeginSiteIds)) {
                log.info("没有城配仓已过截单时间");
                return;
            }

            log.info("其中{}个城配仓已过截单时间", eligibleBeginSiteIds.size());

            // 3. 循环处理每个符合条件的起点站点，避免一次性查询太多数据
            int totalProcessedCount = 0;
            int totalSuccessCount = 0;
            int totalFailedCount = 0;

            for (Long beginSiteId : eligibleBeginSiteIds) {
                try {
                    log.info("开始处理起点站点：{}", beginSiteId);

                    // 查询当前起点站点的干线转运委托单
                    List<DistOrderEntity> siteDistOrders = this.queryTrunkTransportDistOrdersByBeginSite(beginSiteId);

                    if (CollectionUtils.isEmpty(siteDistOrders)) {
                        log.info("起点站点{}未查询到需要处理的干线转运委托单", beginSiteId);
                        continue;
                    }

                    log.info("起点站点{}查询到{}条干线转运委托单需要处理", beginSiteId, siteDistOrders.size());

                    // 处理当前起点站点的委托单
                    int siteProcessedCount = 0;
                    int siteSuccessCount = 0;
                    int siteFailedCount = 0;
                    TrunkTransportDistOrderMatchServiceImpl trunkTransportDistOrderMatchService = applicationContext.getBean(TrunkTransportDistOrderMatchServiceImpl.class);

                    for (DistOrderEntity distOrder : siteDistOrders) {
                        try {
                            siteProcessedCount++;
                            boolean result = trunkTransportDistOrderMatchService.processDistOrder(distOrder);
                            if (result) {
                                siteSuccessCount++;
                            } else {
                                siteFailedCount++;
                            }
                        } catch (Exception e) {
                            siteFailedCount++;
                            log.error("处理委托单{}时发生异常", distOrder.getDistId(), e);
                        }
                    }

                    // 累计统计
                    totalProcessedCount += siteProcessedCount;
                    totalSuccessCount += siteSuccessCount;
                    totalFailedCount += siteFailedCount;

                    log.info("起点站点{}处理完成，处理{}条，成功{}条，失败{}条",
                            beginSiteId, siteProcessedCount, siteSuccessCount, siteFailedCount);

                } catch (Exception e) {
                    log.error("处理起点站点{}时发生异常", beginSiteId, e);
                }
            }

            log.info("干线转运委托单匹配创建批次任务执行完成，总计处理{}条，成功{}条，失败{}条",
                    totalProcessedCount, totalSuccessCount, totalFailedCount);

        } catch (Exception e) {
            log.error("干线转运委托单匹配创建批次任务执行异常", e);
        }

    }

    /**
     * 查询第二天有干线转运的起点站点
     *
     * @return 有干线转运的起点站点ID列表
     */
    private List<Long> queryTrunkTransportBeginSites() {
        try {
            // 配送日期第二天
            LocalDateTime nextDay = LocalDate.now().plusDays(1).atStartOfDay();

            // 指定的source城配来源
            List<Integer> targetSources = DistOrderSourceEnum.getCityCode();

            // 查询第二天有干线转运的委托单的起点站点（去重）
            List<Long> beginSiteIds = distOrderRepository.queryTrunkTransportBeginSites(nextDay, targetSources, DistOrderStatusEnum.TO_BE_WIRED.getCode());

            log.info("第二天有干线转运委托单，涉及{}个起点站点", beginSiteIds.size());
            return beginSiteIds;

        } catch (Exception e) {
            log.error("查询第二天有干线转运的起点站点时发生异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据截单时间过滤起点站点
     *
     * @param beginSiteIds 起点站点ID列表
     * @return 已过截单时间的起点站点ID列表
     */
    private List<Long> filterSitesByCloseTime(List<Long> beginSiteIds) {
        try {
            // 配送日期第二天
            LocalDate nextDay = LocalDate.now().plusDays(1);

            // 查询站点信息
            Map<Long, SiteEntity> siteMap = siteRepository.queryMapByIds(beginSiteIds);

            List<Long> eligibleSiteIds = new ArrayList<>();

            for (Long siteId : beginSiteIds) {
                SiteEntity site = siteMap.get(siteId);
                if (site == null) {
                    log.warn("站点{}不存在", siteId);
                    continue;
                }

                try {
                    // 直接使用DeliveryBatchServiceImpl的方法检查截单时间
                    if (deliveryBatchService.isAfterCloseTime(Integer.parseInt(site.getOutBusinessNo()), nextDay)) {
                        eligibleSiteIds.add(siteId);
                        log.debug("城配仓{}（站点ID：{}）已过截单时间，加入处理列表", site.getOutBusinessNo(), siteId);
                    } else {
                        log.debug("城配仓{}（站点ID：{}）未过截单时间，跳过处理", site.getOutBusinessNo(), siteId);
                    }
                } catch (Exception e) {
                    log.error("检查城配仓{}截单时间时发生异常", site.getOutBusinessNo(), e);
                }
            }

            log.info("共检查{}个有干线转运的城配仓，其中{}个已过截单时间", beginSiteIds.size(), eligibleSiteIds.size());
            return eligibleSiteIds;

        } catch (Exception e) {
            log.error("根据截单时间过滤起点站点时发生异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据单个起点站点查询干线转运委托单
     *
     * @param beginSiteId 起点站点ID
     * @return 干线转运委托单列表
     */
    private List<DistOrderEntity> queryTrunkTransportDistOrdersByBeginSite(Long beginSiteId) {
        // 配送日期第二天
        LocalDateTime nextDay = LocalDate.now().plusDays(1).atStartOfDay();

        // 指定的source来源
        List<Integer> targetSources = DistOrderSourceEnum.getCityCode();

        log.debug("查询起点站点{}的干线转运委托单，配送日期：{}，来源：{}，履约配送方式：4",
                beginSiteId, nextDay, targetSources);

        return distOrderRepository.queryList(DistOrderQuery.builder()
                .sources(targetSources)  // source来源
                .fulfillmentDeliveryWays(Collections.singletonList(DistOrderFulfillmentDeliveryWayEnum.TRUNK_TRANS.getCode()))  // fulfillment_delivery_way是4（干线转运）
                .expectBeginTime(nextDay)  // 配送日期第二天
                .beginSiteId(beginSiteId)  // 单个起点站点
                .status(Collections.singletonList(DistOrderStatusEnum.TO_BE_WIRED.getCode()))
                .build());
    }

    /**
     * 处理单个委托单（核心业务逻辑）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean processDistOrder(DistOrderEntity distOrder) {
        try {
            log.info("开始处理委托单：{}，外部订单号：{}，起点：{}",
                    distOrder.getDistId(),
                    distOrder.getDistClientVO().getOutOrderId(),
                    distOrder.getBeginSite().getId());

            // 3. 检查是否存在对应起点、终点大客户用车调度单
            List<TrunkDeliveryBatchBySiteVO> existingBigCustomerBatches = this.findExistingBigCustomerBatches(distOrder);

            if (!CollectionUtils.isEmpty(existingBigCustomerBatches)) {
                // 直接绑定到调度单
                return bindToExistingBatch(distOrder, existingBigCustomerBatches);
            }

            // 4. 不存在则匹配路由创建干线调度单
            return matchRouteAndCreateBatch(distOrder);

        } catch (Exception e) {
            log.error("处理委托单{}异常", distOrder.getDistId(), e);
            return false;
        }
    }

    /**
     * 查找现有的大客户用车调度单
     * 调用TrunkDeliveryBatchController#listBySite方法
     */
    private List<TrunkDeliveryBatchBySiteVO> findExistingBigCustomerBatches(DistOrderEntity distOrder) {
        try {
            // 查询配送单以及站点信息
            List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryListWithSiteName(DeliveryOrderQuery.builder().distOrderId(distOrder.getDistId()).build());

            // 查询调度单以及站点信息
            DeliveryBatchQuery deliveryBatchQuery = new DeliveryBatchQuery();
            deliveryBatchQuery.setDeliveryBatchType(DeliveryBatchTypeSourceEnum.sourceTypeMap.get(distOrder.getSource().getCode()));
            deliveryBatchQuery.setDeliveryTime(distOrder.getDistFlowVO().getExpectBeginTime());
            deliveryBatchQuery.setDeliveryBatchStatusList(Lists.newArrayList(
                    DeliveryBatchStatusEnum.TO_BE_WIRED.getCode(),
                    DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(),
                    DeliveryBatchStatusEnum.IN_DELIVERY.getCode()
            ));
            deliveryBatchQuery.setPageSize(400);
            PageInfo<DeliveryBatchEntity> deliveryBatchEntityPageInfo = deliveryBatchDomainService.queryListWithSite(deliveryBatchQuery);

            if (deliveryBatchEntityPageInfo == null || CollectionUtils.isEmpty(deliveryBatchEntityPageInfo.getList())) {
                log.debug("未找到现有的大客户用车调度单，委托单：{}", distOrder.getDistId());
                return Collections.emptyList();
            }

            List<DeliveryBatchDTO> batchDTOList = deliveryBatchEntityPageInfo.getList().stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList());

            List<TrunkDeliveryBatchBySiteVO> trunkDeliveryBatchBySiteVOList = new ArrayList<>();
            // 查询匹配的调度单
            for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntities) {
                Long beginSiteId = deliveryOrderEntity.getBeginSiteId();
                Long endSiteId = deliveryOrderEntity.getEndSiteId();
                TmsResult<List<DeliveryBatchDTO>> matchDeliveryBatch = deliveryBatchService.matchDeliveryBatch(batchDTOList,Arrays.asList(beginSiteId,endSiteId));

                if(CollectionUtils.isEmpty(matchDeliveryBatch.getData())){
                    log.info("未找到匹配的调度单，委托单：{}", distOrder.getDistId());
                    return Collections.emptyList();
                }

                List<TrunkDeliveryBatchVO> trunkDeliveryBatchVOList = matchDeliveryBatch.getData().stream()
                        .map(TrunkDeliveryBatchVOConverter::dto2Vo)
                        .collect(Collectors.toList());
                // 匹配的数据
                TrunkDeliveryBatchBySiteVO trunkDeliveryBatchBySiteVO = new TrunkDeliveryBatchBySiteVO();
                trunkDeliveryBatchBySiteVO.setBatchList(trunkDeliveryBatchVOList);
                trunkDeliveryBatchBySiteVO.setBeginSiteId(beginSiteId);
                trunkDeliveryBatchBySiteVO.setEndSiteId(endSiteId);
                trunkDeliveryBatchBySiteVO.setDistOrderId(distOrder.getDistId());

                log.debug("找到可用的大客户用车调度单Id:{}，委托单：{}",trunkDeliveryBatchVOList.stream().map(e -> String.valueOf(e.getBatchId())).collect(Collectors.joining(",")), distOrder.getDistId());

                trunkDeliveryBatchBySiteVOList.add(trunkDeliveryBatchBySiteVO);
            }


            return trunkDeliveryBatchBySiteVOList;

        } catch (Exception e) {
            log.error("查找现有大客户用车调度单时发生异常，委托单：{}", distOrder.getDistId(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 绑定到现有调度单
     */
    private boolean bindToExistingBatch(DistOrderEntity distOrder, List<TrunkDeliveryBatchBySiteVO> existingBigCustomerBatches) {
        List<Long> batchIds = existingBigCustomerBatches.stream().map(e -> {
            return e.getBatchList().stream().map(TrunkDeliveryBatchVO::getBatchId).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        try {
            log.info("委托单{}绑定到现有调度单{}", distOrder.getDistId(), batchIds);
            return this.bindToTrunkBatchList(distOrder,batchIds);
        } catch (Exception e) {
            log.error("委托单{}绑定到调度单{}异常", distOrder.getDistId(),batchIds, e);
            return false;
        }
    }

    /**
     * 绑定委托单到调度单列表
     * 模仿bindToExistingBatch方法，用于绑定到新创建的调度单
     *
     * @param distOrder 委托单
     * @param batchIds 调度单Id列表
     * @return 绑定结果
     */
    private boolean bindToTrunkBatchList(DistOrderEntity distOrder, List<Long> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            log.warn("调度单Id列表为空，无法绑定委托单：{}", distOrder.getDistId());
            return false;
        }

        try {
            log.info("委托单{}绑定到调度单列表{}", distOrder.getDistId(), batchIds);

            List<DeliveryOrderSaveByDistCommand> deliveryOrderSaveByDistCommandList = buildDeliveryOrderSaveByDistCommands(batchIds,distOrder);

            // 调用服务进行绑定
            distOrderService.deliveryOrderSaveByDist(deliveryOrderSaveByDistCommandList);

            log.info("委托单{}成功绑定到调度单列表{}", distOrder.getDistId(), batchIds);
            return true;

        } catch (Exception e) {
            log.error("委托单{}绑定到调度单列表{}异常", distOrder.getDistId(), batchIds, e);
            return false;
        }
    }

    private static List<DeliveryOrderSaveByDistCommand> buildDeliveryOrderSaveByDistCommands(List<Long> trunkBatchIdList, DistOrderEntity distOrder) {
        // 构造绑定命令列表，模仿bindToExistingBatch的逻辑
        List<DeliveryOrderSaveByDistCommand> deliveryOrderSaveByDistCommandList = new ArrayList<>();

        // 构造调度单DTO列表
        List<DeliveryBatchDTO> batchListDTO = trunkBatchIdList.stream().map(batchId -> {
            DeliveryBatchDTO deliveryBatchDTO = new DeliveryBatchDTO();
            deliveryBatchDTO.setDeliveryBatchId(batchId);
            return deliveryBatchDTO;
        }).collect(Collectors.toList());

        DeliveryOrderSaveByDistCommand deliveryOrderSaveByDistCommand = new DeliveryOrderSaveByDistCommand();
        deliveryOrderSaveByDistCommand.setBatchList(batchListDTO);
        deliveryOrderSaveByDistCommand.setDistOrderId(distOrder.getDistId());
        deliveryOrderSaveByDistCommand.setBeginSiteId(distOrder.getBeginSite().getId());
        deliveryOrderSaveByDistCommand.setEndSiteId(distOrder.getEndSite().getId());
        deliveryOrderSaveByDistCommand.setFulfillmentDeliveryWay(distOrder.getFulfillmentDeliveryWay());

        deliveryOrderSaveByDistCommandList.add(deliveryOrderSaveByDistCommand);
        return deliveryOrderSaveByDistCommandList;
    }

    /**
     * 匹配路由并创建调度单
     */
    private boolean matchRouteAndCreateBatch(DistOrderEntity distOrder) {
        try {
            // 5. 匹配路由
            SiteEntity beginSiteEntity = siteRepository.query(distOrder.getBeginSite().getId());
            SiteEntity endSiteEntity = siteRepository.query(distOrder.getEndSite().getId());
            List<TmsPathEntity> matchPathList = pathDomainService.findPathByBeginEndSite(endSiteEntity, beginSiteEntity);
            if (!CollectionUtils.isEmpty(matchPathList)) {
                matchPathList.sort(Comparator.comparing(TmsPathEntity::getCreateTime));
                TmsPathEntity matchedPath = matchPathList.get(0);
                // 创建干线调度单并进行绑定
                return createTrunkBatchAndBind(distOrder, matchedPath);
            } else {
                // 匹配不到路由，发送飞书告警
                sendNoRouteAlert(distOrder);
                return false;
            }

        } catch (Exception e) {
            log.error("委托单{}匹配路由并创建调度单异常", distOrder.getDistId(), e);
            return false;
        }
    }


    /**
     * 创建干线调度单并绑定
     */
    private boolean createTrunkBatchAndBind(DistOrderEntity distOrder, TmsPathEntity path) {
        try {
            log.info("为委托单{}创建干线调度单，使用路由{}", distOrder.getDistId(), path.getPathId());

            // 创建干线调度单
            List<DeliveryBatchEntity> trunkBatchList = createTrunkDeliveryBatch(distOrder, path);

            if (CollectionUtils.isEmpty(trunkBatchList)) {
                log.warn("创建干线调度单失败，委托单：{}", distOrder.getDistId());
                return false;
            }

            // 绑定委托单到调度单列表
            List<Long> bindBatchIdList = trunkBatchList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
            boolean bindResult = bindToTrunkBatchList(distOrder, bindBatchIdList);

            if (bindResult) {
                List<Long> batchIds = trunkBatchList.stream()
                        .map(DeliveryBatchEntity::getId)
                        .collect(Collectors.toList());
                log.info("委托单{}成功创建并绑定到干线调度单{}", distOrder.getDistId(), batchIds);
                return true;
            } else {
                log.warn("委托单{}创建调度单成功但绑定失败", distOrder.getDistId());
                return false;
            }

        } catch (Exception e) {
            log.error("为委托单{}创建干线调度单异常", distOrder.getDistId(), e);
            return false;
        }
    }

    /**
     * 创建干线配送批次
     */
    private List<DeliveryBatchEntity> createTrunkDeliveryBatch(DistOrderEntity distOrder, TmsPathEntity path) {
        TmsResult<PageInfo<TmsPathDTO>> pageInfoTmsResult = tmsPathService.queryList(PathQuery.builder().pathIdList(Collections.singletonList(path.getPathId())).build());
        if (!pageInfoTmsResult.isSuccess()) {
            log.error("查询path失败:{} {}", pageInfoTmsResult.getErrCode(), pageInfoTmsResult.getErrorMessage());
            throw new BizException("查询path失败");
        }
        List<TmsPathDTO> tmsPathDTOList = pageInfoTmsResult.getData().getList();
        if (CollectionUtils.isEmpty(tmsPathDTOList)) {
            return null;
        }
        TmsPathDTO tmsPathDTO = tmsPathDTOList.get(0);
        List<PathCarDTO> pathCarList = tmsPathDTO.getPathCarList();
        if (CollectionUtils.isEmpty(pathCarList)) {
            return null;
        }

        List<Long> matchBatchCreateIdList = new ArrayList<>();

        for (PathCarDTO pathCarDTO : pathCarList) {
            TmsResult<DeliveryBatchEntity> deliveryBatchByPathResult = tmsPathService.createDeliveryBatchByPath(tmsPathDTO, distOrder.getDistFlowVO().getExpectBeginTime().toLocalDate(), pathCarDTO);
            if (deliveryBatchByPathResult.isSuccess() && deliveryBatchByPathResult.getData() != null) {
                matchBatchCreateIdList.add(deliveryBatchByPathResult.getData().getId());
            }
        }

        List<DeliveryBatchEntity> matchBatchCreateList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(matchBatchCreateIdList)) {
            matchBatchCreateList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder().batchIds(matchBatchCreateIdList).build());
        }

        return matchBatchCreateList;
    }

    /**
     * 发送无路由告警
     */
    private void sendNoRouteAlert(DistOrderEntity distOrder) {
        try {
            String lineSeparator = System.lineSeparator();
            StringBuilder textBuild = new StringBuilder();
            textBuild.append("🚨 **委托单匹配干线转运路由告警** 🚨").append(lineSeparator).append(lineSeparator);
            textBuild.append("**委托单ID：**").append(distOrder.getDistId()).append(lineSeparator);
            textBuild.append("**外部订单号：**").append(distOrder.getDistClientVO().getOutOrderId()).append(lineSeparator);
            textBuild.append("**配送日期：**").append(distOrder.getDistFlowVO().getExpectBeginTime()).append(lineSeparator);
            textBuild.append("**告警原因：**").append("未找到匹配的干线路由").append(lineSeparator);

            // 发送飞书告警
            String trunkTransportDistNoRouteRobotUrl = tmsNacosConfig.getTrunkTransportDistNoRouteRobotUrl();
            // 发送飞书告警
            log.warn("发送飞书告警：{},robotUrl:{}", textBuild,trunkTransportDistNoRouteRobotUrl);
            if (StringUtils.isNotBlank(trunkTransportDistNoRouteRobotUrl)) {
                FeishuBotUtil.sendMarkdownMsgAndAtAll(trunkTransportDistNoRouteRobotUrl, textBuild.toString());
            }

        } catch (Exception e) {
            log.error("发送无路由告警异常，委托单：{}", distOrder.getDistId(), e);
        }
    }
}
