package net.summerfarm.tms.service.delivery.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.ContactAdjustRepository;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.ContactAdjustEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.base.site.entity.SiteRecordEntity;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.saas.SaasQueryFacade;
import net.summerfarm.tms.facade.saas.dto.StoreAddressAuditDTO;
import net.summerfarm.tms.local.delivery.converter.DeliverySiteDTOConverter;
import net.summerfarm.tms.query.site.ContactAdjustQuery;
import net.summerfarm.tms.service.delivery.DeliverySiteAuditQueryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Description:配送点位审核查询服务实现
 * date: 2024/1/31 18:39
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliverySiteAuditQueryServiceImpl implements DeliverySiteAuditQueryService {

    private final DeliverySiteRepository deliverySiteRepository;
    private final SiteRepository siteRepository;
    private final ContactAdjustRepository contactAdjustRepository;
    private final SaasQueryFacade saasQueryFacade;

    @Override
    public DeliverySiteDTO queryDeliverySiteAuditDetail(Long deliverySiteId) {
        TmsAssert.notNull(deliverySiteId, ErrorCodeEnum.PARAM_NOT_NULL, "deliverySiteId");
        DeliverySiteEntity deliverySiteEntity = deliverySiteRepository.queryWithDistOrders(deliverySiteId);
        if(deliverySiteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,deliverySiteId);
        }
        List<DistOrderEntity> distOrderEntityList = deliverySiteEntity.getSiteDistOrders();
        if(CollectionUtils.isEmpty(distOrderEntityList)){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"委托单信息");
        }
        SiteEntity siteEntity = siteRepository.queryWithRecord(deliverySiteEntity.getSiteId());
        if(siteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"点位信息");
        }
        deliverySiteEntity.setSiteEntity(siteEntity);
        //判断点位类型
        if(Objects.equals(siteEntity.getType(), TmsSiteTypeEnum.CUSTOMER.getCode())){
            //鲜沐类型点位
            //取委托单上的contactId
            String outContactId = distOrderEntityList.get(0).getDistClientVO().getOutContactId();
            ContactAdjustEntity contactAdjustEntity = contactAdjustRepository.query(ContactAdjustQuery.builder()
                    .contactId(outContactId).build());
            deliverySiteEntity.setContactStatus(contactAdjustEntity.getStatus());
        }else if(Objects.equals(siteEntity.getType(), TmsSiteTypeEnum.SAAS.getCode())){
            //SAAS类型点位
            SiteRecordEntity recentSubmitRecord = siteEntity.getRecentSubmitRecord(deliverySiteId);
            //状态 0 待审核, 1 审核通过 ,2 拒绝重新交, 3 审核失败 线上数据无审核失败的状态
            Integer contactStatus = null;
            if (recentSubmitRecord != null){
                StoreAddressAuditDTO storeAddressAuditDTO = saasQueryFacade.queryStoreAddressAuditRecord(recentSubmitRecord.getId());
                if (storeAddressAuditDTO != null){
                    contactStatus = storeAddressAuditDTO.getUnifiedStatus();
                }
            }
            deliverySiteEntity.setContactStatus(contactStatus);
        }
        return DeliverySiteDTOConverter.entity2Dto(deliverySiteEntity);
    }
}
