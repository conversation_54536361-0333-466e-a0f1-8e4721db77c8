package net.summerfarm.tms.service.backdoor;

import net.summerfarm.tms.inbound.controller.backdoor.input.DistOrderDeliveryTimeChangeCommandInput;

import java.util.List;
import java.util.Map;

/**
 * Description:后门接口
 * date: 2024/2/5 11:54
 *
 * <AUTHOR>
 */
public interface BackDoorService {

    /**
     * 批量修改配送时间
     * @param input 参数
     * @return 结果
     */
    Map<String, String> batchChangeDeliveryTime(DistOrderDeliveryTimeChangeCommandInput input);

    /**
     * 批量修改的配送点顺序 通过过orTools
     * @param batchIds 批次ID集合
     */
    void updateDeliverySiteSequenceByOrTools(List<Long> batchIds);

    /**
     * 批量修改的配送批次顺序 通过过orTools
     * @param batchId 批次ID
     */
    void updateDeliveryBatchSequenceByOrTools(Long batchId);
}
