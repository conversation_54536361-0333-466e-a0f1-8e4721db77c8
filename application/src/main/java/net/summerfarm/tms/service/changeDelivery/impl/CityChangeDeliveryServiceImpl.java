package net.summerfarm.tms.service.changeDelivery.impl;

import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.inbound.controller.delivery.input.command.RouteReassignmentDriverCommandInput;
import net.summerfarm.tms.inbound.controller.delivery.input.command.SiteReassignmentRouteCommand;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.summerfarm.tms.pick.repository.DeliveryPickScanCodeQueryRepository;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.service.changeDelivery.CityChangeDeliveryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 城配改派业务
 * date: 2025/4/25 16:42<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CityChangeDeliveryServiceImpl implements CityChangeDeliveryService {

    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;
    @Resource
    private DeliveryPickScanCodeQueryRepository deliveryPickScanCodeQueryRepository;
    @Resource
    private DeliverySiteDomainService deliverySiteDomainService;
    @Resource
    private DeliveryOrderDomainService deliveryOrderDomainService;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private DeliveryPickDomainService deliveryPickDomainService;
    @Resource
    private EventBusService eventBusService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void routeReassignmentDriver(RouteReassignmentDriverCommandInput input) {
        Long changeToDriverId = input.getDriverId();
        Long batchId = input.getBatchId();
        if (changeToDriverId == null || batchId == null) {
            return;
        }

        DeliveryBatchEntity changeDriverBatchEntity = deliveryBatchRepository.query(batchId);
        if (changeDriverBatchEntity == null) {
            throw new TmsRuntimeException("所选路线不存在");
        }

        // 改派校验
        this.reassignDeliveryTimeSiteStatusPickCheck(changeDriverBatchEntity);

        List<DeliveryBatchEntity> driverHaveBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .type(DeliveryBatchTypeEnum.city.getCode())
                .driverId(changeToDriverId)
                .deliveryTime(changeDriverBatchEntity.getDeliveryTime())
                .build());

        if (!CollectionUtils.isEmpty(driverHaveBatchEntityList)) {
            throw new TmsRuntimeException("所选司机存在排线信息");
        }

        deliveryBatchDomainService.routeReassignmentDriver(changeToDriverId,changeDriverBatchEntity);
    }

    /**
     * 改派校验
     * @param changeDriverBatchEntity 批次日期校验
     */
    private void reassignDeliveryTimeSiteStatusPickCheck(DeliveryBatchEntity changeDriverBatchEntity) {
        // 日期校验
        List<LocalDate> couldChangeDateList = Arrays.asList(LocalDate.now(), LocalDate.now().plusDays(1));
        // 记得开放
        if (!couldChangeDateList.contains(changeDriverBatchEntity.getDeliveryTime().toLocalDate())) {
            throw new TmsRuntimeException("只允许配送日期为当天明天可以改配");
        }
        if (changeDriverBatchEntity.getStatus() == DeliveryBatchStatusEnum.TO_BE_WIRED) {
            throw new TmsRuntimeException("未完成排线不支持改派");
        }
        if(changeDriverBatchEntity.getPickUpTime() != null){
            throw new TmsRuntimeException("已完成拣货不可改派");
        }

        // 到仓打卡、出仓校验
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(changeDriverBatchEntity.getId()).build());
        List<DeliverySiteEntity> beginDeliverySiteList = deliverySiteEntities.stream()
                .filter(e -> Objects.equals(e.getType(), DeliverySiteTypeEnum.begin.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(beginDeliverySiteList)) {
            throw new TmsRuntimeException("所选路线不存在起点");
        }
        if (beginDeliverySiteList.get(0).getStatus() != DeliverySiteStatusEnum.NO) {
            throw new TmsRuntimeException("司机已到达起点不可改派");
        }

        // 拣货扫码校验
        List<DeliveryPickScanCodeEntity> deliveryPickScanCodeEntities = deliveryPickScanCodeQueryRepository.queryByDeliveryBatchId(changeDriverBatchEntity.getId());
        if (!CollectionUtils.isEmpty(deliveryPickScanCodeEntities)) {
            throw new TmsRuntimeException("所选路线存在拣货扫码信息，不可改派");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void siteReassignmentRoute(SiteReassignmentRouteCommand input) {
        Long changeToBatchId = input.getBatchId();
        Long noRouteDeliverySiteId = input.getDeliverySiteId();

        if(changeToBatchId == null || noRouteDeliverySiteId == null){
            return;
        }

        DeliveryBatchEntity changeDriverBatchEntity = deliveryBatchRepository.query(changeToBatchId);
        if (changeDriverBatchEntity == null) {
            throw new TmsRuntimeException("所选路线不存在");
        }

        // 改派校验
        this.reassignDeliveryTimeSiteStatusPickCheck(changeDriverBatchEntity);

        DeliverySiteEntity noRouteDeliverySiteEntity = deliverySiteRepository.queryWithDistOrders(noRouteDeliverySiteId);
        if (noRouteDeliverySiteEntity == null) {
            throw new TmsRuntimeException("所选配送门店不存在");
        }
        Long noRouteDeliveryBatchId = noRouteDeliverySiteEntity.getDeliveryBatchId();
        DeliveryBatchEntity noRouteDeliveryBatchEntity = deliveryBatchRepository.query(noRouteDeliveryBatchId);
        if (!Objects.equals(noRouteDeliveryBatchEntity.getPathId(), Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)) {
            throw new TmsRuntimeException("当前店铺不在未排路线里面，不可改派");
        }

        // 配送单批次修改
        List<DeliveryOrderEntity> needChangeBatchIdDeliveryOrderList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                .deliveryTime(noRouteDeliverySiteEntity.getPlanArriveTime().toLocalDate())
                .batchId(noRouteDeliverySiteEntity.getDeliveryBatchId())
                .endSiteId(noRouteDeliverySiteEntity.getSiteId())
                .beginSiteId(changeDriverBatchEntity.getBeginSiteId())
                .build());

        deliveryOrderDomainService.changeSiteBatch(needChangeBatchIdDeliveryOrderList, changeToBatchId);

        // 修改配送单和委托单状态、生成配送单详情
        deliveryOrderDomainService.completePath(needChangeBatchIdDeliveryOrderList);

        // 配送点位修改批次信息和顺序
        DeliverySiteEntity deliverySiteChangeBatch = new DeliverySiteEntity();
        deliverySiteChangeBatch.setSendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());
        deliverySiteChangeBatch.setDeliveryBatchId(changeToBatchId);
        deliverySiteChangeBatch.setId(noRouteDeliverySiteId);
        deliverySiteChangeBatch.setSequence(input.getSequence());
        deliverySiteDomainService.changeSiteBatch(deliverySiteChangeBatch);

        // 重新生成拣货任务
        //查询详情
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchDomainService.deliveryBatchDetail(changeToBatchId);
        deliveryPickDomainService.completePathAgain(deliveryBatchEntity);

        // 智能排线
        deliverySiteDomainService.intelligentPath(deliveryBatchEntity,true);

        // 生成配点位详情
        deliveryBatchEntity.setDeliverySiteList(deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder().batchId(changeToBatchId).build()));
        deliveryBatchEntity.setDeliveryOrderEntityList(needChangeBatchIdDeliveryOrderList);
        deliverySiteDomainService.completePath(deliveryBatchEntity);

        // 批次完成排线公里数计算通知
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_PICKED);
        deliveryBatchDomainService.completePath(deliveryBatchEntity);

        // 完成排线通知
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                CompletePathMessage msg = new CompletePathMessage();
                msg.setDeliveryBatchIds(Collections.singletonList(changeToBatchId));
                eventBusService.finishCompletePathAllMessageSend(msg);
            }
        });

    }
}
