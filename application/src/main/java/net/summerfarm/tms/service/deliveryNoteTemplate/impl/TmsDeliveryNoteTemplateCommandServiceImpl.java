package net.summerfarm.tms.service.deliveryNoteTemplate.impl;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateBelongQueryRepository;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateCommandRepository;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateQueryRepository;
import net.summerfarm.tms.deliveryNoteTemplate.service.TmsDeliveryNoteTemplateCommandDomainService;
import net.summerfarm.tms.enums.TmsDeliveryNoteTemplateEnums;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.ext.AuthExtService;
import net.summerfarm.tms.ext.AuthService;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.assembler.TmsDeliveryNoteTemplateAssembler;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.*;
import net.summerfarm.tms.service.deliveryNoteTemplate.TmsDeliveryNoteTemplateCommandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
@Service
@Transactional(rollbackFor = Exception.class)
public class TmsDeliveryNoteTemplateCommandServiceImpl implements TmsDeliveryNoteTemplateCommandService {

    @Autowired
    private TmsDeliveryNoteTemplateCommandDomainService tmsDeliveryNoteTemplateCommandDomainService;
    @Autowired
    private TmsDeliveryNoteTemplateQueryRepository tmsDeliveryNoteTemplateQueryRepository;
    @Autowired
    private TmsDeliveryNoteTemplateBelongQueryRepository tmsDeliveryNoteTemplateBelongQueryRepository;
    @Autowired
    private AuthExtService authExtService;
    @Autowired
    private TmsDeliveryNoteTemplateCommandRepository tmsDeliveryNoteTemplateCommandRepository;


    @Override
    public void insert(TmsDeliveryNoteTemplateSaveCommandInput input) {
        // 顺路达和外单只能有一个默认模版
        List<Integer> onlyOneDeliveryNoteTemplateAppSourceList = Arrays.asList(TmsDeliveryNoteTemplateEnums.AppSource.SUNLUDA.getValue(), TmsDeliveryNoteTemplateEnums.AppSource.OUTSIDE.getValue());
        if(onlyOneDeliveryNoteTemplateAppSourceList.contains(input.getAppSource())){
            // 查询数据库是否已经存在
            List<TmsDeliveryNoteTemplateEntity> existEntityList = tmsDeliveryNoteTemplateQueryRepository.selectByCondition(TmsDeliveryNoteTemplateQueryParam.builder()
                    .appSource(input.getAppSource())
                    .build());
            if(!CollectionUtils.isEmpty(existEntityList)){
                throw new TmsRuntimeException("顺路达和外单来源只能存在一个配送单模版");
            }
        }
        List<TmsDeliveryNoteTemplateBelongSaveCommandInput> belongSaveCommandInputList = input.getBelongSaveCommandInputList();
        if(CollectionUtils.isEmpty(belongSaveCommandInputList)){
            throw new TmsRuntimeException("配送单归属不能为空");
        }
        // 查询配送单归属是否有重复的
        List<TmsDeliveryNoteTemplateBelongEntity> haveSameBusinessIdList = tmsDeliveryNoteTemplateBelongQueryRepository.selectListByCondition(TmsDeliveryNoteTemplateBelongQueryParam.builder()
                .scopeBusinessIds(belongSaveCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongSaveCommandInput::getScopeBusinessId).collect(Collectors.toList()))
                .build());

        // key =业务ID + # + 来源 + # + 作用域,value=租户ID
        Map<String, Long> businessIdAppSourceScopeType2TenantIdMap = belongSaveCommandInputList.stream()
                .collect(Collectors.toMap(a -> a.getScopeBusinessId() + "#"  + a.getAppSource() + "#" + a.getScopeType(),
                        TmsDeliveryNoteTemplateBelongSaveCommandInput::getTenantId,
                        (b, c) -> b));

        for (TmsDeliveryNoteTemplateBelongEntity templateBelong : haveSameBusinessIdList) {
            Long tenantId = businessIdAppSourceScopeType2TenantIdMap.get(templateBelong.getScopeBusinessId() + "#" + templateBelong.getAppSource() + "#" + templateBelong.getScopeType());
            if(tenantId != null){
                throw new TmsRuntimeException(templateBelong.getScopeBusinessName() + "已存在,配送单ID:" + templateBelong.getDeliveryNoteTemplateId());
            }
        }

        TmsDeliveryNoteTemplateCommandParam param = TmsDeliveryNoteTemplateAssembler.buildCreateParam(input);
        param.setLastOperatorName(authExtService.getCurrentUserName());
        tmsDeliveryNoteTemplateCommandDomainService.insert(param);
    }


    @Override
    public void update(TmsDeliveryNoteTemplateUpdateCommandInput input) {
        // 逻辑校验
        if (input.getId() == null) {
            throw new RuntimeException("id不能为空");
        }
        // 查询数据
        TmsDeliveryNoteTemplateEntity deliveryNoteTemplateEntity = tmsDeliveryNoteTemplateQueryRepository.selectById(input.getId());
        if (deliveryNoteTemplateEntity == null) {
            throw new TmsRuntimeException("没找到对应的数据");
        }

        List<TmsDeliveryNoteTemplateBelongUpdateCommandInput> belongUpdateCommandInputList = input.getBelongUpdateCommandInput();
        if(CollectionUtils.isEmpty(belongUpdateCommandInputList)){
            throw new TmsRuntimeException("配送单归属不能为空");
        }

        // 查询配送单归属是否有重复的
        List<TmsDeliveryNoteTemplateBelongEntity> haveSameBusinessIdList = tmsDeliveryNoteTemplateBelongQueryRepository.selectListByCondition(TmsDeliveryNoteTemplateBelongQueryParam.builder()
                .scopeBusinessIds(belongUpdateCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongUpdateCommandInput::getScopeBusinessId).collect(Collectors.toList()))
                .build());

        // 排除掉当前配送单模版的归属数据
        haveSameBusinessIdList = haveSameBusinessIdList.stream().filter(t -> !t.getDeliveryNoteTemplateId().equals(deliveryNoteTemplateEntity.getId())).collect(Collectors.toList());

        // key =业务ID + # + 来源 + # + 作用域,value=租户ID
        Map<String, Long> businessIdAppSourceScopeType2TenantIdMap = belongUpdateCommandInputList.stream()
                .collect(Collectors.toMap(a -> a.getScopeBusinessId() + "#"  + a.getAppSource() + "#" + a.getScopeType(),
                        TmsDeliveryNoteTemplateBelongUpdateCommandInput::getTenantId,
                        (b, c) -> b));

        for (TmsDeliveryNoteTemplateBelongEntity templateBelong : haveSameBusinessIdList) {
            Long tenantId = businessIdAppSourceScopeType2TenantIdMap.get(templateBelong.getScopeBusinessId() + "#" + templateBelong.getAppSource() + "#" + templateBelong.getScopeType());
            if(tenantId != null){
                throw new TmsRuntimeException(templateBelong.getScopeBusinessName() + "已存在,配送单ID:" + templateBelong.getDeliveryNoteTemplateId());
            }
        }
        TmsDeliveryNoteTemplateCommandParam tmsDeliveryNoteTemplateCommandParam = TmsDeliveryNoteTemplateAssembler.buildUpdateParam(input);
        tmsDeliveryNoteTemplateCommandParam.setLastOperatorName(authExtService.getCurrentUserName());
        // 更新数据
        tmsDeliveryNoteTemplateCommandDomainService.update(tmsDeliveryNoteTemplateCommandParam);
    }


    @Override
    public int delete(Long id) {
        return tmsDeliveryNoteTemplateCommandDomainService.delete(id);
    }

    @Override
    public void useStateUpdate(TmsDeliveryNoteTemplateUseStateUpdateCommandInput input) {
        Long id = input.getId();
        if(id == null){
            return;
        }
        TmsDeliveryNoteTemplateEntity deliveryNoteTemplateEntity = tmsDeliveryNoteTemplateQueryRepository.selectById(id);
        if (deliveryNoteTemplateEntity == null) {
            throw new TmsRuntimeException("没找到对应的数据");
        }

        // 默认的配送单不能变更状态
        if (Objects.equals(TmsDeliveryNoteTemplateEnums.ScopeType.DEFAULT.getValue(),deliveryNoteTemplateEntity.getScopeType())) {
            throw new TmsRuntimeException("默认的配送单不能变更状态");
        }

        // 更新状态
        TmsDeliveryNoteTemplateCommandParam useStateParam = TmsDeliveryNoteTemplateCommandParam.builder()
                .id(deliveryNoteTemplateEntity.getId())
                .useState(input.getUseState())
                .lastOperatorName(authExtService.getCurrentUserName())
                .build();
        tmsDeliveryNoteTemplateCommandRepository.updateSelectiveById(useStateParam);
    }

}