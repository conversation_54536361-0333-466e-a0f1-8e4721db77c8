package net.summerfarm.tms.service.deliveryNoteTemplate;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateSaveCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateUseStateUpdateCommandInput;

/**
 * @date 2024-12-09 14:00:38
 * @version 1.0
 */
public interface TmsDeliveryNoteTemplateCommandService {

    /**
     * @description: 新增
     * @return TmsDeliveryNoteTemplateEntity
     **/
    void insert(TmsDeliveryNoteTemplateSaveCommandInput input);


    /**
     * @description: 更新
     * @return:
     **/
    void update(TmsDeliveryNoteTemplateUpdateCommandInput input);



    /**
    * @description: 删除
    * @return:
    **/
    int delete(Long id);

    /**
     * 更新配送单启用、停用状态
     * @param input 入参
     */
    void useStateUpdate(TmsDeliveryNoteTemplateUseStateUpdateCommandInput input);
}