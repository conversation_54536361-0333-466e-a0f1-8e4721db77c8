package net.summerfarm.tms.service.common;

import net.xianmu.download.support.dto.DownloadCenterRecordDTO;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * Description:下载中心服务接口
 * date: 2024/2/20 17:11
 *
 * <AUTHOR>
 */
public interface DownloadCenterService {

    /**
     * 异步上传文件到OSS
     * @param recordDTO 下载中心记录
     * @param paramOrData 帮助生成需要上传的数据
     * @param queryFunction 查询函数
     * @param excelClass excel类
     * @return 资源ID
     * @param <P> 入参类型
     * @param <R> 出参类型
     */
    <P, R> Long asyncUpload(DownloadCenterRecordDTO recordDTO, P paramOrData, Function<P, List<R>> queryFunction, Class<R> excelClass);

    /**
     * 异步上传文件到OSS并记录上传结果到下载中心
     *
     * @param recordDTO                   下载中心记录
     * @param paramOrData                 帮助生成需要上传的数据
     * @param writeDataToTempFileConsumer 将需要上传的数据写入临时文件的方法
     * @param <T>                         指定类型
     * @return 资源ID
     */
    <T> Long asyncUploadAndRecordResult(DownloadCenterRecordDTO recordDTO, T paramOrData, BiConsumer<File, T> writeDataToTempFileConsumer);

    /**
     * 异步上传文件到OSS并记录上传结果到下载中心
     * 业务自定义Workbook场景可使用
     *
     * @param recordDTO                   下载中心记录
     * @param paramOrData                 帮助生成需要上传的数据
     * @param writeDataToTempFileFunction 将需要上传的数据写入临时文件的方法
     * @return 资源ID
     */
    <T> Long asyncUploadAndRecordResult(DownloadCenterRecordDTO recordDTO, T paramOrData, Function<T, Workbook> writeDataToTempFileFunction);

}
