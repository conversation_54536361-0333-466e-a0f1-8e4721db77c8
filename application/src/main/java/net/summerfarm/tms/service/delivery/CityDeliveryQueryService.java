package net.summerfarm.tms.service.delivery;

import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.command.UploadKeepTemperatureMethodPicCommandInput;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.query.NeedUploadKeepTemperatureMethodPicQueryInput;

/**
 * 城配业务查询服务
 */
public interface CityDeliveryQueryService {

    /**
     * 查询是否需要上传保温措施图片
     * @param input 入参
     * @return ture 需要 false 不需要
     */
    Boolean queryNeedUploadKeepTemperatureMethodPic(NeedUploadKeepTemperatureMethodPicQueryInput input);

    /**
     * 上传保温措施图片
     * @param input 入参
     */
    void uploadKeepTemperatureMethodPic(UploadKeepTemperatureMethodPicCommandInput input);
}
