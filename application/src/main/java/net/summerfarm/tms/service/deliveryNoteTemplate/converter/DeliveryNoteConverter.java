package net.summerfarm.tms.service.deliveryNoteTemplate.converter;

import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderItemFlatObject;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.facade.ofc.converter.FulfillmentConverter;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderInfoDTO;
import net.summerfarm.tms.facade.ofc.dto.FulfillmentDeliveryNoteOrderItemInfoDTO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.DeliveryNoteRenderingExtendExcelVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.GoodsItemVO;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 配送单转换<br/>
 * date: 2025/1/20 18:35<br/>
 *
 * <AUTHOR> />
 */
public class DeliveryNoteConverter {

    public static DeliveryNoteRenderingExtendExcelVO objectListToExcelVO(List<DeliveryNoteOrderFlatObject> objs) {
        if(CollectionUtils.isEmpty(objs)){
            return null;
        }
        DeliveryNoteRenderingExtendExcelVO vo = new DeliveryNoteRenderingExtendExcelVO();

        DeliveryNoteOrderFlatObject obj = objs.get(0);

        vo.setOrderNo(objs.stream()
                .map(DeliveryNoteOrderFlatObject::getOrderNo)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(",")));

        vo.setOrderRemark(objs.stream()
                .map(DeliveryNoteOrderFlatObject::getOrderRemark)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(";")));

        if(obj.getMerchantSize() != null && "大客户".equals(obj.getMerchantSize())){
            vo.setMerchantName(obj.getMerchantName() + "(大客户)");
        }else{
            vo.setMerchantName(obj.getMerchantName());
        }
        vo.setContactName(obj.getContactName());
        vo.setContactPhone(obj.getContactPhone());
        vo.setDetailAddress(obj.getDeliveryAddress());
        vo.setSendRemark(obj.getSendRemark());
        vo.setStoreName(obj.getStoreName());
        if(obj.getDeliveryTime() != null){
            vo.setDeliveryTime(obj.getDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        vo.setBrandName(obj.getOuterBrandName());
        vo.setBdName(obj.getBdName());
        vo.setBdPhone(obj.getBdPhone());
        vo.setSellingEntityName(obj.getSellingEntityName());
        if(StringUtils.isNotBlank(obj.getPathCode()) && StringUtils.isNotBlank(obj.getPathSequence())){
            vo.setDeliveryPath(obj.getPathCode() + "-" + obj.getPathSequence());
        }

        BigDecimal totalWeightInPounds = new BigDecimal(0);
        int totalQuantity = 0;
        BigDecimal totalSubtotal = new BigDecimal(0);

        List<DeliveryNoteOrderItemFlatObject> itemFlatObjects = objs.stream()
                .map(DeliveryNoteOrderFlatObject::getItems)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        itemFlatObjects.sort(Comparator.comparing(DeliveryNoteOrderItemFlatObject::getDistItemId, Comparator.nullsFirst(Comparator.naturalOrder())));

        ArrayList<GoodsItemVO> goodsItemVOS = new ArrayList<>();
        if(!CollectionUtils.isEmpty(itemFlatObjects)){
            for (int i = 0; i < itemFlatObjects.size(); i++) {
                DeliveryNoteOrderItemFlatObject itemObj = itemFlatObjects.get(i);

                GoodsItemVO itemVO = new GoodsItemVO();
                itemVO.setItemId(itemObj.getDistItemId());
                itemVO.setSeq(String.valueOf(i+1));
                itemVO.setItemName(itemObj.getProductName());
                itemVO.setSpecification(itemObj.getSpecification());
                itemVO.setTemperature(itemObj.getTemperatureStr());
                itemVO.setUnit(itemObj.getUnit());
                itemVO.setPrice(itemObj.getPrice() != null ? itemObj.getPrice().stripTrailingZeros().toPlainString() : "0");
                itemVO.setQuantity(String.valueOf(itemObj.getQuantity()));
                if(itemObj.getPrice() != null && itemObj.getPrice().compareTo(BigDecimal.ZERO) > 0){
                    itemVO.setSubtotal(itemObj.getPrice().multiply(new BigDecimal(itemObj.getQuantity())).stripTrailingZeros().toPlainString());
                }else{
                    itemVO.setSubtotal("0");
                }

                if(itemObj.getSkuExtType() != null){
                    if(itemObj.getSkuExtType() == 1){
                        itemVO.setItemName(itemVO.getItemName() + "(活动)");
                    }
                    if(itemObj.getSkuExtType() == 2){
                        itemVO.setItemName(itemVO.getItemName() + "(临保)");
                    }
                }

                goodsItemVOS.add(itemVO);
                if(StringUtils.isNotBlank(itemObj.getWeight()) && itemObj.getQuantity() != null){
                    // 斤数 = 数量 * 重量（单位需要转化为斤）
                    BigDecimal weight = new BigDecimal(itemObj.getWeight());
                    BigDecimal quantity =  new BigDecimal(itemObj.getQuantity());

                    BigDecimal weightInPounds= weight.multiply(quantity).multiply(BigDecimal.valueOf(1000))
                            .divide(BigDecimal.valueOf(500), 2, RoundingMode.HALF_UP);

                    itemVO.setWeightInPounds(weightInPounds.stripTrailingZeros().toPlainString());

                    // 合计斤数
                    totalWeightInPounds = totalWeightInPounds.add(weightInPounds);
                }
                if(itemObj.getQuantity() != null && !Objects.equals("配送费",itemObj.getDistItemId()) && !Objects.equals("加单费",itemObj.getDistItemId())){
                    totalQuantity = totalQuantity + itemObj.getQuantity();
                }

                totalSubtotal = totalSubtotal.add(new BigDecimal(itemVO.getSubtotal()));
            }
        }
        vo.setTotalQuantity(totalQuantity);
        vo.setTotalWeightInPounds(totalWeightInPounds.stripTrailingZeros().toPlainString());
        vo.setTotalSubtotal(totalSubtotal.stripTrailingZeros().toPlainString());
        vo.setGoodsItemVOList(goodsItemVOS);

        return vo;
    }

    public static DeliveryNoteOrderFlatObject fulDeliveryNoteOrderDTO2FlatObject(FulfillmentDeliveryNoteOrderInfoDTO dto) {
        if(dto == null){
            return null;
        }
        DeliveryNoteOrderFlatObject obj = new DeliveryNoteOrderFlatObject();
        Integer orderSource = dto.getOrderSource();
        if(orderSource != null){
            if(dto.getOrderSource() == 100){
                obj.setSource(DistOrderSourceEnum.SAAS_MALL.getCode());
            }else if(dto.getOrderSource() == 200){
                obj.setSource(DistOrderSourceEnum.XM_MALL.getCode());
            }else if(dto.getOrderSource() == 400){
                obj.setSource(DistOrderSourceEnum.POP_MALL.getCode());
            }else{
                obj.setSource(DistOrderSourceEnum.OUTER_TRUNK_CITY.getCode());
            }
        }

        obj.setOutContactId(dto.getContactId() != null ? dto.getContactId().toString() : null);
        obj.setOuterBrandName(dto.getBrandName());
        obj.setSendRemark(StringUtils.isNotBlank(dto.getAddressRemark()) ? dto.getAddressRemark() : null);
        obj.setOrderNo(dto.getOutOrderNo());
        obj.setMerchantName(dto.getOutClientName());
        obj.setMerchantSize(dto.getStoreSize());
        obj.setDeliveryTime(dto.getFulfillmentTime());
        obj.setOuterTenantId(dto.getTenantId() != null ? dto.getTenantId().toString() : null);
        obj.setMerchantId(dto.getStoreId() != null ?  dto.getStoreId().toString() : null);
        obj.setContactName(dto.getContactName());
        obj.setContactPhone(dto.getContactPhone());
        obj.setDeliveryAddress(dto.getAddress());
        obj.setStoreNo(dto.getStoreNo());
        obj.setStoreName(dto.getStoreName());
        obj.setBigCustomerId(dto.getBigCustomerId());
        obj.setSellingEntityName(dto.getSellingEntityName());
        List<FulfillmentDeliveryNoteOrderItemInfoDTO> fulfillmentOrderItemList = dto.getFulfillmentOrderItemList();
        if(!CollectionUtils.isEmpty(fulfillmentOrderItemList)){
            obj.setItems(fulfillmentOrderItemList.stream().map(DeliveryNoteConverter::itemDTO2FlatObject).collect(Collectors.toList()));
        }
        obj.setOrderRemark(dto.getOrderRemark());

        return obj;
    }

    public static DeliveryNoteOrderItemFlatObject itemDTO2FlatObject(FulfillmentDeliveryNoteOrderItemInfoDTO dto) {
        if(dto == null){
            return null;
        }
        DeliveryNoteOrderItemFlatObject obj = new DeliveryNoteOrderItemFlatObject();
        obj.setSku(dto.getSku());
        obj.setSkuName(dto.getSkuName());
        obj.setQuantity(dto.getProductAmount());
        obj.setUnit(dto.getProductUnit());
        obj.setSpecification(dto.getProductSpecifications());
        obj.setWeight(dto.getSkuWeightNum() != null ? dto.getSkuWeightNum().toString() : null);
        obj.setVolume(dto.getSkuVolume());
        obj.setTemperature(dto.getSkuStorageLocation());
        obj.setPrice(dto.getProductUnitPrice());
        obj.setProductName(dto.getProductName());
        obj.setWarehouseNo(dto.getWarehouseNo());
        return obj;
    }
}
