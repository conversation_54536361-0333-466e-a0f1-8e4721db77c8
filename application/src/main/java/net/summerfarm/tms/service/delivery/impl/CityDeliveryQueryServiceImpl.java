package net.summerfarm.tms.service.delivery.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.DeliverySiteRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;
import net.summerfarm.tms.delivery.param.command.TmsDeliveryBatchExtCommandParam;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtCommandRepository;
import net.summerfarm.tms.delivery.repository.TmsDeliveryBatchExtQueryRepository;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.command.UploadKeepTemperatureMethodPicCommandInput;
import net.summerfarm.tms.inbound.controller.wechatMiniProgram.input.query.NeedUploadKeepTemperatureMethodPicQueryInput;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.service.delivery.CityDeliveryQueryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 城配业务查询服务
 * <AUTHOR> />
 */
@Slf4j
@Service
public class CityDeliveryQueryServiceImpl implements CityDeliveryQueryService {

    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private TmsDeliveryBatchExtQueryRepository tmsDeliveryBatchExtQueryRepository;
    @Resource
    private TmsDeliveryBatchExtCommandRepository tmsDeliveryBatchExtCommandRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;

    @Override
    public Boolean queryNeedUploadKeepTemperatureMethodPic(NeedUploadKeepTemperatureMethodPicQueryInput input) {
        DeliveryBatchEntity currentDeliveryBatch = deliveryBatchRepository.queryWithDriver(input.getBatchId());
        if (currentDeliveryBatch == null) {
            throw new TmsRuntimeException("批次信息不存在");
        }

        // 已上传过保温措施照片，无需拍照
        String keepTemperatureMethodPics = tmsDeliveryBatchExtQueryRepository.queryKeepTemperatureMethodPicsByBatchId(currentDeliveryBatch.getId());
        if (StringUtils.isNotBlank(keepTemperatureMethodPics)) {
            return false;
        }

        DriverEntity driverEntity = currentDeliveryBatch.getDriverEntity();
        if (driverEntity == null) {
            throw new TmsRuntimeException("批次不存在司机");
        }

        // 查询司机没有稽查标识，无需拍照
        if (driverEntity.getKeepTemperatureMethodAudit() == null ||
                Objects.equals(driverEntity.getKeepTemperatureMethodAudit(), DriverEnums.keepTemperatureMethodAudit.NO_AUDIT.getValue())) {
            return false;
        }

        // 未完成配送店铺数不过半无需拍照
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryListWithItems(DeliverySiteQuery.builder()
                .batchIdList(Collections.singletonList(input.getBatchId()))
                .build());

        // 排除城配仓点位
        List<DeliverySiteEntity> noBeginDeliverySiteList = deliverySiteEntities.stream()
                .filter(e -> !Objects.equals(e.getSiteId(), currentDeliveryBatch.getBeginSiteId()))
                .collect(Collectors.toList());

        // 排除城配仓点位已完成完成配送店铺的数量
        List<DeliverySiteEntity> haveFinishDeliverySiteList = noBeginDeliverySiteList.stream()
                .filter(e -> e.getStatus() == DeliverySiteStatusEnum.FINISH_DELIVERY)
                .collect(Collectors.toList());

        List<DeliverySiteEntity> noFinishDeliverySiteList = noBeginDeliverySiteList.stream()
                .filter(e -> e.getStatus() != DeliverySiteStatusEnum.FINISH_DELIVERY)
                .collect(Collectors.toList());
        List<DeliverySiteItemEntity> noFinishDeliverySiteItemList = noFinishDeliverySiteList.stream()
                .map(DeliverySiteEntity::getDeliverySiteItemEntityList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // 过滤出未完成配送的冷冻冷藏商品
        List<Integer> freezeColdTemperature = Arrays.asList(TmsTemperatureEnum.FREEZE.getCode(), TmsTemperatureEnum.COLD.getCode());
        List<DeliverySiteItemEntity> onFinishDeliveryFreezeColdTemperatureItems = noFinishDeliverySiteItemList.stream()
                .filter(e -> Objects.equals(e.getType(), DeliverySiteItemTypeEnum.DELIVERY.getCode()))
                .filter(e -> freezeColdTemperature.contains(e.getTemperature()))
                .collect(Collectors.toList());

        // 存在未完成配送的不存在冷冻冷藏商品，无需要拍照
        if (onFinishDeliveryFreezeColdTemperatureItems.size() <= 0) {
            return false;
        }

        boolean isNeedUploadKeepTemperatureMethodPic = false;
        if (new BigDecimal(haveFinishDeliverySiteList.size()).divide(BigDecimal.valueOf(noBeginDeliverySiteList.size()),2, RoundingMode.HALF_UP)
                .compareTo(new BigDecimal("0.5")) >= 0) {
            isNeedUploadKeepTemperatureMethodPic = true;
        }
        return isNeedUploadKeepTemperatureMethodPic;
    }

    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    @Override
    public void uploadKeepTemperatureMethodPic(UploadKeepTemperatureMethodPicCommandInput input) {
        DeliveryBatchEntity currentDeliveryBatch = deliveryBatchRepository.query(input.getBatchId());
        if (currentDeliveryBatch == null) {
            throw new TmsRuntimeException("批次信息不存在");
        }

        // 已上传过保温措施照片，无需拍照
        String keepTemperatureMethodPics = tmsDeliveryBatchExtQueryRepository.queryKeepTemperatureMethodPicsByBatchId(currentDeliveryBatch.getId());
        if (StringUtils.isNotBlank(keepTemperatureMethodPics)) {
            throw new TmsRuntimeException("保温措施已上传,请勿重复上传");
        }

        // 保存保温措施照片
        TmsDeliveryBatchExtCommandParam extCommandParam = new TmsDeliveryBatchExtCommandParam();
        extCommandParam.setDeliveryBatchId(input.getBatchId());
        extCommandParam.setExtKey(DeliveryBatchExtEnum.KEEP_TEMPERATURE_METHOD_PICS.getCode());
        extCommandParam.setExtValue(input.getKeepTemperatureMethodPics());
        tmsDeliveryBatchExtCommandRepository.insertSelective(extCommandParam);
    }
}
