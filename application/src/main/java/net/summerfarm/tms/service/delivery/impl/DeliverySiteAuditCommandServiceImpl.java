package net.summerfarm.tms.service.delivery.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.ContactAdjustRepository;
import net.summerfarm.tms.base.site.SiteDomainService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.base.site.param.ContactAdjustCommandParam;
import net.summerfarm.tms.base.site.param.SiteRecordCommandParam;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.enums.ContactAdjustStatusEnum;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.saas.SaasCommandFacade;
import net.summerfarm.tms.facade.saas.SaasQueryFacade;
import net.summerfarm.tms.facade.saas.input.AddressAuditCreateCommandInput;
import net.summerfarm.tms.inbound.controller.delivery.input.DeliverySiteAuditCommandInput;
import net.summerfarm.tms.inbound.converter.delivery.SiteRecordParamConverter;
import net.summerfarm.tms.service.delivery.DeliverySiteAuditCommandService;
import net.summerfarm.tms.service.delivery.DeliverySiteAuditQueryService;
import net.summerfarm.tms.util.DistanceUtil;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.user.UserInfoHolder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Description:配送点位审核操作服务实现
 * date: 2024/1/31 18:40
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliverySiteAuditCommandServiceImpl implements DeliverySiteAuditCommandService {

    private final DeliverySiteAuditQueryService deliverySiteAuditQueryService;
    private final SiteDomainService siteDomainService;
    private final ContactAdjustRepository contactAdjustRepository;
    private final SaasCommandFacade saasCommandFacade;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverySiteAudit(DeliverySiteAuditCommandInput commandInput) {
        DeliverySiteDTO deliverySiteDTO = deliverySiteAuditQueryService.queryDeliverySiteAuditDetail(commandInput.getDeliverySiteId());
        if (deliverySiteDTO == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"运输点位信息");
        }
        if (Objects.equals(deliverySiteDTO.getContactStatus(), ContactAdjustStatusEnum.WAIT_HANDLE.getStatus())){
            throw new TmsRuntimeException("该点位正在审核中");
        }
        List<DistOrderDTO> distOrderDTOS = deliverySiteDTO.getDistOrderDTOS();
        if(CollectionUtils.isEmpty(distOrderDTOS)){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND,"委托单信息");
        }
        SiteDTO siteDTO = Optional.ofNullable(deliverySiteDTO.getSiteDTO()).orElse(new SiteDTO());
        SiteRecordCommandParam param = SiteRecordParamConverter.input2Param(commandInput);
        UserBase user = Optional.ofNullable(UserInfoHolder.getUser()).orElse(new UserBase());
        param.setSiteId(deliverySiteDTO.getSiteId());
        param.setOldPoi(siteDTO.getPoi());
        double poiDistance = DistanceUtil.getPoiDistance(param.getOldPoi(), param.getNewPoi());
        long distance = Math.round(poiDistance);
        param.setDistance(distance);
        param.create(user.getBizUserId(), user.getNickname());

        Long siteAuditId = siteDomainService.createSiteAudit(param);
        //判断点位类型
        if(Objects.equals(siteDTO.getType(), TmsSiteTypeEnum.CUSTOMER.getCode())){
            ContactAdjustCommandParam xmParam = param.convertXmParam();
            Integer outContactId = Integer.parseInt(distOrderDTOS.get(0).getOutContactId());
            xmParam.create(Integer.valueOf(deliverySiteDTO.getOuterClientId()), outContactId);
            xmParam.setStatus(ContactAdjustStatusEnum.WAIT_HANDLE.getStatus());
            contactAdjustRepository.save(xmParam);
        }else if(Objects.equals(siteDTO.getType(), TmsSiteTypeEnum.SAAS.getCode())){
            Long outTenantId = Long.parseLong(distOrderDTOS.get(0).getOutTenantId());

            AddressAuditCreateCommandInput saasCommandInput =  AddressAuditCreateCommandInput.builder()
                    .tenantId(outTenantId)
                    .storeId(Long.parseLong(deliverySiteDTO.getOuterClientId()))
                    .auditNo(String.valueOf(siteAuditId))
                    .province(param.getNewProvince())
                    .city(param.getNewCity())
                    .area(param.getNewArea())
                    .address(param.getNewAddress())
                    .contactName(siteDTO.getName())
                    .contactPhone(siteDTO.getPhone())
                    .poi(param.getNewPoi())
                    .distance(distance).build();
            saasCommandFacade.createAddressAudit(saasCommandInput);
        }
    }
}
