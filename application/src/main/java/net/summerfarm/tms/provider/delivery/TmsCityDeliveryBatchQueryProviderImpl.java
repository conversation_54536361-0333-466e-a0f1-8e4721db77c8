package net.summerfarm.tms.provider.delivery;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.client.delivery.provider.TmsCityDeliveryBatchQueryProvider;
import net.summerfarm.tms.client.delivery.req.CityDeliveryBatchReq;
import net.summerfarm.tms.client.delivery.resp.CityDeliveryBatchResp;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.provider.delivery.converter.CityDeliveryBatchConverter;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description: 城配仓批次查询服务<br/>
 * date: 2024/8/12 15:42<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsCityDeliveryBatchQueryProviderImpl implements TmsCityDeliveryBatchQueryProvider {

    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;

    @Resource
    private SiteRepository siteRepository;

    @Override
    public DubboResponse<List<CityDeliveryBatchResp>> queryCityDeliveryBatch(@Valid CityDeliveryBatchReq req) {
        SiteEntity siteEntity = siteRepository.queryForceMasterByOutBusinessNoType(String.valueOf(req.getStoreNo()), SiteTypeEnum.store.getCode());
        if(siteEntity == null){
            throw new TmsRuntimeException("城配仓对应的点位不存在");
        }
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .deliveryTime(req.getDeliveryTime().atStartOfDay())
                .type(DeliveryBatchTypeEnum.city.getCode())
                .beginSiteId(siteEntity.getId())
                .build());

        // 排除未拍路线
        List<DeliveryBatchEntity> deliveryBatch = deliveryBatchEntityList.stream().filter(batch -> !Objects.equals(batch.getPathId(), Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID)).collect(Collectors.toList());

        return DubboResponse.getOK(deliveryBatch.stream().map(CityDeliveryBatchConverter::entity2Resp).collect(Collectors.toList()));
    }
}
