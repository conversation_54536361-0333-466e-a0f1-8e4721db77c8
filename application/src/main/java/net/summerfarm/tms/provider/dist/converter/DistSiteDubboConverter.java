package net.summerfarm.tms.provider.dist.converter;

import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.client.dist.req.DistSiteQueryReq;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.tms.query.base.carrier.SiteQuery;

/**
 * Description: <br/>
 * date: 2023/1/28 15:00<br/>
 *
 * <AUTHOR> />
 */
public class DistSiteDubboConverter {

    public static SiteQuery reqToDistSite(DistSiteQueryReq distSiteQueryReq) {
        SiteQuery siteQuery = new SiteQuery();
        if (siteQuery == null) {
            return siteQuery;
        }
        siteQuery.setId(distSiteQueryReq.getId());
        siteQuery.setOutBusinessNo(distSiteQueryReq.getOutBusinessNo());
        siteQuery.setType(distSiteQueryReq.getType());
        siteQuery.setState(distSiteQueryReq.getState());

        return siteQuery;
    }

    public static DistSiteResp distSiteDTOToResp(SiteDTO siteDTO) {
        DistSiteResp distSiteResp = new DistSiteResp();
        if(siteDTO == null){
            return distSiteResp;
        }
        distSiteResp.setId(siteDTO.getId());
        distSiteResp.setOutBusinessNo(siteDTO.getOutBusinessNo());
        distSiteResp.setProvince(siteDTO.getProvince());
        distSiteResp.setCity(siteDTO.getCity());
        distSiteResp.setArea(siteDTO.getArea());
        distSiteResp.setAddress(siteDTO.getAddress());
        distSiteResp.setPoi(siteDTO.getPoi());
        distSiteResp.setPhone(siteDTO.getPhone());
        distSiteResp.setName(siteDTO.getName());
        distSiteResp.setContactPerson(siteDTO.getContactPerson());
        distSiteResp.setType(siteDTO.getType());
        distSiteResp.setSuperviseSiteId(siteDTO.getSuperviseSiteId());
        distSiteResp.setDistance(siteDTO.getDistance());
        distSiteResp.setOuterClientName(siteDTO.getOuterClientName());
        distSiteResp.setFullAddress(siteDTO.getFullAddress());
        distSiteResp.setCreator(siteDTO.getCreator());
        distSiteResp.setCreatorName(siteDTO.getCreatorName());
        distSiteResp.setCreateTime(siteDTO.getCreateTime());
        distSiteResp.setIntelligencePath(siteDTO.getIntelligencePath());
        distSiteResp.setOutTime(siteDTO.getOutTime());
        distSiteResp.setState(siteDTO.getState());
        distSiteResp.setPunchDistance(siteDTO.getPunchDistance());

        return distSiteResp;
    }
}