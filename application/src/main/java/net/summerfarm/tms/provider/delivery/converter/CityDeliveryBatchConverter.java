package net.summerfarm.tms.provider.delivery.converter;

import net.summerfarm.tms.client.delivery.resp.CityDeliveryBatchResp;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;

/**
 * Description: 转换类<br/>
 * date: 2024/8/12 16:04<br/>
 *
 * <AUTHOR> />
 */
public class CityDeliveryBatchConverter {

    public static CityDeliveryBatchResp entity2Resp(DeliveryBatchEntity deliveryBatchEntity){
        if(deliveryBatchEntity == null){
            return null;
        }

        CityDeliveryBatchResp resp = new CityDeliveryBatchResp();
        resp.setId(deliveryBatchEntity.getId());
        resp.setType(deliveryBatchEntity.getType());
        if(deliveryBatchEntity.getStatus() != null){
            resp.setStatus(deliveryBatchEntity.getStatus().getCode());
        }
        resp.setCarId(deliveryBatchEntity.getCarId());
        resp.setDriverId(deliveryBatchEntity.getDriverId());
        resp.setCarrierId(deliveryBatchEntity.getCarrierId());
        resp.setPathId(deliveryBatchEntity.getPathId());
        resp.setPathCode(deliveryBatchEntity.getPathCode());
        resp.setPathName(deliveryBatchEntity.getPathName());
        resp.setBeginSiteId(deliveryBatchEntity.getBeginSiteId());
        resp.setEndSiteId(deliveryBatchEntity.getEndSiteId());
        resp.setDeliveryTime(deliveryBatchEntity.getDeliveryTime());
        resp.setPlanBeginTime(deliveryBatchEntity.getBeginTime());
        resp.setPlanTotalDistance(deliveryBatchEntity.getPlanTotalDistance());
        resp.setRealTotalDistance(deliveryBatchEntity.getRealTotalDistance());
        resp.setBePathTime(deliveryBatchEntity.getBePathTime());
        resp.setPickUpTime(deliveryBatchEntity.getPickUpTime());
        resp.setFinishDeliveryTime(deliveryBatchEntity.getFinishDeliveryTime());

        return resp;
    }
}
