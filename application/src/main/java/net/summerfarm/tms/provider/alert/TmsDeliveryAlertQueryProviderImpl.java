package net.summerfarm.tms.provider.alert;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.alert.DeliveryAlertDomainService;
import net.summerfarm.tms.client.alert.provider.TmsDeliveryAlertQueryProvider;
import net.summerfarm.tms.client.alert.req.SummerfarmDeliveryAlertTimeQueryReq;
import net.summerfarm.tms.client.alert.resp.SummerfarmDeliveryAlertTimeResp;
import net.summerfarm.tms.config.RedisKeyConfig;
import net.summerfarm.tms.enums.DeliveryAlertEnums;
import net.summerfarm.tms.query.alert.LastDeliveryTimeQueryParam;
import net.summerfarm.tms.util.RedisCacheUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalTime;

/**
 * Description: 配送时间预警查询服务提供者<br/>
 * date: 2024/3/29 11:40<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDeliveryAlertQueryProviderImpl implements TmsDeliveryAlertQueryProvider {

    @Resource
    private DeliveryAlertDomainService deliveryAlertDomainService;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    public DubboResponse<SummerfarmDeliveryAlertTimeResp> querySummerfarmDeliveryAlertTime(@Valid SummerfarmDeliveryAlertTimeQueryReq req) {
        LastDeliveryTimeQueryParam queryParam = LastDeliveryTimeQueryParam.builder()
                .storeNo(req.getStoreNo())
                .outerTenantId(req.getAdminId())
                .outerClientId(req.getMerchantId())
                .channel(DeliveryAlertEnums.Channel.XM_MALL)
                .city(req.getCity())
                .area(req.getArea())
                .build();

        String key = RedisKeyConfig.SUMMERFARM_DELIVERY_ALERT_TIME_CACHE_KEY +
                queryParam.getStoreNo() +
                queryParam.getCity() +
                queryParam.getArea() +
                queryParam.getOuterClientId() +
                queryParam.getOuterTenantId() +
                queryParam.getChannel();

        LocalTime lastDeliveryTime = redisCacheUtil.getCacheObjectValue(key, 60L,
                () -> deliveryAlertDomainService.queryDeliveryAlertTimeFrameList(queryParam),LocalTime.class);

        SummerfarmDeliveryAlertTimeResp resp = new SummerfarmDeliveryAlertTimeResp();
        resp.setLastDeliveryTime(lastDeliveryTime);

        return DubboResponse.getOK(resp);
    }
}
