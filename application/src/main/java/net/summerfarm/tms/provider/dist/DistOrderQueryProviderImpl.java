package net.summerfarm.tms.provider.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistBlackConfigDTO;
import net.summerfarm.tms.dist.dto.DistOrderDTO;
import net.summerfarm.tms.dist.dto.DistOrderInterceptDTO;
import net.summerfarm.tms.provider.ProviderUtil;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:委托单查询提供者实现类
 * date: 2022/9/21 15:52
 *
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class DistOrderQueryProviderImpl implements DistOrderQueryProvider {

    @Resource
    private DistOrderService distOrderService;

    @Override
    public DubboResponse<DistOrderDTO> queryDetail(DistOrderQuery distOrderQuery) {
        TmsResult<DistOrderDTO> result = distOrderService.queryDetail(distOrderQuery);
        return ProviderUtil.convert2DubboRes(result);
    }

    @Override
    public DubboResponse<Boolean> isAutoSubmitDistOrder(DistBlackConfigDTO distBlackConfigDTO) {
        TmsResult<Boolean> result = distOrderService.isAutoSubmitDistOrder(distBlackConfigDTO);
        return ProviderUtil.convert2DubboRes(result);
    }

    @Override
    public DubboResponse<List<DistOrderInterceptDTO>> queryInterceptSiteDistOrder(DistOrderQuery distOrderQuery) {
        log.info("订单拦截查询方法queryInterceptSiteDistOrder接收到参数:{}",JSON.toJSONString(distOrderQuery));
        TmsResult<List<DistOrderInterceptDTO>> result = distOrderService.queryInterceptSiteDistOrder(distOrderQuery);
        log.info("订单拦截查询方法queryInterceptSiteDistOrder返回结果:{}",JSON.toJSONString(ProviderUtil.convert2DubboRes(result)));
        return ProviderUtil.convert2DubboRes(result);
    }

    //@PostConstruct
    public void test(){
        String str = "{\"cancelFlag\":true,\"needPagination\":true,\"outerOrderIds\":[\"01166908679528715\",\"01166908517710038\",\"02166908516658265\",\"01166908310565791\",\"01166908309489157\",\"02166902731044521\",\"01166902728091887\",\"01166902701929760\",\"02166902696227433\",\"02166902074260162\",\"04166901975742223\",\"01166900100662980\",\"01166900055118024\",\"01166900052002162\",\"01166899951073350\",\"01166899949521368\",\"01166899878702494\",\"01166899877652269\",\"02166899876751507\",\"01166899668589314\"],\"outerRequest\":true,\"pageIndex\":1,\"pageSize\":20,\"source\":200,\"startRow\":0,\"storeNo\":1}";
        DistOrderQuery distOrderQuery = JSON.parseObject(str, DistOrderQuery.class);
        TmsResult<List<DistOrderInterceptDTO>> result = distOrderService.queryInterceptSiteDistOrder(distOrderQuery);
        System.out.println(JSON.toJSONString(result));
    }
}
