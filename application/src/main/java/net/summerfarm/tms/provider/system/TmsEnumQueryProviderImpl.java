package net.summerfarm.tms.provider.system;

import com.google.common.collect.Lists;
import net.summerfarm.tms.client.system.provider.TmsEnumQueryProvider;
import net.summerfarm.tms.client.system.resp.EnumResp;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/11 18:24
 */
@DubboService
public class TmsEnumQueryProviderImpl implements TmsEnumQueryProvider {
    @Override
    public DubboResponse<List<EnumResp>> queryTrunkCarTypeEnum() {
        List<EnumResp> result = Lists.newArrayList();

        List<Integer> allTrunkType = DeliveryBatchTypeEnum.getAllTrunkType();
        for (Integer batchType : allTrunkType) {
            DeliveryBatchTypeEnum deliveryBatchTypeEnum = DeliveryBatchTypeEnum.code2Enum(batchType);
            if(deliveryBatchTypeEnum == null){
                continue;
            }
            result.add(new EnumResp(batchType, deliveryBatchTypeEnum.getName()));
        }
        return DubboResponse.getOK(result);
    }
}
