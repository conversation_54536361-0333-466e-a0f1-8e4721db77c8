package net.summerfarm.tms.provider.ortools;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.client.ortools.provider.TravelPlanningQueryProvider;
import net.summerfarm.tms.client.ortools.req.TravelSequencePlanningQueryReq;
import net.summerfarm.tms.client.ortools.resp.TravelSequencePlanningResp;
import net.summerfarm.tms.util.PoiUtil;
import net.summerfarm.tms.util.VRPSolverUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 出行规划
 * date: 2025/9/26 14:04<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TravelPlanningQueryProviderImpl implements TravelPlanningQueryProvider {

    @Resource
    private VRPSolverUtil vrpsolverUtil;

    /**
     * 查询出行规划
     * @param req 查询参数
     * @return 结果
     */
    @Override
    public DubboResponse<TravelSequencePlanningResp> queryTravelSequencePlanning(@Valid TravelSequencePlanningQueryReq req) {
        log.info("开始处理出行规划请求，起点：{}，途经点数量：{}",
                req.getBeginSite().getId(), req.getWaypointSite().size());

        TravelSequencePlanningQueryReq.Site beginSite = req.getBeginSite();
        List<TravelSequencePlanningQueryReq.Site> waypointSite = req.getWaypointSite();

        // 验证POI坐标格式
        PoiUtil.validatePoi(beginSite.getPoi());
        waypointSite.forEach(site -> {
            PoiUtil.validatePoi(site.getPoi());
        });

        // 构建完整的站点列表（起点 + 途经点）
        List<TravelSequencePlanningQueryReq.Site> siteList = new ArrayList<>();
        siteList.add(beginSite);
        siteList.addAll(waypointSite);

        // 提取POI坐标和ID列表
        List<String> poiList = siteList.stream().map(TravelSequencePlanningQueryReq.Site::getPoi).collect(Collectors.toList());
        List<Long> ids = siteList.stream().map(TravelSequencePlanningQueryReq.Site::getId).collect(Collectors.toList());

        // 调用VRP求解器获取最优路径顺序
        List<Long> idSequencePlanning = vrpsolverUtil.solve(PoiUtil.convertToDoubleArray(poiList), ids, beginSite.getId());

        // 构建响应对象
        List<TravelSequencePlanningResp.TravelSequence> travelSequenceList = new ArrayList<>();
        Long sequence = 1L;
        for (Long id : idSequencePlanning) {
            TravelSequencePlanningResp.TravelSequence travelSequence = new TravelSequencePlanningResp.TravelSequence();

            travelSequence.setId(id);
            travelSequence.setSequence(sequence);
            travelSequenceList.add(travelSequence);

            sequence++;
        }
        
        TravelSequencePlanningResp resp = new TravelSequencePlanningResp();
        resp.setTravelSequenceList(travelSequenceList);

        return DubboResponse.getOK(resp);

    }
}
