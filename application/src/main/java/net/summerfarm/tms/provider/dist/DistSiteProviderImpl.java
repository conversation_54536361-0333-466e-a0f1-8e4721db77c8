package net.summerfarm.tms.provider.dist;

import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.provider.ProviderUtil;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class DistSiteProviderImpl implements DistSiteProvider{
    @Resource
    SiteService siteService;

    @Override
    public DubboResponse<SiteDTO> queryDistSiteById(Long id, Integer type) {
        TmsResult<SiteDTO> siteDTOTmsResult = siteService.siteDetail(id, type);
        return ProviderUtil.convert2DubboRes(siteDTOTmsResult);
    }
}
