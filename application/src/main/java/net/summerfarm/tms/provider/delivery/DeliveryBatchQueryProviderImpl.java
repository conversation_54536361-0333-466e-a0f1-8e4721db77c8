package net.summerfarm.tms.provider.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.after.ExpenseRepository;
import net.summerfarm.tms.after.entity.ExpenseDetailEntity;
import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.*;
import net.summerfarm.tms.delivery.dto.DeliveryBatchDTO;
import net.summerfarm.tms.delivery.dto.DeliverySectionDTO;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.entity.DeliverySectionEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.enums.ExpenseEnums;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.local.delivery.converter.DeliveryBatchDTOConverter;
import net.summerfarm.tms.local.delivery.converter.DeliverySectionDTOConverter;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryPickQuery;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.expense.ExpenseQuery;
import net.xianmu.common.result.DubboResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description: <br/>
 * date: 2022/12/12 13:48<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class DeliveryBatchQueryProviderImpl implements DeliveryBatchQueryProvider{
    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private DeliveryPickRepository deliveryPickRepository;
    @Resource
    private ExpenseRepository expenseRepository;
    @Resource
    private DeliverySectionRepository deliverySectionRepository;
    @Resource
    private DeliverySiteDomainService deliverySiteDomainService;

    @Override
    public DubboResponse<List<DeliveryBatchDTO>> queryBatch(DeliverySectionQuery deliverySectionQuery) {
        log.info("查询路段信息报文:{}",JSON.toJSONString(deliverySectionQuery));
        LocalDate deliveryTime = deliverySectionQuery.getDeliveryTime();
        String storeNo = deliverySectionQuery.getStoreNo();

        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "deliveryTime");
        TmsAssert.notEmpty(storeNo, ErrorCodeEnum.PARAM_NOT_NULL, "storeNo");

        //根据城配仓编号查询点位信息
        SiteEntity siteEntity = siteRepository.siteDetail(Long.parseLong(deliverySectionQuery.getStoreNo()), TmsSiteTypeEnum.STORE.getCode());
        if(siteEntity == null){
            throw new TmsRuntimeException(ErrorCodeEnum.NOT_FIND, "城配仓");
        }
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryListWithDriver(DeliveryBatchQuery.builder()
                .beginSiteId(siteEntity.getId())
                .deliveryBatchType(DeliveryBatchTypeEnum.city.getCode())
                .deliveryTime(deliveryTime.atStartOfDay())
                .build());

        //过滤未拍批次
        deliveryBatchEntityList = deliveryBatchEntityList.stream().filter(deliveryBatchEntity -> deliveryBatchEntity.getPathId() != -1).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            throw new TmsRuntimeException("当前日期不存在此城配仓的配送批次");
        }

        ArrayList<DeliveryBatchEntity> deliveryBatchEntities = new ArrayList<>();

        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            //商品数、打车点位数、帮采费、打车费
            List<DeliveryPickEntity> deliveryPickEntityList = deliveryPickRepository.queryList(DeliveryPickQuery.builder().batchId(deliveryBatchEntity.getId()).build());
            //商品数
            long skuNum = deliveryPickEntityList.stream().mapToLong(DeliveryPickEntity::getQuantity).sum();

            //查询该批次下所有运输点位
            List<DeliverySiteEntity> deliverySitesWithBatch = deliverySiteDomainService.queryDetailByBatchId(deliveryBatchEntity.getId());

            //过滤城配点位
            List<DeliverySiteEntity> sendDeliverySiteList = deliverySitesWithBatch.stream()
                    .filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteId(), siteEntity.getId())).collect(Collectors.toList());
            //过滤没有点位的批次
            if(sendDeliverySiteList.size() == 0){
                continue;
            }
            deliveryBatchEntity.setDeliverySiteList(sendDeliverySiteList);
            //重量、体积、价格、店铺数计算
            deliveryBatchEntity.batchParamCalculate();

            List<Long> deliverySiteIds = deliverySitesWithBatch.stream().map(DeliverySiteEntity::getId).collect(Collectors.toList());
            List<ExpenseEntity> expenseEntities = expenseRepository.queryListWithDetail(ExpenseQuery.builder().deliverySiteIds(deliverySiteIds).build());
            //过滤状态是成功的
            expenseEntities = expenseEntities.stream()
                    .filter(expenseEntity -> Objects.equals(expenseEntity.getStatus(), ExpenseEnums.State.SUCCESS_PROCESS.getValue()))
                    .collect(Collectors.toList());
            //打车点位数
            List<ExpenseEntity> taxiExpenseList = expenseEntities.stream().filter(expenseEntity ->
                    Objects.equals(expenseEntity.getType(), ExpenseEnums.Type.TRAFFIC_FEE.getValue()) ||
                            Objects.equals(expenseEntity.getType(), ExpenseEnums.Type.BUY_TRAFFIC_FEE.getValue())).collect(Collectors.toList());
            List<ExpenseDetailEntity> taxiExpenseDetail = taxiExpenseList.stream().map(ExpenseEntity::getExpenseDetailEntityList).flatMap(Collection::stream).collect(Collectors.toList());
            //打车费
            BigDecimal taxiMoney = taxiExpenseDetail.stream().filter(detail -> Objects.equals(detail.getType(), ExpenseEnums.Type.TRAFFIC_FEE.getValue()))
                    .map(ExpenseDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            //帮采费
            List<ExpenseEntity> buyExpenseList = expenseEntities.stream().filter(expenseEntity ->
                    Objects.equals(expenseEntity.getType(), ExpenseEnums.Type.BUY_FEE.getValue()) ||
                            Objects.equals(expenseEntity.getType(), ExpenseEnums.Type.BUY_TRAFFIC_FEE.getValue())).collect(Collectors.toList());
            List<ExpenseDetailEntity> buyExpenseDetail = buyExpenseList.stream().map(ExpenseEntity::getExpenseDetailEntityList).flatMap(Collection::stream).collect(Collectors.toList());

            BigDecimal buyMoney = buyExpenseDetail.stream().filter(detail -> Objects.equals(detail.getType(), ExpenseEnums.Type.BUY_FEE.getValue()))
                    .map(ExpenseDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            deliveryBatchEntity.setTaxiSize(taxiExpenseList.size());
            deliveryBatchEntity.setSkuNum(skuNum);
            deliveryBatchEntity.setTaxiMoney(taxiMoney);
            deliveryBatchEntity.setBuyMoney(buyMoney);

            deliveryBatchEntities.add(deliveryBatchEntity);
        }

        return DubboResponse.getOK(deliveryBatchEntities.stream().map(DeliveryBatchDTOConverter::DeliveryBatchEntity2Dto).collect(Collectors.toList()));
    }

    @Override
    public DubboResponse<List<DeliverySectionDTO>> queryDeliverySection(DeliverySectionQuery deliverySectionQuery) {
        Long batchId = deliverySectionQuery.getBatchId();
        LocalDateTime finishDeliveryTime = deliverySectionQuery.getFinishDeliveryTime();
        Integer type = deliverySectionQuery.getType();

        TmsAssert.notNull(batchId, ErrorCodeEnum.PARAM_NOT_NULL, "batchId");
        if(Objects.equals(DeliverySectionEnums.Type.FINISH_SEND.getValue(),type)){
            TmsAssert.notNull(finishDeliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "finishDeliveryTime");
        }
        TmsAssert.notNull(type, ErrorCodeEnum.PARAM_NOT_NULL, "type");

        DeliverySiteQuery deliverySiteQuery = new DeliverySiteQuery();
        deliverySiteQuery.setBatchId(batchId);
        if (Objects.equals(DeliverySectionEnums.Type.FINISH_SEND.getValue(), type)) {
            deliverySiteQuery.setSignInTime(finishDeliveryTime);
        }
        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryList(deliverySiteQuery);

        List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryListWithSite(DeliverySectionQuery.builder()
                .batchId(batchId)
                .type(deliverySectionQuery.getType())
                .endSiteIds(deliverySiteEntities.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()))
                .build());

        // 设置起点、终点的归属区域id
        Map<Long, Integer> siteIdToAdCodeMsgIdMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(deliverySiteEntities)){
            siteIdToAdCodeMsgIdMap = deliverySiteEntities.stream()
                    .filter(e -> e.getAdCodeMsgId() != null)
                    .collect(Collectors.toMap(DeliverySiteEntity::getSiteId, DeliverySiteEntity::getAdCodeMsgId, (o, n) -> n));
        }

        List<DeliverySectionDTO> deliverySectionDTOList = deliverySectionEntityList.stream().map(DeliverySectionDTOConverter::en2Dto).collect(Collectors.toList());
        for (DeliverySectionDTO deliverySectionDTO : deliverySectionDTOList) {
            if(deliverySectionDTO.getBeginSite() != null){
                Integer beginSiteAdCodeMsgId = siteIdToAdCodeMsgIdMap.get(deliverySectionDTO.getBeginSite().getId());
                deliverySectionDTO.setAdCodeMsgIdForBeginSite(beginSiteAdCodeMsgId);
            }
            if(deliverySectionDTO.getEndSite() != null){
                Integer endSiteAdCodeMsgId = siteIdToAdCodeMsgIdMap.get(deliverySectionDTO.getEndSite().getId());
                deliverySectionDTO.setAdCodeMsgIdForEndSite(endSiteAdCodeMsgId);
            }
        }

        return DubboResponse.getOK(deliverySectionDTOList);
    }

    /*@PostConstruct*/
    public void test(){
        DubboResponse<List<DeliveryBatchDTO>> listDubboResponse = queryBatch(DeliverySectionQuery.builder().deliveryTime(
                LocalDate.of(2023, 03,15)).storeNo("1").build());
        /*Long batchId = deliverySectionQuery.getBatchId();
        LocalDateTime finishDeliveryTime = deliverySectionQuery.getFinishDeliveryTime();
        Integer type = deliverySectionQuery.getType();*/
       /* DubboResponse<List<DeliverySectionDTO>> listDubboResponse = queryDeliverySection(DeliverySectionQuery.builder().batchId(3779L)
                .finishDeliveryTime(LocalDateTime.of(2022, 12, 13, 15, 00))
                .type(1)
                .build());*/

        System.out.println(JSON.toJSONString(listDubboResponse));
    }
}
