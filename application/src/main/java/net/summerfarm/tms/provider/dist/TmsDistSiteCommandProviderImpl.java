package net.summerfarm.tms.provider.dist;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.SiteService;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.client.dist.provider.TmsDistSiteCommandProvider;
import net.summerfarm.tms.client.dist.req.DistSiteCreateCommandReq;
import net.summerfarm.tms.client.dist.resp.DistSiteResp;
import net.summerfarm.tms.dao.TmsDistSite;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.local.base.site.SiteDtoConverter;
import net.summerfarm.tms.provider.dist.converter.DistSiteDubboConverter;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * Description:点位操作类 <br/>
 * date: 2024/8/2 17:59<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class TmsDistSiteCommandProviderImpl implements TmsDistSiteCommandProvider {

    @Resource
    private SiteService siteService;
    @Resource
    private SiteRepository siteRepository;

    @Override
    public DubboResponse<DistSiteResp> createDistSiteForPurchase(@Valid DistSiteCreateCommandReq req) {
        SiteDTO siteDTO = new SiteDTO();
        siteDTO.setProvince(req.getProvince());
        siteDTO.setCity(req.getCity());
        siteDTO.setArea(req.getArea());
        siteDTO.setAddress(req.getAddress());
        siteDTO.setName(req.getName());
        siteDTO.setPhone(req.getPhone());
        siteDTO.setPoi(req.getPoi());
        siteDTO.setContactPerson(req.getContactPerson());
        siteDTO.setCreator(req.getCreatorId());
        siteDTO.setType(TmsSiteTypeEnum.PURCHASE_ADDRESS.getCode());

        /*SiteEntity siteEntity = siteRepository.queryByTypeAndDetailAddress(siteDTO.getType(), siteDTO.getProvince(), siteDTO.getCity(), siteDTO.getArea(), siteDTO.getAddress());
        if(siteEntity != null){
            return DubboResponse.getOK(DistSiteDubboConverter.distSiteDTOToResp(SiteDtoConverter.entity2Dto(siteEntity)));
        }*/
        TmsResult<SiteDTO> siteDTOTmsResult = siteService.siteAdd(siteDTO);

        return DubboResponse.getOK(DistSiteDubboConverter.distSiteDTOToResp(siteDTOTmsResult.getData()));
    }
}
