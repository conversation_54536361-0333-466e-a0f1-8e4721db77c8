package net.summerfarm.tms.provider.path;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.path.PathDomainService;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.client.path.provider.PathQueryProvider;
import net.summerfarm.tms.client.path.req.PathRouteQueryReq;
import net.summerfarm.tms.client.path.req.PathSiteReq;
import net.summerfarm.tms.client.path.resp.PathRouteResp;
import net.summerfarm.tms.dist.query.SiteTypeDetailAddressBusinessNoQuery;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * 路线相关查询服务
 * date: 2025/7/3 15:50<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@DubboService
public class PathQueryProviderImpl implements PathQueryProvider {

    @Resource
    private SiteRepository siteRepository;
    @Resource
    private PathDomainService pathDomainService;

    /**
     * 路线路由查询
     * @param pathRouteQueryReq 路线查询请求
     * @return 路线路由查询结果
     */
    @Override
    public DubboResponse<PathRouteResp> queryIsHaveRoute(@Valid PathRouteQueryReq pathRouteQueryReq) {
        // 参数校验
        this.checkPathRouteQueryReq(pathRouteQueryReq);
        PathSiteReq beginSite = pathRouteQueryReq.getBeginSite();

        // 先查询起点、终点的点位ID
        SiteEntity beginSiteEntity = siteRepository.querySite(SiteTypeDetailAddressBusinessNoQuery.builder()
                .type(beginSite.getType())
                .provice(beginSite.getProvince())
                .city(beginSite.getCity())
                .area(beginSite.getArea())
                .address(beginSite.getAddress())
                .phone(beginSite.getPhone())
                .name(beginSite.getName())
                .outBusinessNo(Objects.equals(beginSite.getType(), SiteTypeEnum.cust.getCode()) ? null : beginSite.getOutBusinessNo())
                .build());

        PathSiteReq endSite = pathRouteQueryReq.getEndSite();
        SiteEntity endSiteEntity = siteRepository.querySite(SiteTypeDetailAddressBusinessNoQuery.builder()
                .type(endSite.getType())
                .provice(endSite.getProvince())
                .city(endSite.getCity())
                .area(endSite.getArea())
                .address(endSite.getAddress())
                .phone(endSite.getPhone())
                .name(endSite.getName())
                .outBusinessNo(Objects.equals(endSite.getType(), SiteTypeEnum.cust.getCode()) ? null : endSite.getOutBusinessNo())
                .build());

        // 从起点到终点是否存在路由
        List<TmsPathEntity> matchPathList = pathDomainService.findPathByBeginEndSite(endSiteEntity, beginSiteEntity);
        PathRouteResp pathRouteResp = new PathRouteResp();
        pathRouteResp.setHavePathRoute(!CollectionUtil.isEmpty(matchPathList));
        return DubboResponse.getOK(pathRouteResp);
    }

    /**
     * 参数校验
     * @param pathRouteQueryReq 路线查询请求
     */
    private void checkPathRouteQueryReq(@Valid PathRouteQueryReq pathRouteQueryReq) {
        PathSiteReq beginSite = pathRouteQueryReq.getBeginSite();
        PathSiteReq endSite = pathRouteQueryReq.getEndSite();

        // 如果类型是城配仓、库存仓、必须要传outBusinessNo
        if (beginSite.getType() == TmsSiteTypeEnum.STORE.getCode() || beginSite.getType() == TmsSiteTypeEnum.WAREHOUSE.getCode()) {
            if (beginSite.getOutBusinessNo() == null) {
                throw new TmsRuntimeException("城配仓、库存仓类型必须要传outBusinessNo");
            }
        }
        if (endSite.getType() == TmsSiteTypeEnum.STORE.getCode() || endSite.getType() == TmsSiteTypeEnum.WAREHOUSE.getCode()) {
            if (endSite.getOutBusinessNo() == null) {
                throw new TmsRuntimeException("城配仓、库存仓类型必须要传outBusinessNo");
            }
        }

        // 如果是客户类型。省、市、详细地址、联系人、手机号必传
        if (beginSite.getType() == TmsSiteTypeEnum.CUSTOMER.getCode()) {
            if (beginSite.getProvince() == null || beginSite.getCity() == null || beginSite.getAddress() == null || beginSite.getName() == null || beginSite.getPhone() == null) {
                throw new TmsRuntimeException("客户类型必须要传省、市、详细地址、联系人、手机号");
            }
        }
        if (endSite.getType() == TmsSiteTypeEnum.CUSTOMER.getCode()) {
            if (endSite.getProvince() == null || endSite.getCity() == null || endSite.getAddress() == null || endSite.getName() == null || endSite.getPhone() == null) {
                throw new TmsRuntimeException("客户类型必须要传省、市、详细地址、联系人、手机号");
            }
        }
    }
}
