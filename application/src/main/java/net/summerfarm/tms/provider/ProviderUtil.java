package net.summerfarm.tms.provider;

import net.summerfarm.tms.base.TmsResult;
import net.xianmu.common.result.DubboResponse;

/**
 * <AUTHOR>
 */
public class ProviderUtil {
    public static <T> DubboResponse<T> convert2DubboRes(TmsResult<T> result) {
        if (result.isSuccess()) {
            return DubboResponse.getOK(result.getData());
        } else {
            return DubboResponse.getError(result.getErrCode(), result.getErrorMessage());
        }
    }
}
