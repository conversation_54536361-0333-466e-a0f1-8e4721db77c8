package net.summerfarm.tms.message.consumer.delivery;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.delivery.DeliveryBatchCommandService;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 完成排线蚁群算法路线处理<br/>
 * date: 2024/1/11 11:49<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqListener(topic = MqConstants.Topic.TMS_PATH, tag = MqConstants.Tag.TAG_CALC_PATH_DISTANCE,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_COMMON,
        consumeThreadMin = 1,
        consumeThreadMax = 2)
public class DeliveryBatchOrToolsAlgorithmConsumer extends AbstractMqListener<CalcTmsPathDistanceMessage> {

    @Resource
    private DeliveryBatchCommandService deliveryBatchCommandService;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void process(CalcTmsPathDistanceMessage calcTmsPathDistanceMessage) {
        log.info("接受到TMS ortools排线计算公里数逻辑处理:{}", JSON.toJSONString(calcTmsPathDistanceMessage));
        if (CollectionUtils.isEmpty(calcTmsPathDistanceMessage.getWaypointsInputList())) {
            log.error("路段信息为空,请检查");
            return;
        }
        if (Objects.isNull(calcTmsPathDistanceMessage.getBatchId())) {
            log.error("批次id信息为空请检查");
            return;
        }
        if (Objects.isNull(calcTmsPathDistanceMessage.getType())) {
            log.error("计算距离公里数类型为空，请检查参数");
            return;
        }
        // 只有完成排线才走算法
        if(!Objects.equals(calcTmsPathDistanceMessage.getType(), DeliverySectionEnums.Type.complete_path)){
            return;
        }
        redisUtil.executeCallableByJustTryOneTime(RedisConstants.PathOpearation.TMS_CALC_PATH_DISTANCE, 5L, TimeUnit.MINUTES, () ->{
                    try {
                        deliveryBatchCommandService.calcPathDistanceByOrTools(calcTmsPathDistanceMessage);
                    } catch (Exception e) {
                        log.error("OrTools 计算路径规划异常",e);
                    }
                    return true;
                });
    }
}
