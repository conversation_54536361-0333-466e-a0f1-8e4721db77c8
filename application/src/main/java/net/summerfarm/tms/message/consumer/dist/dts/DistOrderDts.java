package net.summerfarm.tms.message.consumer.dist.dts;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Map;

/**
 * 承运单DTS
 */
@Data
public class DistOrderDts {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，
     * 100：调拨，
     * 101：采购，
     * 102：销售出库，
     * 103，出样出库，
     * 104，补货出库，
     * 105，自提销售
     * 150，外单-干线
     * 151，外单-干配
     * 200：鲜沐商城，
     * 201：鲜沐售后
     * 202：鲜沐样品
     * 203：鲜沐商城省心送
     * 210：Saas商城
     * 211：Saas售后
     * 220：外单-城配
     */
    private Integer source;

    /**
     * 10:待排线 18:已关闭 19:取消 30:配送中 40:已完成
     */
    private Integer state;

    /**
     * 外部租户号
     */
    private String outerTenantId;

    /**
     * 外部品牌名
     */
    private String outerBrandName;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部联系人ID
     */
    private String outerContactId;


    /**
     * 期望开始时间
     */
    private LocalDateTime expectBeginTime;

    public DistOrderDts(Map<String, String> fields){
        if (fields.containsKey("id")) {
            this.id = Long.valueOf(fields.get("id"));
        }
        if (fields.containsKey("outer_order_id")) {
            this.outerOrderId = fields.get("outer_order_id");
        }
        if (fields.containsKey("source")) {
            this.source = Integer.valueOf(fields.get("source"));
        }
        if (fields.containsKey("state")) {
            this.state = Integer.valueOf(fields.get("state"));
        }
        if (fields.containsKey("outer_tenant_id")) {
            this.outerTenantId = fields.get("outer_tenant_id");
        }
        if (fields.containsKey("outer_brand_name")) {
            this.outerBrandName = fields.get("outer_brand_name");
        }
        if (fields.containsKey("outer_client_id")) {
            this.outerClientId = fields.get("outer_client_id");
        }
        if (fields.containsKey("outer_client_name")) {
            this.outerClientName = fields.get("outer_client_name");
        }
        if (fields.containsKey("outer_contact_id")) {
            this.outerContactId = fields.get("outer_contact_id");
        }
        if (fields.containsKey("expect_begin_time")) {
            try {
                this.expectBeginTime = LocalDateTime.parse(fields.get("expect_begin_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.0"));
            } catch (DateTimeParseException e) {
                this.expectBeginTime = LocalDateTime.parse(fields.get("expect_begin_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        if (fields.containsKey("create_time")) {
            try {
                this.createTime = LocalDateTime.parse(fields.get("create_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.0"));
            } catch (DateTimeParseException e) {
                this.createTime = LocalDateTime.parse(fields.get("create_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
        if (fields.containsKey("update_time")) {
            try {
                this.updateTime = LocalDateTime.parse(fields.get("update_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.0"));
            } catch (DateTimeParseException e) {
                this.updateTime = LocalDateTime.parse(fields.get("update_time"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }
    }
}
