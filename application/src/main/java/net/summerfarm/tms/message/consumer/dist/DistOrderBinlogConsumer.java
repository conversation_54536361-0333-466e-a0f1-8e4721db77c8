package net.summerfarm.tms.message.consumer.dist;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.gray.old2new.sync.enums.DtsModelTypeEnum;
import net.summerfarm.tms.gray.old2new.sync.model.DtsModel;
import net.summerfarm.tms.message.consumer.dist.converter.TrunkDistOrderStateMessageConverter;
import net.summerfarm.tms.message.consumer.dist.dts.DistOrderDts;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Description: 承运单监听<br/>
 * date: 2024/4/28 10:31<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqOrderlyListener(topic = MqConstants.Topic.MYSQL_BINLOG_ORDERLY,
        tag = "tms_dist_order",
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_BINLOG_DIST_ORDER_ORDERLY,
        maxReconsumeTimes = 5)
public class DistOrderBinlogConsumer extends AbstractMqListener<DtsModel> {

    @Resource
    private EventBusService eventBusService;

    @Override
    public void process(DtsModel dtsModel) {

        log.info("DistOrderBinlogConsumer收到消息，{}", JSON.toJSONString(dtsModel));
        DtsModelTypeEnum dtsModelTypeEnum = DtsModelTypeEnum.getDtsModelTypeByType(dtsModel.getType());
        if (dtsModelTypeEnum == null) {
            log.info("非法DML无法解析,msgKey：{},丢弃消息", dtsModel.getMsgKey());
            return;
        }
        dtsModel.setDtsModelTypeEnum(dtsModelTypeEnum);
        //批量DML操作解析
        List<DtsModel> dtsModels = dtsModel.convertModelList();
        for (DtsModel model : dtsModels) {
            DistOrderDts oldData;
            DistOrderDts sourceData;
            switch (model.getDtsModelTypeEnum()) {
                case DELETE:
                    oldData = new DistOrderDts(model.getOld().get(0));
                    //过滤干线外单来源
                    if (DistOrderSourceEnum.getTrunkOuterCode().contains(oldData.getSource())){
                        log.error("不应该呀，干线外单不应该存在删除数据的操作的");
                        break;
                    }
                    break;
                case UPDATE:
                    sourceData = new DistOrderDts(model.getData().get(0));
                    oldData = new DistOrderDts(model.getOld().get(0));
                    //旧状态
                    Integer oldState = oldData.getState();
                    //新状态
                    Integer newState = sourceData.getState();
                    //过滤干线外单来源
                    if (!DistOrderSourceEnum.getTrunkOuterCode().contains(sourceData.getSource())){
                        break;
                    }
                    //状态一样不需要处理
                    if(Objects.equals(oldState,newState)){
                        break;
                    }
                    //发送消息
                    eventBusService.trunkDistOrderState(TrunkDistOrderStateMessageConverter.distOrderDts2Msg(sourceData));
                    break;
                case INSERT:
                    sourceData = new DistOrderDts(dtsModel.getData().get(0));
                    //过滤干线外单来源
                    if (!DistOrderSourceEnum.getTrunkOuterCode().contains(sourceData.getSource())){
                        break;
                    }
                    //发送消息
                    eventBusService.trunkDistOrderState(TrunkDistOrderStateMessageConverter.distOrderDts2Msg(sourceData));
                    break;
                default:
                    log.info("无需处理的DML操作：{},msgKey：{},丢弃消息", dtsModel.getType(), dtsModel.getMsgKey());
                    break;
            }
        }
    }
}
