package net.summerfarm.tms.message.consumer.dist.converter;

import net.summerfarm.tms.message.consumer.dist.dts.DistOrderDts;
import net.summerfarm.tms.message.out.TrunkDistOrderStateMessage;

/**
 * Description: 干线委托单消息转换类<br/>
 * date: 2024/4/28 15:54<br/>
 *
 * <AUTHOR> />
 */
public class TrunkDistOrderStateMessageConverter {

    public static TrunkDistOrderStateMessage distOrderDts2Msg(DistOrderDts dts){
        if(dts == null){
            return null;
        }
        return TrunkDistOrderStateMessage.builder()
                .outerBrandName(dts.getOuterBrandName())
                .outerClientId(dts.getOuterClientId())
                .outerClientName(dts.getOuterClientName())
                .outerContactId(dts.getOuterContactId())
                .outerOrderId(dts.getOuterOrderId())
                .outerTenantId(dts.getOuterTenantId())
                .id(dts.getId())
                .state(dts.getState())
                .source(dts.getSource())
                .updateTime(dts.getUpdateTime())
                .createTime(dts.getCreateTime())
                .expectBeginTime(dts.getExpectBeginTime())
                .build();
    }
}
