package net.summerfarm.tms.message.consumer.performance;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.MqConstants;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.message.in.DeliveryPerformanceReviewAppealCreateMessage;
import net.summerfarm.tms.performance.domain.DeliveryPerformanceReviewDetailAppealCommandDomainService;
import net.summerfarm.tms.performance.entity.DeliveryPerformanceReviewDetailAppealEntity;
import net.summerfarm.tms.track.param.command.TmsTrackLogCommandParam;
import net.summerfarm.tms.track.service.TmsTrackLogCommandDomainService;
import net.summerfarm.tms.util.RedisUtil;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.annotation.MqPrimary;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static net.summerfarm.tms.enums.TmsTrackLogEnums.BizType.APPEAL;

/**
 * Description: 履约申诉创建<br/>
 * date: 2024/9/4 15:19<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@MqPrimary
@MqListener(topic = MqConstants.Topic.TMS_DELIVERY, tag = MqConstants.Tag.TAG_TMS_DELIVERY_PERFORMANCE_REVIEW_APPEAL_CREATE,
        consumerGroup = MqConstants.ConsumeGroup.GID_TMS_DELIVERY_COMMON,
        consumeThreadMin = 1,
        consumeThreadMax = 2,
        maxReconsumeTimes=5)
public class DeliveryPerformanceReviewAppealCreateConsumer extends AbstractMqListener<DeliveryPerformanceReviewAppealCreateMessage> {

    @Resource
    private DeliveryPerformanceReviewDetailAppealCommandDomainService deliveryPerformanceReviewDetailAppealCommandDomainService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private TmsTrackLogCommandDomainService tmsTrackLogCommandDomainService;

    @Override
    public void process(DeliveryPerformanceReviewAppealCreateMessage msg) {
        Long performanceReviewTaskId = msg.getPerformanceReviewTaskId();
        try {
            redisUtil.executeCallableByJustTryOneTime(RedisConstants.Performance.DELIVERY_PERFORMANCE_REVIEW_DETAIL_APPEAL_CREATE + performanceReviewTaskId,
                    2L, TimeUnit.HOURS, () ->{
                        List<DeliveryPerformanceReviewDetailAppealEntity> deliveryPerformanceReviewDetailAppealEntities = deliveryPerformanceReviewDetailAppealCommandDomainService.create(performanceReviewTaskId);

                        LocalDateTime now = LocalDateTime.now();
                        List<TmsTrackLogCommandParam> tmsTrackLogCommandParams = new ArrayList<>();
                        deliveryPerformanceReviewDetailAppealEntities.forEach(appealEntity -> {
                            TmsTrackLogCommandParam param = new TmsTrackLogCommandParam();
                            // 日志记录
                            param.setBizNo(appealEntity.getId());
                            param.setBizType(APPEAL.getValue());
                            param.setActionName("记录生成");
                            param.setCreateTime(now);
                            param.setOperater("系统");
                            tmsTrackLogCommandParams.add(param);
                        });
                        try {
                            tmsTrackLogCommandDomainService.batchInsert(tmsTrackLogCommandParams);
                        } catch (Exception e) {
                            log.error("插入日志失败,入参:{}", JSON.toJSONString(tmsTrackLogCommandParams), e);
                        }
                        return 1;
            });
        } catch (Exception e) {
            throw e;
        }
    }
}
