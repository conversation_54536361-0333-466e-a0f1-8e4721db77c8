package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.assembler;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateBelongSaveCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateBelongUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.TmsDeliveryNoteTemplateBelongQueryInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateBelongVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
public class TmsDeliveryNoteTemplateBelongAssembler {

    private TmsDeliveryNoteTemplateBelongAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static TmsDeliveryNoteTemplateBelongQueryParam toTmsDeliveryNoteTemplateBelongQueryParam(TmsDeliveryNoteTemplateBelongQueryInput tmsDeliveryNoteTemplateBelongQueryInput) {
        if (tmsDeliveryNoteTemplateBelongQueryInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateBelongQueryParam tmsDeliveryNoteTemplateBelongQueryParam = new TmsDeliveryNoteTemplateBelongQueryParam();
        tmsDeliveryNoteTemplateBelongQueryParam.setId(tmsDeliveryNoteTemplateBelongQueryInput.getId());
        tmsDeliveryNoteTemplateBelongQueryParam.setCreateTime(tmsDeliveryNoteTemplateBelongQueryInput.getCreateTime());
        tmsDeliveryNoteTemplateBelongQueryParam.setUpdateTime(tmsDeliveryNoteTemplateBelongQueryInput.getUpdateTime());
        tmsDeliveryNoteTemplateBelongQueryParam.setDeliveryNoteTemplateId(tmsDeliveryNoteTemplateBelongQueryInput.getDeliveryNoteTemplateId());
        tmsDeliveryNoteTemplateBelongQueryParam.setTenantId(tmsDeliveryNoteTemplateBelongQueryInput.getTenantId());
        tmsDeliveryNoteTemplateBelongQueryParam.setScopeType(tmsDeliveryNoteTemplateBelongQueryInput.getScopeType());
        tmsDeliveryNoteTemplateBelongQueryParam.setScopeBusinessId(tmsDeliveryNoteTemplateBelongQueryInput.getScopeBusinessId());
        tmsDeliveryNoteTemplateBelongQueryParam.setScopeBusinessName(tmsDeliveryNoteTemplateBelongQueryInput.getScopeBusinessName());
        tmsDeliveryNoteTemplateBelongQueryParam.setPageIndex(tmsDeliveryNoteTemplateBelongQueryInput.getPageIndex());
        tmsDeliveryNoteTemplateBelongQueryParam.setPageSize(tmsDeliveryNoteTemplateBelongQueryInput.getPageSize());
        return tmsDeliveryNoteTemplateBelongQueryParam;
    }





    public static TmsDeliveryNoteTemplateBelongCommandParam buildCreateParam(TmsDeliveryNoteTemplateBelongSaveCommandInput tmsDeliveryNoteTemplateBelongCommandInput) {
        if (tmsDeliveryNoteTemplateBelongCommandInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateBelongCommandParam tmsDeliveryNoteTemplateBelongCommandParam = new TmsDeliveryNoteTemplateBelongCommandParam();
        tmsDeliveryNoteTemplateBelongCommandParam.setTenantId(tmsDeliveryNoteTemplateBelongCommandInput.getTenantId());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeType(tmsDeliveryNoteTemplateBelongCommandInput.getScopeType());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeBusinessId(tmsDeliveryNoteTemplateBelongCommandInput.getScopeBusinessId());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeBusinessName(tmsDeliveryNoteTemplateBelongCommandInput.getScopeBusinessName());
        tmsDeliveryNoteTemplateBelongCommandParam.setAppSource(tmsDeliveryNoteTemplateBelongCommandInput.getAppSource());

        return tmsDeliveryNoteTemplateBelongCommandParam;
    }


    public static TmsDeliveryNoteTemplateBelongCommandParam buildUpdateParam(TmsDeliveryNoteTemplateBelongUpdateCommandInput tmsDeliveryNoteTemplateBelongUpdateCommandInput) {
        if (tmsDeliveryNoteTemplateBelongUpdateCommandInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateBelongCommandParam tmsDeliveryNoteTemplateBelongCommandParam = new TmsDeliveryNoteTemplateBelongCommandParam();
        tmsDeliveryNoteTemplateBelongCommandParam.setTenantId(tmsDeliveryNoteTemplateBelongUpdateCommandInput.getTenantId());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeType(tmsDeliveryNoteTemplateBelongUpdateCommandInput.getScopeType());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeBusinessId(tmsDeliveryNoteTemplateBelongUpdateCommandInput.getScopeBusinessId());
        tmsDeliveryNoteTemplateBelongCommandParam.setScopeBusinessName(tmsDeliveryNoteTemplateBelongUpdateCommandInput.getScopeBusinessName());
        tmsDeliveryNoteTemplateBelongCommandParam.setAppSource(tmsDeliveryNoteTemplateBelongUpdateCommandInput.getAppSource());
        return tmsDeliveryNoteTemplateBelongCommandParam;
    }




// ------------------------------- response ----------------------------

    public static List<TmsDeliveryNoteTemplateBelongVO> toTmsDeliveryNoteTemplateBelongVOList(List<TmsDeliveryNoteTemplateBelongEntity> tmsDeliveryNoteTemplateBelongEntityList) {
        if (tmsDeliveryNoteTemplateBelongEntityList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryNoteTemplateBelongVO> tmsDeliveryNoteTemplateBelongVOList = new ArrayList<>();
        for (TmsDeliveryNoteTemplateBelongEntity tmsDeliveryNoteTemplateBelongEntity : tmsDeliveryNoteTemplateBelongEntityList) {
            tmsDeliveryNoteTemplateBelongVOList.add(toTmsDeliveryNoteTemplateBelongVO(tmsDeliveryNoteTemplateBelongEntity));
        }
        return tmsDeliveryNoteTemplateBelongVOList;
}


   public static TmsDeliveryNoteTemplateBelongVO toTmsDeliveryNoteTemplateBelongVO(TmsDeliveryNoteTemplateBelongEntity tmsDeliveryNoteTemplateBelongEntity) {
       if (tmsDeliveryNoteTemplateBelongEntity == null) {
            return null;
       }
       TmsDeliveryNoteTemplateBelongVO tmsDeliveryNoteTemplateBelongVO = new TmsDeliveryNoteTemplateBelongVO();
       tmsDeliveryNoteTemplateBelongVO.setDeliveryNoteTemplateId(tmsDeliveryNoteTemplateBelongEntity.getDeliveryNoteTemplateId());
       tmsDeliveryNoteTemplateBelongVO.setTenantId(tmsDeliveryNoteTemplateBelongEntity.getTenantId());
       tmsDeliveryNoteTemplateBelongVO.setScopeType(tmsDeliveryNoteTemplateBelongEntity.getScopeType());
       tmsDeliveryNoteTemplateBelongVO.setScopeBusinessId(tmsDeliveryNoteTemplateBelongEntity.getScopeBusinessId());
       tmsDeliveryNoteTemplateBelongVO.setScopeBusinessName(tmsDeliveryNoteTemplateBelongEntity.getScopeBusinessName());
       return tmsDeliveryNoteTemplateBelongVO;
   }

}
