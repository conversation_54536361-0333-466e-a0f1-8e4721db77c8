package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.converter.PageInfoConverter;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;
import net.summerfarm.tms.deliveryNoteTemplate.repository.TmsDeliveryNoteTemplateBelongQueryRepository;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.assembler.TmsDeliveryNoteTemplateAssembler;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateSaveCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateUseStateUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.TmsDeliveryNoteTemplateQueryDetailInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.TmsDeliveryNoteTemplateQueryInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateDetailVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateVO;
import net.summerfarm.tms.service.deliveryNoteTemplate.TmsDeliveryNoteTemplateCommandService;
import net.summerfarm.tms.service.deliveryNoteTemplate.TmsDeliveryNoteTemplateQueryService;
import net.xianmu.common.result.CommonResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;


/**
 * @Title 配送单模版
 * @Description 配送单模版功能模块
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 */
@RestController
@RequestMapping(value="/tms-new/tmsDeliveryNoteTemplate")
public class TmsDeliveryNoteTemplateController{

	@Autowired
	private TmsDeliveryNoteTemplateCommandService tmsDeliveryNoteTemplateCommandService;
	@Autowired
	private TmsDeliveryNoteTemplateQueryService tmsDeliveryNoteTemplateQueryService;
	@Autowired
	private TmsDeliveryNoteTemplateBelongQueryRepository tmsDeliveryNoteTemplateBelongQueryRepository;

	/**
	 * 配送单模版列表
	 * @return TmsDeliveryNoteTemplateVO
	 */
	@PostMapping(value="/query/page")
	public CommonResult<PageInfo<TmsDeliveryNoteTemplateVO>> getPage(@RequestBody TmsDeliveryNoteTemplateQueryInput input){
		PageInfo<TmsDeliveryNoteTemplateEntity> page = tmsDeliveryNoteTemplateQueryService.getPage(input);
		return CommonResult.ok(PageInfoConverter.toPageResp(page, TmsDeliveryNoteTemplateAssembler::toTmsDeliveryNoteTemplateVO));
	}

	/**
	* 获取详情
	* @return TmsDeliveryNoteTemplateVO
	*/
	@PostMapping(value = "/query/detail")
	public CommonResult<TmsDeliveryNoteTemplateDetailVO> detail(@RequestBody @Valid TmsDeliveryNoteTemplateQueryDetailInput input){
		// 获取配送单配置详情
		TmsDeliveryNoteTemplateEntity detailEntity = tmsDeliveryNoteTemplateQueryService.getDetail(input.getId());
		if(detailEntity == null){
			throw new TmsRuntimeException("不存在此配送单模版");
		}
		// 查询配送单归属
		List<TmsDeliveryNoteTemplateBelongEntity> tmsDeliveryNoteTemplateBelongEntities = tmsDeliveryNoteTemplateBelongQueryRepository.selectByCondition(TmsDeliveryNoteTemplateBelongQueryParam.builder()
				.deliveryNoteTemplateId(detailEntity.getId()).build());
		// 转换
		detailEntity.setTmsDeliveryNoteTemplateBelongEntities(tmsDeliveryNoteTemplateBelongEntities);

		return CommonResult.ok(TmsDeliveryNoteTemplateAssembler.toTmsDeliveryNoteTemplateDetailVO(detailEntity));
	}


	/**
	 * 新增
	 * @return TmsDeliveryNoteTemplateVO
	 */
	@PostMapping(value = "/upsert/insert")
	public CommonResult<Void> insert(@RequestBody TmsDeliveryNoteTemplateSaveCommandInput input) {
		tmsDeliveryNoteTemplateCommandService.insert(input);
		return CommonResult.ok();
	}

	/**
	 * 更新配送单使用状态
	 * @return TmsDeliveryNoteTemplateVO
	 */
	@PostMapping(value = "/upsert/use-state")
	public CommonResult<Void> useStateUpdate(@RequestBody TmsDeliveryNoteTemplateUseStateUpdateCommandInput input) {
		tmsDeliveryNoteTemplateCommandService.useStateUpdate(input);
		return CommonResult.ok();
	}

	/**
	 * 修改
	 */
	@PostMapping(value = "/upsert/update")
	public CommonResult<Void> update(@RequestBody TmsDeliveryNoteTemplateUpdateCommandInput input){
		tmsDeliveryNoteTemplateCommandService.update(input);
		return CommonResult.ok();
	}


}

