package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.DeliveryNotePrintInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.OrderDeliveryNotePrintInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.OrderListDeliveryNotePrintInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.SelfPickupOrderDeliveryNotePrintInput;
import net.summerfarm.tms.service.deliveryNoteTemplate.TmsDeliveryNoteTemplateQueryService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.redis.support.lock.annotation.XmLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * Description: 配送单<br/>
 * date: 2025/1/17 10:49<br/>
 *
 * <AUTHOR> />
 */
@RestController
@RequestMapping(value="/tms-new/delivery-note")
@Slf4j
public class DeliveryNoteController {

    @Autowired
    private TmsDeliveryNoteTemplateQueryService tmsDeliveryNoteTemplateQueryService;

    /**
     * 配送单导出
     * @return TmsDeliveryNoteTemplateVO
     */
    @PostMapping(value="/export-async/print-delivery-note-info")
    @XmLock(prefixKey = RedisConstants.Delivery.TMS_DELIVERY_NOTE_DOWN,key = "{input.storeNo}:{input.deliveryTime}",waitTime = 1000, message = "配送单已在导出中,请耐心等待")
    public CommonResult<Long> downloadPrintDeliveryNoteInfo(@RequestBody @Validated DeliveryNotePrintInput input) throws IOException {
        return CommonResult.ok(tmsDeliveryNoteTemplateQueryService.downloadPrintDeliveryNoteInfo(input));
    }

    /**
     * 订单配送单导出
     */
    @PostMapping(value="/export-async/order-print-delivery-note-info")
    public CommonResult<Long> downloadOrderPrintDeliveryNoteInfo(@RequestBody @Validated OrderDeliveryNotePrintInput input) throws IOException {
        return CommonResult.ok(tmsDeliveryNoteTemplateQueryService.downloadOrderPrintDeliveryNoteInfo(Arrays.asList(input.getOrderNo()),input.getPrintDirection(), null));
    }

    /**
     * 自提配送单导出
     */
    @PostMapping(value="/export-async/self-pickup-order-print-delivery-note-info")
    public CommonResult<Long> downloadSelfPickupOrderPrintDeliveryNoteInfo(@RequestBody @Validated SelfPickupOrderDeliveryNotePrintInput input) throws IOException {
        return CommonResult.ok(tmsDeliveryNoteTemplateQueryService.downloadSelfPickupOrderPrintDeliveryNoteInfo(input));
    }

    /**
     * 订单配送单导出
     */
    @PostMapping(value="/export-async/order-list-print-delivery-note-info")
    public CommonResult<Long> downloadOrderListPrintDeliveryNoteInfo(@RequestBody @Validated OrderListDeliveryNotePrintInput input) throws IOException {
        List<String> orderNos = input.getOrderNos();
        Boolean printDirection = input.getPrintDirection();
        String downLoadFileName = "配送单-" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return CommonResult.ok(tmsDeliveryNoteTemplateQueryService.downloadOrderPrintDeliveryNoteInfo(orderNos, printDirection,downLoadFileName));
    }
}
