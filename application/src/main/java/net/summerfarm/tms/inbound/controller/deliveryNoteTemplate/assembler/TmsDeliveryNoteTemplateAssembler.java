package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.assembler;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateBelongSaveCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateBelongUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateSaveCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command.TmsDeliveryNoteTemplateUpdateCommandInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query.TmsDeliveryNoteTemplateQueryInput;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateBelongVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateDetailVO;
import net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.vo.TmsDeliveryNoteTemplateVO;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
public class TmsDeliveryNoteTemplateAssembler {

    private TmsDeliveryNoteTemplateAssembler() {
        // 无需实现
    }


// ------------------------------- request ----------------------------
    public static TmsDeliveryNoteTemplateQueryParam toTmsDeliveryNoteTemplateQueryParam(TmsDeliveryNoteTemplateQueryInput tmsDeliveryNoteTemplateQueryInput) {
        if (tmsDeliveryNoteTemplateQueryInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateQueryParam tmsDeliveryNoteTemplateQueryParam = new TmsDeliveryNoteTemplateQueryParam();
        tmsDeliveryNoteTemplateQueryParam.setId(tmsDeliveryNoteTemplateQueryInput.getId());
        tmsDeliveryNoteTemplateQueryParam.setDeliveryNoteName(tmsDeliveryNoteTemplateQueryInput.getDeliveryNoteName());
        tmsDeliveryNoteTemplateQueryParam.setAppSource(tmsDeliveryNoteTemplateQueryInput.getAppSource());
        tmsDeliveryNoteTemplateQueryParam.setUseState(tmsDeliveryNoteTemplateQueryInput.getUseState());
        tmsDeliveryNoteTemplateQueryParam.setPageIndex(tmsDeliveryNoteTemplateQueryInput.getPageIndex());
        tmsDeliveryNoteTemplateQueryParam.setPageSize(tmsDeliveryNoteTemplateQueryInput.getPageSize());
        tmsDeliveryNoteTemplateQueryParam.setBelongBusinessName(tmsDeliveryNoteTemplateQueryInput.getBelongBusinessName());
        return tmsDeliveryNoteTemplateQueryParam;
    }





    public static TmsDeliveryNoteTemplateCommandParam buildCreateParam(TmsDeliveryNoteTemplateSaveCommandInput tmsDeliveryNoteTemplateSaveCommandInput) {
        if (tmsDeliveryNoteTemplateSaveCommandInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateCommandParam tmsDeliveryNoteTemplateCommandParam = new TmsDeliveryNoteTemplateCommandParam();
        tmsDeliveryNoteTemplateCommandParam.setDeliveryNoteName(tmsDeliveryNoteTemplateSaveCommandInput.getDeliveryNoteName());
        tmsDeliveryNoteTemplateCommandParam.setAppSource(tmsDeliveryNoteTemplateSaveCommandInput.getAppSource());
        tmsDeliveryNoteTemplateCommandParam.setUseState(tmsDeliveryNoteTemplateSaveCommandInput.getUseState());
        tmsDeliveryNoteTemplateCommandParam.setFrontPageStr(tmsDeliveryNoteTemplateSaveCommandInput.getFrontPageStr());
        tmsDeliveryNoteTemplateCommandParam.setShowPriceFlag(tmsDeliveryNoteTemplateSaveCommandInput.getShowPriceFlag());
        tmsDeliveryNoteTemplateCommandParam.setScopeType(tmsDeliveryNoteTemplateSaveCommandInput.getScopeType());
        tmsDeliveryNoteTemplateCommandParam.setNoShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateSaveCommandInput.getNoShowPriceTemplateOssUrl());
        tmsDeliveryNoteTemplateCommandParam.setShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateSaveCommandInput.getShowPriceTemplateOssUrl());

        List<TmsDeliveryNoteTemplateBelongSaveCommandInput> belongSaveCommandInputList = tmsDeliveryNoteTemplateSaveCommandInput.getBelongSaveCommandInputList();
        if(!CollectionUtils.isEmpty(belongSaveCommandInputList)){
            tmsDeliveryNoteTemplateCommandParam.setBelongCommandParamList(belongSaveCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongAssembler::buildCreateParam).collect(Collectors.toList()));
            tmsDeliveryNoteTemplateCommandParam.setBelongBusinessName(belongSaveCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongSaveCommandInput::getScopeBusinessName).collect(Collectors.joining(",")));
        }
        return tmsDeliveryNoteTemplateCommandParam;
    }


    public static TmsDeliveryNoteTemplateCommandParam buildUpdateParam(TmsDeliveryNoteTemplateUpdateCommandInput tmsDeliveryNoteTemplateUpdateCommandInput) {
        if (tmsDeliveryNoteTemplateUpdateCommandInput == null) {
            return null;
        }
        TmsDeliveryNoteTemplateCommandParam tmsDeliveryNoteTemplateCommandParam = new TmsDeliveryNoteTemplateCommandParam();
        tmsDeliveryNoteTemplateCommandParam.setId(tmsDeliveryNoteTemplateUpdateCommandInput.getId());
        tmsDeliveryNoteTemplateCommandParam.setDeliveryNoteName(tmsDeliveryNoteTemplateUpdateCommandInput.getDeliveryNoteName());
        tmsDeliveryNoteTemplateCommandParam.setAppSource(tmsDeliveryNoteTemplateUpdateCommandInput.getAppSource());
        tmsDeliveryNoteTemplateCommandParam.setFrontPageStr(tmsDeliveryNoteTemplateUpdateCommandInput.getFrontPageStr());
        tmsDeliveryNoteTemplateCommandParam.setShowPriceFlag(tmsDeliveryNoteTemplateUpdateCommandInput.getShowPriceFlag());
        tmsDeliveryNoteTemplateCommandParam.setScopeType(tmsDeliveryNoteTemplateUpdateCommandInput.getScopeType());
        tmsDeliveryNoteTemplateCommandParam.setNoShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateUpdateCommandInput.getNoShowPriceTemplateOssUrl());
        tmsDeliveryNoteTemplateCommandParam.setShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateUpdateCommandInput.getShowPriceTemplateOssUrl());

        List<TmsDeliveryNoteTemplateBelongUpdateCommandInput> belongUpdateCommandInputList = tmsDeliveryNoteTemplateUpdateCommandInput.getBelongUpdateCommandInput();
        if(!CollectionUtils.isEmpty(belongUpdateCommandInputList)){
            tmsDeliveryNoteTemplateCommandParam.setBelongCommandParamList(belongUpdateCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongAssembler::buildUpdateParam).collect(Collectors.toList()));
            tmsDeliveryNoteTemplateCommandParam.setBelongBusinessName(belongUpdateCommandInputList.stream().map(TmsDeliveryNoteTemplateBelongUpdateCommandInput::getScopeBusinessName).collect(Collectors.joining(",")));
        }
        return tmsDeliveryNoteTemplateCommandParam;
    }


// ------------------------------- response ----------------------------

    public static List<TmsDeliveryNoteTemplateVO> toTmsDeliveryNoteTemplateVOList(List<TmsDeliveryNoteTemplateEntity> tmsDeliveryNoteTemplateEntityList) {
        if (tmsDeliveryNoteTemplateEntityList == null) {
            return Collections.emptyList();
        }
        List<TmsDeliveryNoteTemplateVO> tmsDeliveryNoteTemplateVOList = new ArrayList<>();
        for (TmsDeliveryNoteTemplateEntity tmsDeliveryNoteTemplateEntity : tmsDeliveryNoteTemplateEntityList) {
            tmsDeliveryNoteTemplateVOList.add(toTmsDeliveryNoteTemplateVO(tmsDeliveryNoteTemplateEntity));
        }
        return tmsDeliveryNoteTemplateVOList;
}


   public static TmsDeliveryNoteTemplateVO toTmsDeliveryNoteTemplateVO(TmsDeliveryNoteTemplateEntity tmsDeliveryNoteTemplateEntity) {
       if (tmsDeliveryNoteTemplateEntity == null) {
            return null;
       }
       TmsDeliveryNoteTemplateVO tmsDeliveryNoteTemplateVO = new TmsDeliveryNoteTemplateVO();
       tmsDeliveryNoteTemplateVO.setId(tmsDeliveryNoteTemplateEntity.getId());
       tmsDeliveryNoteTemplateVO.setCreateTime(tmsDeliveryNoteTemplateEntity.getCreateTime());
       tmsDeliveryNoteTemplateVO.setUpdateTime(tmsDeliveryNoteTemplateEntity.getUpdateTime() == null ? tmsDeliveryNoteTemplateEntity.getCreateTime() : tmsDeliveryNoteTemplateEntity.getUpdateTime());
       tmsDeliveryNoteTemplateVO.setDeliveryNoteName(tmsDeliveryNoteTemplateEntity.getDeliveryNoteName());
       tmsDeliveryNoteTemplateVO.setAppSource(tmsDeliveryNoteTemplateEntity.getAppSource());
       tmsDeliveryNoteTemplateVO.setUseState(tmsDeliveryNoteTemplateEntity.getUseState());
       tmsDeliveryNoteTemplateVO.setFrontPageStr(tmsDeliveryNoteTemplateEntity.getFrontPageStr());
       tmsDeliveryNoteTemplateVO.setLastOperatorName(tmsDeliveryNoteTemplateEntity.getLastOperatorName());
       tmsDeliveryNoteTemplateVO.setBelongBusinessName(tmsDeliveryNoteTemplateEntity.getBelongBusinessName());
       tmsDeliveryNoteTemplateVO.setShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateEntity.getShowPriceTemplateOssUrl());
       tmsDeliveryNoteTemplateVO.setNoShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateEntity.getNoShowPriceTemplateOssUrl());
       tmsDeliveryNoteTemplateVO.setShowPriceFlag(tmsDeliveryNoteTemplateEntity.getShowPriceFlag());
       return tmsDeliveryNoteTemplateVO;
   }


   public static TmsDeliveryNoteTemplateDetailVO toTmsDeliveryNoteTemplateDetailVO(TmsDeliveryNoteTemplateEntity tmsDeliveryNoteTemplateEntity){
        if(tmsDeliveryNoteTemplateEntity == null){
            return null;
        }
        TmsDeliveryNoteTemplateDetailVO tmsDeliveryNoteTemplateDetailVO = new TmsDeliveryNoteTemplateDetailVO();
        tmsDeliveryNoteTemplateDetailVO.setId(tmsDeliveryNoteTemplateEntity.getId());
        tmsDeliveryNoteTemplateDetailVO.setCreateTime(tmsDeliveryNoteTemplateEntity.getCreateTime());
        tmsDeliveryNoteTemplateDetailVO.setUpdateTime(tmsDeliveryNoteTemplateEntity.getUpdateTime());
        tmsDeliveryNoteTemplateDetailVO.setDeliveryNoteName(tmsDeliveryNoteTemplateEntity.getDeliveryNoteName());
        tmsDeliveryNoteTemplateDetailVO.setAppSource(tmsDeliveryNoteTemplateEntity.getAppSource());
        tmsDeliveryNoteTemplateDetailVO.setUseState(tmsDeliveryNoteTemplateEntity.getUseState());
        tmsDeliveryNoteTemplateDetailVO.setLastOperatorName(tmsDeliveryNoteTemplateEntity.getLastOperatorName());
        tmsDeliveryNoteTemplateDetailVO.setFrontPageStr(tmsDeliveryNoteTemplateEntity.getFrontPageStr());
        tmsDeliveryNoteTemplateDetailVO.setShowPriceFlag(tmsDeliveryNoteTemplateEntity.getShowPriceFlag());
        tmsDeliveryNoteTemplateDetailVO.setScopeType(tmsDeliveryNoteTemplateEntity.getScopeType());
        tmsDeliveryNoteTemplateDetailVO.setShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateEntity.getShowPriceTemplateOssUrl());
        tmsDeliveryNoteTemplateDetailVO.setNoShowPriceTemplateOssUrl(tmsDeliveryNoteTemplateEntity.getNoShowPriceTemplateOssUrl());
        if(!CollectionUtils.isEmpty(tmsDeliveryNoteTemplateEntity.getTmsDeliveryNoteTemplateBelongEntities())){
            List<TmsDeliveryNoteTemplateBelongEntity> tmsDeliveryNoteTemplateBelongEntities = tmsDeliveryNoteTemplateEntity.getTmsDeliveryNoteTemplateBelongEntities();
            tmsDeliveryNoteTemplateDetailVO.setTmsDeliveryNoteTemplateBelongVOList(tmsDeliveryNoteTemplateBelongEntities.stream().map(TmsDeliveryNoteTemplateAssembler::toTmsDeliveryNoteTemplateBelongVO).collect(Collectors.toList()));
        }

        return tmsDeliveryNoteTemplateDetailVO;
   }

   public static TmsDeliveryNoteTemplateBelongVO toTmsDeliveryNoteTemplateBelongVO(TmsDeliveryNoteTemplateBelongEntity entity){
        if(entity == null){
            return null;
        }
       TmsDeliveryNoteTemplateBelongVO vo = new TmsDeliveryNoteTemplateBelongVO();

        vo.setDeliveryNoteTemplateId(entity.getDeliveryNoteTemplateId());
        vo.setTenantId(entity.getTenantId());
        vo.setScopeType(entity.getScopeType());
        vo.setScopeBusinessId(entity.getScopeBusinessId());
        vo.setScopeBusinessName(entity.getScopeBusinessName());

       return vo;
   }

}
