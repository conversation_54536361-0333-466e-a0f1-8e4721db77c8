package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * Description: 配送单打印<br/>
 * date: 2025/1/17 11:04<br/>
 *
 * <AUTHOR> />
 */
@Data
public class OrderDeliveryNotePrintInput {

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    /**
     * 打印方向
     * true:纵向 false:横向
     */
    private Boolean printDirection;
}
