package net.summerfarm.tms.inbound.controller.deliveryNoteTemplate.input.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2024-12-09 14:00:38
 * @version 1.0
 *
 */
@Data
public class TmsDeliveryNoteTemplateBelongSaveCommandInput implements Serializable{

	/**
	 * 租户ID
	 */
	@NotNull(message = "租户ID不能为空")
	private Long tenantId;

	/**
	 * 作用域 0默认 1租户 2大客户
	 */
	@NotNull(message = "作用域不能为空")
	private Integer scopeType;

	/**
	 * 作用域下的业务ID 0默认
	 */
	@NotBlank(message = "作用域下的业务ID不能为空")
	private String scopeBusinessId;

	/**
	 * 业务方名称
	 */
	@NotBlank(message = "业务方名称不能为空")
	private String scopeBusinessName;

	/**
	 * 应用来源 1顺路达 2鲜沐 3saas 4外单
	 */
	@NotNull(message = "应用来源不能为空")
	private Integer appSource;
}