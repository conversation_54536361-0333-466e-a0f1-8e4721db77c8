package net.summerfarm.tms.dist;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.flatObject.DeliveryNoteOrderFlatObject;
import net.summerfarm.tms.dist.flatObject.ManyFulfillmentWayDistOrderFlatObject;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.query.dist.DistOrderQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Description:委托单仓库
 * date: 2022/9/14 18:51
 *
 * <AUTHOR>
 */
public interface DistOrderRepository {

    /**
     * 分页查询委托单
     *
     * @param distOrderQuery 委托单查询
     * @return 委托单分页信息
     * todo 部分query中的字段没有支持
     */
    PageInfo<DistOrderEntity> queryPage(DistOrderQuery distOrderQuery);

    /**
     * 根据外部单号和来源查询委托单信息 包含返回with后面的信息
     *
     * @param outOrderId 外部单号
     * @param sourceType 来源
     * @return 委托单信息
     */
    DistOrderEntity queryWithItemByOutOrderIdAndSource(String outOrderId, DistOrderSourceEnum sourceType);

    /**
     * 根据UK查询 包含返回with后面的信息
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithItemByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);


    /**
     * 根据UK查询 包含返回with后面的信息
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);

    /**
     * 根据UK查询 包含返回with后面的信息
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithItemWithSiteWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);

    /**
     * 根据UK查询 包含返回with后面的信息
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithItemWithSiteByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);

    /**
     * 根据UK查询 包含返回with后面的信息
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithItemWithDeliveryOrderByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);

    /**
     * 根据UK查询 主表数据
     *
     * @param outOrderId      外部单号
     * @param sourceType      来源
     * @param expectBeginTime 期待开始时间
     * @param outerContactId  外部联系人ID
     * @return 委托单信息
     */
    DistOrderEntity queryByUk(String outOrderId, DistOrderSourceEnum sourceType, LocalDateTime expectBeginTime, String outerContactId);


    /**
     * 新增委托单
     * 新增 有则报错
     *
     * @param distOrderEntity 委托单实体
     * @return 委托单ID
     */
    Long save(DistOrderEntity distOrderEntity);

    /**
     * 保存委托单
     * 标准 有则覆盖 没有则新增
     *
     * @param distOrderEntity 委托单实体
     * @return 委托单ID
     */
    Long saveOrUpdate(DistOrderEntity distOrderEntity);

    /**
     * 根据主键查询 包含返回with后面的信息
     *
     * @param distId 委托单ID
     * @return 委托单信息
     */
    DistOrderEntity queryWithItem(Long distId);

    /**
     * 查询主表和关联的所有信息
     * @param distId 委托单ID
     * @return 委托单信息
     */
    DistOrderEntity queryDetail(Long distId);

    /**
     * 根据委托单ID查询委托单数量
     *
     * @param distId 委托单ID
     * @return 数量
     */
    Long queryCount(Long distId);

    /**
     * 根据主键查询 主表数据
     *
     * @param distId 委托单ID
     * @return 委托单信息
     */
    DistOrderEntity query(Long distId);

    /**
     * 根据主键查询 包含返回with后面的信息
     *
     * @param distId 委托单ID
     * @return 委托单实体
     */
    DistOrderEntity queryWithDeliveryOrder(Long distId);

    /**
     * 修改委托单状态
     *
     * @param distOrderEntity
     */
    void modifyStatus(DistOrderEntity distOrderEntity);

    /**
     * 根据委托单id更新委托单信息
     *
     * @param distOrderEntity 委托单实体
     */
    void update(DistOrderEntity distOrderEntity);

    /**
     * 根据承运单状态查询承运单信息
     *
     * @param statusList 状态
     * @return 结果
     */
    List<DistOrderEntity> queryByStatus(List<DistOrderStatusEnum> statusList);

    /**
     * 获取承运单带承运、承运中自提销售
     *
     * @return 结果
     */
    List<DistOrderEntity> queryActDistOrderBySource(List<Integer> distOrderSourceCodes);

    /**
     * 根据委托单ID删除删除委托单
     *
     * @param distId 委托单ID
     */
    void remove(Long distId);

    /**
     * 查询
     *
     * @param distOrderQuery 查询
     * @return 结果
     * todo 部分query中的字段没有支持
     */
    List<DistOrderEntity> queryList(DistOrderQuery distOrderQuery);

    /**
     * 查询单个
     *
     * @param distOrderQuery 委托单
     * @return 结果
     */
    DistOrderEntity query(DistOrderQuery distOrderQuery);

    /**
     * 查询
     *
     * @param distOrderQuery 查询条件
     * @return
     */
    List<DistOrderEntity> queryListWithItem(DistOrderQuery distOrderQuery);

    /**
     * 查询
     *
     * @param distOrderQuery 查询条件
     * @return
     */
    List<DistOrderEntity> queryListWithDeliveryOrder(DistOrderQuery distOrderQuery);

    /**
     * 查询
     *
     * @param distOrderQuery 查询条件
     * @return
     */
    DistOrderEntity queryWithItemWithBeginSite(DistOrderQuery distOrderQuery);

    /**
     * 查询
     * @param distOrderId 委托单id
     * @return
     */
    DistOrderEntity queryWithSiteWithItem(Long distOrderId);

    /**
     * 批量更新委托单状态
     * @param updateDistOrderEntityList 委托单集合
     * @param statusEnum 状态
     */
    void updateStatus(ArrayList<DistOrderEntity> updateDistOrderEntityList, DistOrderStatusEnum statusEnum);

    /**
     * 查询
     *
     * @param distOrderQuery 查询条件
     * @return
     */
    List<DistOrderEntity> queryListWithItemWithBeginSite(DistOrderQuery distOrderQuery);

    /**
     * 查询店铺名称
     * @param distOrderQuery 查询条件
     * @return 点位和店铺的信息
     */
    Map<Long, String> querySiteShopName(DistOrderQuery distOrderQuery);

    /**
     * 查询排除拦截的数据
     * @param distOrderIdList id集合
     * @return 结果
     */
    List<DistOrderEntity> queryValidListWithItemByIds(List<Long> distOrderIdList);

    /**
     * 根据委托单物品ID删除委托单项
     * @param distItemId
     */
    void removeDistItem(Long distItemId);

    /**
     * 根据调度单ID查询委托单信息
     * @param batchId 调度单ID
     * @return 委托单信息
     */
    List<DistOrderEntity> queryDistOrdersByBatchId(Long batchId);

    /**
     * 根据调度单ID查询委托单信息
     *
     * @param batchId 调度单ID
     * @return 委托单信息
     */
    List<DistOrderEntity> queryDistOrdersWithItemByBatchId(Long batchId);

    /**
     * 强制查询主库
     * @param distOrderQuery 查询
     * @return 结果
     */
    DistOrderEntity queryLastWithDeliveryOrderForceMaster(DistOrderQuery distOrderQuery);

    /**
     * 根据Id查询数据
     * @param distId 委托单Id
     * @return 结果
     */
    DistOrderEntity queryById(Long distId);

    /**
     * 批量更新
     * @param distOrderEntities 委托单集合
     */
    void batchUpdate(List<DistOrderEntity> distOrderEntities);

    /**
     * 根据日期和城配仓查询配送单信息
     * @param deliveryTime 日期
     * @param storeNo 城配仓
     * @return 结果
     */
    List<DeliveryNoteOrderFlatObject> queryDeliveryNoteByDateAndStoreNo(LocalDate deliveryTime, Integer storeNo);

    /**
     * 根据订单编号查询配送单信息
     * @param orderNo 订单编号
     * @return 结果
     */
    List<DeliveryNoteOrderFlatObject> queryOrderDeliveryNoteByOrderNo(String orderNo);

    /**
     * 批量更新履约方式
     * @param distOrderEntities 委托单集合
     */
    void batchUpdateFulfillmentWay(List<DistOrderEntity> distOrderEntities);

    /**
     * 查询干线转运委托单的起点站点
     *
     * @param nextDay       配送日期第二天
     * @param targetSources 来源
     * @param state         状态
     * @return 结果
     */
    List<Long> queryTrunkTransportBeginSites(LocalDateTime nextDay, List<Integer> targetSources, Integer state);

    /**
     * 查询第二天有多种履约配送方式的委托单
     * @param deliveryTime 配送日期
     * @param sourceList 来源
     * @return 结果
     */
    List<ManyFulfillmentWayDistOrderFlatObject> queryHaveNextDeliveryDayManyFulfillmentWay(LocalDate deliveryTime, List<Integer> sourceList);
}
