package net.summerfarm.tms.dist.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistFlowVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.dist.vo.DistStaticVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.dist.DistOrderMark;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:委托单实体
 * date: 2022/9/8 11:26
 *
 * <AUTHOR>
 */
@Data
public class DistOrderEntity {
    /**
     * 委托单号
     */
    private Long distId;
    
    /**
     * 外部客户信息
     */
    private DistClientVO distClientVO;
    /**
     * 具体配送信息描述
     */
    private DistFlowVO distFlowVO;
    /**
     * 订单统计信息
     */
    private DistStaticVO distStaticVO;
    /**
     * 物品列表
     */
    private List<DistItemVO> distItems;
    /**
     * 配送详情列表
     */
    private List<DeliveryOrderEntity> deliveryOrders;
    /**
     * 起点
     */
    private SiteEntity beginSite;
    /**
     * 中转站
     */
    private SiteEntity midSite;
    /**
     * 终点
     */
    private SiteEntity endSite;
    /**
     * 委托单来源
     */
    private DistOrderSourceEnum source;
    /**
     * 拣货类型
     */
    private DistPickTypeEnum pickType;
    /**
     * 状态
     */
    private DistOrderStatusEnum status;
    /**
     * 备注(关闭原因..)
     */
    private String closeReason;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人ID
     */
    private String creatorId;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 更新人ID
     */
    private String updaterId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 取消类型，0：普通，1：拦截
     */
    private DistOrderCancelTypeEnum cancelType;

    /**
     * 取消时间
     */
    private LocalDateTime cancelTime;
    /**
     * 配送备注
     */
    private String sendRemark;
    /**
     * 起点客户名
     */
    private String beginClientName;

    /**
     * 履约配送方式  0：干配，1：自提，2：干线 ，3：快递 ，4：干线转运
     */
    private Integer fulfillmentDeliveryWay;

    /**
     * 围栏id
     */
    private Integer fenceId;

    /**
     * 围栏名称
     */
    private String fenceName;

    /**
     * 区域id
     */
    private Integer adCodeMsgId;

    /**
     * 自定义区域名称
     */
    private String customAreaName;
    /**
     * 判断委托单状态是否有效
     *
     * @return 是否有效，true：是，false：否
     */
    public boolean isValid() {
        return DistOrderStatusEnum.isValid(this.status);
    }

    /**
     * 判断委托单是否待承运
     *
     * @return 是否有效，true：是，false：否
     */
    public boolean waitCarrierFlag() {
        if (!isValid()) {
            throw new TmsRuntimeException("该委托单已取消或已关闭");
        }
        return this.status == DistOrderStatusEnum.TO_BE_WIRED;
    }

    /**
     * 创建委托单
     */
    public void create() {
        this.status = DistOrderStatusEnum.TO_BE_WIRED;
        this.createTime = LocalDateTime.now();
    }

    /**
     * 订单项信息统计
     */
    public void orderItemsStats() {
        if (this.distItems == null) {
            this.distItems = new ArrayList<>();
        }
        BigDecimal normalWeight = BigDecimal.ZERO;
        BigDecimal normalVolume = BigDecimal.ZERO;
        Integer normalQuantity = 0;
        BigDecimal coldWeight = BigDecimal.ZERO;
        BigDecimal coldVolume = BigDecimal.ZERO;
        Integer coldQuantity = 0;
        BigDecimal freezeWeight = BigDecimal.ZERO;
        BigDecimal freezeVolume = BigDecimal.ZERO;
        Integer freezeQuantity = 0;
        BigDecimal totalPrice = BigDecimal.ZERO;

        Map<TmsTemperatureEnum, List<DistItemVO>> temperatureGroupMap = this.distItems.stream().collect(Collectors.groupingBy(DistItemVO::getTemperatureEnum));
        for (Map.Entry<TmsTemperatureEnum, List<DistItemVO>> entry : temperatureGroupMap.entrySet()) {
            List<DistItemVO> list = entry.getValue();
            for (DistItemVO distItemVO : list) {
                Integer quantity = distItemVO.getQuantity();
                BigDecimal quantityWithBigDecimal = new BigDecimal(quantity);
                BigDecimal weight = distItemVO.getWeight();
                BigDecimal volume = distItemVO.getVolume();
                BigDecimal price = distItemVO.getOutItemPrice() == null ? BigDecimal.ZERO : distItemVO.getOutItemPrice();
                totalPrice = totalPrice.add(price.multiply(quantityWithBigDecimal));
                if (TmsTemperatureEnum.NORMAL.equals(entry.getKey())) {

                    normalWeight = normalWeight.add(weight.multiply(quantityWithBigDecimal));
                    normalVolume = normalVolume.add(volume.multiply(quantityWithBigDecimal));
                    normalQuantity += quantity;
                }
                if (TmsTemperatureEnum.COLD.equals(entry.getKey())) {
                    coldWeight = coldWeight.add(weight.multiply(quantityWithBigDecimal));
                    coldVolume = coldVolume.add(volume.multiply(quantityWithBigDecimal));
                    coldQuantity += quantity;
                }
                if (TmsTemperatureEnum.FREEZE.equals(entry.getKey())) {
                    freezeWeight = freezeWeight.add(weight.multiply(quantityWithBigDecimal));
                    freezeVolume = freezeVolume.add(volume.multiply(quantityWithBigDecimal));
                    freezeQuantity += quantity;
                }
            }
        }
        DistStaticVO distStatic = new DistStaticVO();
        distStatic.setFreezeWeight(freezeWeight.setScale(2, RoundingMode.HALF_UP));
        distStatic.setFreezeVolume(freezeVolume.setScale(2, RoundingMode.HALF_UP));
        distStatic.setFreezeQuantity(freezeQuantity);
        distStatic.setColdWeight(coldWeight.setScale(2, RoundingMode.HALF_UP));
        distStatic.setColdVolume(coldVolume.setScale(2, RoundingMode.HALF_UP));
        distStatic.setColdQuantity(coldQuantity);
        distStatic.setNormalWeight(normalWeight.setScale(2, RoundingMode.HALF_UP));
        distStatic.setNormalVolume(normalVolume.setScale(2, RoundingMode.HALF_UP));
        distStatic.setNormalQuantity(normalQuantity);
        distStatic.setTotalPrice(totalPrice.setScale(2, RoundingMode.HALF_UP));
        this.distStaticVO = distStatic;
    }

    /**
     * 关闭委托单
     *
     * @param closeReason 关闭原因
     */
    public void close(String closeReason) {
        this.status = DistOrderStatusEnum.CLOSED;
        this.closeReason = closeReason;
    }

    /**
     * 关闭委托单
     *
     * @param closeReason 关闭原因
     * @param updater     更新人
     * @param updaterId   更新人ID
     */
    public void close(String closeReason, String updater, String updaterId) {
        this.status = DistOrderStatusEnum.CLOSED;
        this.closeReason = closeReason;
        update(updater, updaterId);
    }

    public void resetExpectBeginTime(LocalDateTime newLocalDateTime){
        if (newLocalDateTime == null){
            throw new TmsRuntimeException("修改后的配送时间不能为空");
        }
        this.distFlowVO.setExpectBeginTime(newLocalDateTime);
    }
    public void resetBeginSite(SiteEntity newBeginSite){
        if (newBeginSite == null){
            throw new TmsRuntimeException("修改后的起点不能为空");
        }
        this.beginSite = newBeginSite;
    }
    public void resetEndSite(SiteEntity newEndSite){
        if (newEndSite == null){
            throw new TmsRuntimeException("修改后的终点不能为空");
        }
        this.distClientVO.setOutContactId(newEndSite.getOutBusinessNo());
        this.endSite = newEndSite;
    }

    public void resetDistItemQuantity(String outItemId,Integer quantity, Integer newQuantity) {
        Optional<DistItemVO> distItemOptional = this.findDistItem(outItemId, quantity);
        if (!distItemOptional.isPresent()){
            throw new TmsRuntimeException("委托单明细不存在");
        }
        DistItemVO distItemVO = distItemOptional.get();
        distItemVO.setQuantity(newQuantity);
    }

    public Optional<DistItemVO> findDistItem(String outItemId,Integer quantity) {
        if (CollectionUtils.isEmpty(this.distItems) || StrUtil.isBlank(outItemId) || quantity == null){
            return Optional.empty();
        }
        return this.distItems.stream().filter(e -> Objects.equals(e.getOutItemId(), outItemId) &&
                Objects.equals(e.getQuantity(), quantity)).findFirst();
    }

    /**
     * 是否待排线状态
     */
    public boolean isWaitWired() {
        List<DistOrderStatusEnum> waitWiredStatus = Arrays.asList(DistOrderStatusEnum.TO_BE_WIRED, DistOrderStatusEnum.IN_WIRED);
        return waitWiredStatus.contains(this.status);
    }

    /**
     * 完成委托单
     */
    public boolean isComplete() {
        return this.status == DistOrderStatusEnum.COMPLETE_DELIVERY;
    }

    /**
     * 是否拦截委托单
     */
    public boolean isIntercept() {
        return this.cancelType == DistOrderCancelTypeEnum.intercept;
    }

    /**
     * 完成委托单
     */
    public boolean isInvalid() {
        return this.status == DistOrderStatusEnum.CLOSED
                || this.status == DistOrderStatusEnum.CANCEL_BEFORE_WIRED
                || this.status == DistOrderStatusEnum.CANCEL_AFTER_WIRED;
    }

    /**
     * 完成委托单
     */
    public void complete() {
        this.status = DistOrderStatusEnum.COMPLETE_DELIVERY;
        this.getDistFlowVO().setRealArrivalTime(LocalDateTime.now());
    }

    /**
     * 更新委托单
     *
     * @param updater   更新人
     * @param updaterId 更新人ID
     */
    public void update(String updater, String updaterId) {
        this.updater = updater;
        this.updaterId = updaterId;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 取消委托单
     */
    public void cancel() {
        this.status = DistOrderStatusEnum.CANCEL_BEFORE_WIRED;
    }

    /**
     * 绑定配送批次
     */
    public void bind() {
        this.status = DistOrderStatusEnum.IN_DELIVERY;
    }

    /**
     * 解绑配送批次
     */
    public void unbind() {
        this.status = DistOrderStatusEnum.TO_BE_WIRED;
    }

    /**
     * 取消委托单
     *
     * @param updater   更新人
     * @param updaterId 更新人ID
     */
    public void cancel(String updater, String updaterId) {
        this.status = DistOrderStatusEnum.CANCEL_BEFORE_WIRED;
        update(updater, updaterId);
    }

    /**
     * 更新点位
     *
     * @param midSiteId 点位ID
     */
    public void modifyMidSite(Long midSiteId) {
        if (!isValid()) {
            throw new TmsRuntimeException("该委托单已取消或已关闭");
        }
        if (midSiteId.equals(this.beginSite.getId()) || midSiteId.equals(this.endSite.getId())) {
            throw new TmsRuntimeException("中转站不可与起点、终点重复");
        }
        if (this.midSite == null) {
            this.midSite = new SiteEntity();
        }
        if (Objects.equals(midSiteId, this.midSite.getId())) {
            throw new TmsRuntimeException("要修改的中转站与原中转站一致");
        }
        this.midSite.setId(midSiteId);
        this.deliveryOrders = handleDeliveryOrders(String.valueOf(midSiteId));
    }

    /**
     * 更新点位
     *
     * @param midSiteId 点位ID
     * @param updater   更新人
     * @param updaterId 更新人ID
     */
    public void modifyMidSite(Long midSiteId, String updater, String updaterId) {
        if (!isValid()) {
            throw new TmsRuntimeException("该委托单已取消或已关闭");
        }
        if (midSiteId.equals(this.beginSite.getId()) || midSiteId.equals(this.endSite.getId())) {
            throw new TmsRuntimeException("中转站不可与起点、终点重复");
        }
        this.midSite.setId(midSiteId);
        update(updater, updaterId);
    }

    /**
     * 自动生成中转站
     *
     * @param midSiteIdStr 点位ID
     */
    public void autoGenerateMidSite(String midSiteIdStr) {
        if (!DistOrderSourceEnum.isTrunk(this.source)) {
            this.deliveryOrders = handleDeliveryOrders(null);
            return;
        }
        //人为手动提交用车需求
        if (!"系统".equals(this.creatorId)) {
            this.deliveryOrders = handleDeliveryOrders(null);
            return;
        }
        //系统提交用车需求
        this.deliveryOrders = handleDeliveryOrders(midSiteIdStr);
        if (!DistOrderSourceEnum.isAuto(this.source) || midSiteIdStr == null) {
            return;
        }
        this.midSite = new SiteEntity();
        this.midSite.setId(Long.parseLong(midSiteIdStr));

    }

    /**
     * 处理配送单信息
     *
     * @param midSiteIdStr 点位ID
     * @return 配送单信息
     */
    public List<DeliveryOrderEntity> handleDeliveryOrders(String midSiteIdStr) {
        List<DeliveryOrderEntity> deliveryOrders = new ArrayList<>(2);
        if (StringUtils.isBlank(midSiteIdStr)) {
            DeliveryOrderEntity deliveryOrder = getDeliveryOrder();
            deliveryOrder.setBeginSiteId(this.beginSite.getId());
            deliveryOrder.setEndSiteId(this.endSite.getId());
            deliveryOrder.setDeliveryOrderSiteType(DeliveryOrderSiteTypeEnum.NO_MID);
            deliveryOrders.add(deliveryOrder);
        } else {
            long midSiteId = Long.parseLong(midSiteIdStr);
            DeliveryOrderEntity firstHalfDeliveryOrder = getDeliveryOrder();
            firstHalfDeliveryOrder.setBeginSiteId(this.beginSite.getId());
            firstHalfDeliveryOrder.setEndSiteId(midSiteId);
            firstHalfDeliveryOrder.setDeliveryOrderSiteType(DeliveryOrderSiteTypeEnum.END_SITE_MID);
            firstHalfDeliveryOrder.setName(null);
            firstHalfDeliveryOrder.setPhone(null);
            firstHalfDeliveryOrder.setAddressDetail(null);
            deliveryOrders.add(firstHalfDeliveryOrder);
            DeliveryOrderEntity secondHalfDeliveryOrder = getDeliveryOrder();
            secondHalfDeliveryOrder.setBeginSiteId(midSiteId);
            secondHalfDeliveryOrder.setEndSiteId(this.endSite.getId());
            secondHalfDeliveryOrder.setDeliveryOrderSiteType(DeliveryOrderSiteTypeEnum.BEGIN_SITE_MID);
            deliveryOrders.add(secondHalfDeliveryOrder);
        }
        if (this.distFlowVO.getType() == DistTypeEnum.DELIVERY_AND_RECYCLE.getCode()) {
            List<DeliveryOrderEntity> deliveryOrdersNew = new ArrayList<>(deliveryOrders.size() * 2);
            for (DeliveryOrderEntity deliveryOrder : deliveryOrders) {
                deliveryOrder.setType(DeliveryOrderTypeEnum.send.getCode());
                DeliveryOrderEntity deliveryOrderEntityNew = DeliveryOrderEntity.copy(deliveryOrder);
                deliveryOrderEntityNew.setType(DeliveryOrderTypeEnum.recycle.getCode());
                deliveryOrdersNew.add(deliveryOrder);
                deliveryOrdersNew.add(deliveryOrderEntityNew);
            }
            return deliveryOrdersNew;
        } else {
            return deliveryOrders;
        }
    }

    /**
     * 获取配送单信息
     *
     * @return 配送单信息
     */
    private DeliveryOrderEntity getDeliveryOrder() {
        DeliveryOrderEntity deliveryOrderVO = new DeliveryOrderEntity();
        deliveryOrderVO.setDistOrderId(this.distId);
        deliveryOrderVO.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
        deliveryOrderVO.setOuterOrderId(this.distClientVO.getOutOrderId());
        deliveryOrderVO.setOuterClientName(this.distClientVO.getOutClientName());
        deliveryOrderVO.setOuterClientId(this.distClientVO.getOutClientId());
        deliveryOrderVO.setOuterContactId(this.distClientVO.getOutContactId());
        deliveryOrderVO.setSource(this.source);
        deliveryOrderVO.setDeliveryTime(this.distFlowVO.getExpectBeginTime());
        deliveryOrderVO.setName(this.endSite.getDeliveryOrderName());
        deliveryOrderVO.setPhone(this.endSite.getPhone());
        deliveryOrderVO.setAddressDetail(this.endSite.getFullAddress());
        deliveryOrderVO.setType(this.distFlowVO.getType());
        deliveryOrderVO.setSendRemark(this.getSendRemark());
        deliveryOrderVO.setFenceId(this.fenceId);
        deliveryOrderVO.setFenceName(this.fenceName);
        deliveryOrderVO.setAdCodeMsgId(this.adCodeMsgId);
        deliveryOrderVO.setCustomAreaName(this.customAreaName);
        return deliveryOrderVO;
    }

    public DistOrderMark getDistOrderMark() {
        return new DistOrderMark(this.distId, this.distClientVO.getOutOrderId(), this.source, this.distFlowVO.getExpectBeginTime(), this.distClientVO.getOutContactId());
    }

    /**
     * 拦截委托单
     */
    public DistOrderEntity interceptDistOrder() {
        //如果不是有效状态进行跳过
        if (!DistOrderStatusEnum.validNotCompleteStatus().contains(this.getStatus().getCode())) {
            return null;
        }
        DistOrderEntity distOrderEntity = new DistOrderEntity();
        distOrderEntity.setDistId(this.getDistId());
        //更新拦截状态和时间
        if (this.getCancelTime() == null) {
            distOrderEntity.setCancelTime(LocalDateTime.now());
        }
        distOrderEntity.setCancelType(DistOrderCancelTypeEnum.intercept);
        distOrderEntity.setStatus(DistOrderStatusEnum.interceptIsCompeltePath(this.getStatus().getCode()));

        return distOrderEntity;
    }

    /**
     * 构建业务主键
     * @return uk
     */
    public String buildUk(){
        StringJoiner ukJoiner = new StringJoiner("#");
        if(this.distClientVO != null){
            ukJoiner.add(this.distClientVO.getOutOrderId());
        }
        if(this.source != null){
            ukJoiner.add(this.source.getCode().toString());
        }
        if(this.distFlowVO != null){
            ukJoiner.add(this.distFlowVO.getExpectBeginTime().toString());
        }
        if(this.distClientVO != null){
            ukJoiner.add(this.distClientVO.getOutContactId());
        }
        return ukJoiner.toString();
    }
}
