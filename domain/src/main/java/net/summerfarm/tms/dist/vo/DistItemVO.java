package net.summerfarm.tms.dist.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.enums.DistItemDeliveryTypeEnum;
import net.summerfarm.tms.enums.DistItemTypeEnum;
import net.summerfarm.tms.enums.TmsTemperatureEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * Date:2022/7/8
 * 委托物品
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistItemVO {
    private Long id;
    private Long distOrderId;
    private String outItemId;
    private String outItemType;
    private String outItemName;
    private BigDecimal outItemPrice;
    private BigDecimal volume;
    private BigDecimal weight;
    private Integer quantity;
    private TmsTemperatureEnum temperatureEnum;
    private String specification;
    private String unit;
    private DistItemTypeEnum itemTypeEnum;
    private DistItemDeliveryTypeEnum itemDeliveryTypeEnum;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    @Deprecated
    private Integer deliveryType;
    /**
     * 商品类型，0:普通，1:水果
     */
    private Integer type;
    /**
     * 包装类型：0单品 1包裹
     */
    private Integer packType;
}
