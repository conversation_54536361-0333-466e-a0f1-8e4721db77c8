package net.summerfarm.tms.dist.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * Date:2022/7/8
 * 订单统计信息
 */
@Data
public class DistStaticVO {

    /**
     * 冷冻重量
     */
    private BigDecimal freezeWeight;

    /**
     * 冷冻体积
     */
    private BigDecimal freezeVolume;

    /**
     * 冷冻件数
     */
    private Integer freezeQuantity;

    /**
     * 冷藏重量
     */
    private BigDecimal coldWeight;

    /**
     * 冷藏体积
     */
    private BigDecimal coldVolume;

    /**
     * 冷藏件数
     */
    private Integer coldQuantity;

    /**
     * 常温重量
     */
    private BigDecimal normalWeight;

    /**
     * 常温体积
     */
    private BigDecimal normalVolume;

    /**
     * 常温件数
     */
    private Integer normalQuantity;

    /**
     * 总价值
     */
    private BigDecimal totalPrice;

    /**
     * 获取总重量
     *
     * @return 总重量
     */
    public BigDecimal getTotalWeight() {
        return this.freezeWeight.add(this.coldWeight).add(this.normalWeight);
    }

    /**
     * 获取总体积
     *
     * @return 总体积
     */
    public BigDecimal getTotalVolume() {
        return this.freezeVolume.add(this.coldVolume).add(this.normalVolume);
    }

    /**
     * 获取总数量
     *
     * @return 总数量
     */
    public Integer getTotalQuantity() {
        return this.freezeQuantity + this.coldQuantity + this.normalQuantity;
    }
}
