package net.summerfarm.tms.dist;


import net.summerfarm.tms.enums.DistConfigTypeEnum;

import java.util.List;

/**
 * Description:委托单配置仓库
 * date: 2022/9/14 18:51
 *
 * <AUTHOR>
 */
public interface DistConfigRepository {

    /**
     * 查询配置值
     *
     * @param beginSiteId        起点ID
     * @param endSiteId          终点ID
     * @param distConfigTypeEnum 委托单配置类型枚举
     * @return 配置值
     */
    String findConfigValue(Long beginSiteId, Long endSiteId, DistConfigTypeEnum distConfigTypeEnum);

    /**
     * 查询配置数量
     *
     * @param beginSiteId        起点ID
     * @param endSiteId          终点ID
     * @param distConfigTypeEnum 委托单配置类型枚举
     * @return 配置数量
     */
    Long queryCount(Long beginSiteId, Long endSiteId, DistConfigTypeEnum distConfigTypeEnum);

    /**
     * 配置初始化
     *
     * @param configs 初始配置集合
     */
    void configInit(List<String> configs);

    /**
     * 更新
     * @param storeNos
     * @param distConfigTypeEnum
     */
    void update(List<String> storeNos, DistConfigTypeEnum distConfigTypeEnum);

    /**
     * 新增
     * @param grayStoreNos
     * @param distConfigTypeEnum
     */
    void add(List<String> grayStoreNos, DistConfigTypeEnum distConfigTypeEnum);
}
