package net.summerfarm.tms.dist.vo;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.base.Constants;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * Date:2022/7/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistFlowVO {

    /**
     * 期望开始配送时间
     */
    private LocalDateTime expectBeginTime;
    /**
     * 期望完成配送时间
     */
    private LocalDateTime expectEndTime;
    /**
     * 0配送 1回收 2配送&回收
     */
    private Integer type;
    /**
     * 实际送达时间
     */
    private LocalDateTime realArrivalTime;
    /**
     * 希望签收时间
     */
    private String timeFrame;

    public boolean isOutTimeFrame(LocalDateTime currentDateTime){
        if (this.expectBeginTime == null || currentDateTime == null || StrUtil.isBlank(this.timeFrame)){
            return false;
        }
        String[] splitTimes = this.timeFrame.split(Constants.Symbol.BAR);
        if (splitTimes.length != 2){
            return false;
        }
        LocalDateTime beginTime = LocalDateTime.of(this.expectBeginTime.toLocalDate(),LocalTime.parse(splitTimes[0]));
        LocalDateTime endTime = LocalDateTime.of(this.expectBeginTime.toLocalDate(),LocalTime.parse(splitTimes[1]));
        return currentDateTime.isBefore(beginTime) || currentDateTime.isAfter(endTime);

    }
}
