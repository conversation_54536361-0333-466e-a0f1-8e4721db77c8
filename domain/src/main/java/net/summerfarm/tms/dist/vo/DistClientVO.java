package net.summerfarm.tms.dist.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * Date:2022/7/8
 * 客户信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistClientVO {
    /**
     * 外部关联单据号
     */
    private String outOrderId;
    /**
     * 外部租户号
     */
    private String outTenantId;
    /**
     * 外部品牌名
     */
    private String outBrandName;
    /**
     * 外部客户号
     */
    private String outClientId;
    /**
     * 外部客户名
     */
    private String outClientName;
    /**
     * 外部联系人ID
     */
    private String outContactId;

}
