package net.summerfarm.tms.after.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ExpenseDetailEntity {
    private Integer id;

    /**
     * 报销单id
     */
    private Integer expenseId;

    /**
     * 报销类型
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 是否复核
     */
    private Integer isReview;

    /**
     * 照片
     */
    private String photos;

    /**
     * 报销起点
     */
    private String startAddress;

    /**
     * 报销终点
     */
    private String endAddress;

    /**
     * 报销里程
     */
    private BigDecimal mileage;

    /**
     * 报销金额
     */
    private BigDecimal amount;

    /**
     * 报销明细
     */
    private String remark;

    /**
     * 修改人
     */
    private String updater;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
