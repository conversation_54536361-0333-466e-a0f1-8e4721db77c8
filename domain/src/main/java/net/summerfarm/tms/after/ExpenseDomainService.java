package net.summerfarm.tms.after;

import net.summerfarm.tms.after.entity.ExpenseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class ExpenseDomainService {
    @Resource
    ExpenseRepository expenseRepository;
    @Resource
    ExpenseAuditRecordRepository expenseAuditRecordRepository;

    public void submit(ExpenseEntity expense) {
        expenseRepository.saveOrUpdate(expense);
    }

    public Long audit(ExpenseEntity expense) {
        return expenseAuditRecordRepository.save(expense);

    }
}
