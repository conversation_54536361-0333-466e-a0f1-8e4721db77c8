package net.summerfarm.tms.after;

import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.query.expense.ExpenseQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/21 18:28<br/>
 *
 * <AUTHOR> />
 */
public interface ExpenseRepository {

    /**
     * 保存报销单
     *
     * @param expense
     */
    void saveOrUpdate(ExpenseEntity expense);

    /**
     * 根据运输单ID 查询相关的报销单
     * @param deliverySiteId
     * @return
     */
    List<ExpenseEntity> queryListByDeliverySiteId(Long deliverySiteId);

    /**
     * 报销单审核
     * @param expense
     */
    void audit(ExpenseEntity expense);

    /**
     * 查询
     * @param id
     * @return
     */
    ExpenseEntity query(Integer id);

    /**
     * 查询报销以及报销详情
     * @param expenseQuery 查询条件
     * @return 结果
     */
    List<ExpenseEntity> queryListWithDetail(ExpenseQuery expenseQuery);

    /**
     * 根据配送id查询配送信息
     * @param siteId 配送id
     * @return 结果
     */
    ExpenseEntity queryByDeliverySiteId(Long siteId);
}
