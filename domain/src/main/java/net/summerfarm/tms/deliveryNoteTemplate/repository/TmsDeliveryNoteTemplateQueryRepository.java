package net.summerfarm.tms.deliveryNoteTemplate.repository;



import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateQueryParam;

import java.util.List;



/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
public interface TmsDeliveryNoteTemplateQueryRepository {

    PageInfo<TmsDeliveryNoteTemplateEntity> getPage(TmsDeliveryNoteTemplateQueryParam param);

    TmsDeliveryNoteTemplateEntity selectById(Long id);

    List<TmsDeliveryNoteTemplateEntity> selectByCondition(TmsDeliveryNoteTemplateQueryParam param);

    /**
     * 根据条件查询配送单模板和配送单归属
     * @param param 查询
     * @return 结果
     */
    List<TmsDeliveryNoteTemplateEntity> queryWithBelong(TmsDeliveryNoteTemplateQueryParam param);
}