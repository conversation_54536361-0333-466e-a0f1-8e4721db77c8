package net.summerfarm.tms.deliveryNoteTemplate.repository;


import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.query.TmsDeliveryNoteTemplateBelongQueryParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
public interface TmsDeliveryNoteTemplateBelongQueryRepository {

    TmsDeliveryNoteTemplateBelongEntity selectById(Long id);

    List<TmsDeliveryNoteTemplateBelongEntity> selectByCondition(TmsDeliveryNoteTemplateBelongQueryParam param);

    List<TmsDeliveryNoteTemplateBelongEntity> selectListByCondition(TmsDeliveryNoteTemplateBelongQueryParam build);
}