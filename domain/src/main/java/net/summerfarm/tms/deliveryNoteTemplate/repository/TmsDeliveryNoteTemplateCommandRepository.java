package net.summerfarm.tms.deliveryNoteTemplate.repository;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateCommandParam;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
public interface TmsDeliveryNoteTemplateCommandRepository {

    TmsDeliveryNoteTemplateEntity insertSelective(TmsDeliveryNoteTemplateCommandParam param);

    void updateSelectiveById(TmsDeliveryNoteTemplateCommandParam param);

    int remove(Long id);

}