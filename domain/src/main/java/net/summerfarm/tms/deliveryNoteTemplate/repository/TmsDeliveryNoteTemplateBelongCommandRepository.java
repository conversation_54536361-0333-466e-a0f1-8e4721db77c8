package net.summerfarm.tms.deliveryNoteTemplate.repository;


import net.summerfarm.tms.deliveryNoteTemplate.entity.TmsDeliveryNoteTemplateBelongEntity;
import net.summerfarm.tms.deliveryNoteTemplate.param.command.TmsDeliveryNoteTemplateBelongCommandParam;

import java.util.List;

/**
*
* <AUTHOR>
* @date 2024-12-09 14:00:38
* @version 1.0
*
*/
public interface TmsDeliveryNoteTemplateBelongCommandRepository {

    TmsDeliveryNoteTemplateBelongEntity insertSelective(TmsDeliveryNoteTemplateBelongCommandParam param);

    int updateSelectiveById(TmsDeliveryNoteTemplateBelongCommandParam param);

    int remove(Long id);

    /**
     * 根据模版ID删除
     * @param templateId 模版ID
     */
    void removeByTemplateId(Long templateId);

    /**
     * 批量新增
     * @param belongCommandParamList 归属集合
     */
    void batchInsert(List<TmsDeliveryNoteTemplateBelongCommandParam> belongCommandParamList);
}