package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;
import net.summerfarm.tms.query.delivery.DeliverySiteItemQuery;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

public interface DeliverySiteItemRepository {
    /**
     * 保存item 配送数据
     *
     * @param deliverySiteItemEntity
     */
    void update(DeliverySiteItemEntity deliverySiteItemEntity);

    /**
     * 查询
     *
     * @param deliverySiteItemQuery
     * @return 结果
     */
    List<DeliverySiteItemEntity> queryList(DeliverySiteItemQuery deliverySiteItemQuery);

    /**
     * 查询
     *
     * @param deliverySiteId
     * @param outItemId
     * @param type
     * @return
     */
    DeliverySiteItemEntity queryByUk(Long deliverySiteId, String outItemId,Integer type);

    /**
     * 保存
     *
     * @param deliverySiteItemEntity
     */
    void save(DeliverySiteItemEntity deliverySiteItemEntity);

    /**
     * 批量保存
     *
     * @param deliverySiteItemEntities -
     */
    void saveBatch(ArrayList<DeliverySiteItemEntity> deliverySiteItemEntities);


    /**
     * 查询、扫码信息
     * @param deliverySiteItemQuery 查询
     * @return 结果
     */
    List<DeliverySiteItemEntity> queryWithCodeList(DeliverySiteItemQuery deliverySiteItemQuery);

    /**
     * 查询、扫码信息 回收信息
     * @param deliverySiteItemQuery 查询
     * @return 结果
     */
    List<DeliverySiteItemEntity> queryListWithCodeWithRecycle(DeliverySiteItemQuery deliverySiteItemQuery);

    /**
     * 配送点位物品状态数据初始化
     * @return 结果
     */
    Boolean siteItemStatusDataInit();

    /**
     * 批量更新
     * @param needUpdateItemEntityList 需要更新的数据
     */
    void batchUpdate(List<DeliverySiteItemEntity> needUpdateItemEntityList);
}
