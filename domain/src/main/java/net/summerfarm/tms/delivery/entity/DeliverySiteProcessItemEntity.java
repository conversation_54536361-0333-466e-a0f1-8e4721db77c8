package net.summerfarm.tms.delivery.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: <br/>
 * date: 2023/2/21 19:50<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteProcessItemEntity {
    /**
     * 单位
     */
    private String unit;
    /**
     * 重量
     */
    private String weight;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 已加工加工的数量
     */
    private Integer haveProductSkuQuantity;

    /**
     * 加工标识 0 加工 1未加工
     */
    private int processFlag;
}
