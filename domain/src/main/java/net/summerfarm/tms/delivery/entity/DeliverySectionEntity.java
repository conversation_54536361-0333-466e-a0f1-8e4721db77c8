package net.summerfarm.tms.delivery.entity;

import lombok.Data;
import net.summerfarm.tms.base.site.entity.SiteEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/12/8 14:12<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySectionEntity implements Serializable {

    /**
     * primary key
     */
    private Long id;

    /**
     * 配送批次id
     */
    private Long batchId;

    /**
     * 开始点位
     */
    private SiteEntity beginSiteEntity;

    /**
     * 结束点位
     */
    private SiteEntity endSiteEntity;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * 0完成排线 1配送完成、定时扫描
     */
    private Integer type;

}
