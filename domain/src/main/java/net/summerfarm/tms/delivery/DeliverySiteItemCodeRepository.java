package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliverySiteItemCodeEntity;

import java.util.List;
import java.util.Map;

public interface DeliverySiteItemCodeRepository {
    /**
     * 保存扫码结果
     *
     * @param deliverySiteItemCodeEntity
     */
    void save(DeliverySiteItemCodeEntity deliverySiteItemCodeEntity);

    /**
     * 统计扫码数量
     *
     * @param deliveryItemId
     */
    Long countByDeliverySiteItemId(Long deliveryItemId);

    /**
     * 根据唯一码查询数量
     * @param onlyCode 唯一码
     * @return 数量
     */
    Long counyByOnlyCode(String onlyCode);

    /**
     * 根据扫描码查询信息
     * @param code 码
     * @return 结果
     */
    DeliverySiteItemCodeEntity queryByOnlyCode(String code);

    /**
     * 根据配送商品ID统计商品扫码数量
     * @param deliverySiteItemIds 配送站点商品ID集合
     * @return  key deliverySiteItemId value 扫码数量
     */
    Map<Long, Long> delSiteItemId2ScanCountMapByDelSiteItemIds(List<Long> deliverySiteItemIds);

    /**
     * 根据站点ID查询配送扫码信息
     * @param deliverySiteId 站点ID
     * @return 扫码信息
     */
    List<DeliverySiteItemCodeEntity> queryListByDeliverySiteId(Long deliverySiteId);
}
