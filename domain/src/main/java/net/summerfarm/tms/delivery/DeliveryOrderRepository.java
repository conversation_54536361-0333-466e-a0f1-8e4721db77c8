package net.summerfarm.tms.delivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.delivery.entity.query.DeliveryOrderRelatedQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/9/13 18:20<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryOrderRepository {

    /**
     * 根据主键获取信息
     *
     * @param id 主键
     * @return 返回
     */
    DeliveryOrderEntity query(Long id);

    /**
     * 根据UK获取信息
     *
     * @param distId
     * @param beginSiteId
     * @param endSiteId
     * @param batchId
     * @param type
     * @return
     */
    DeliveryOrderEntity queryByUk(Long distId, Long beginSiteId, Long endSiteId, Long batchId, Integer type);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param deliveryOrderQuery
     * @return
     */
    List<DeliveryOrderEntity> queryList(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param deliveryOrderQuery
     * @return
     */
    PageInfo<DeliveryOrderEntity> queryPage(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param id
     * @return
     */
    DeliveryOrderEntity queryDetail(Long id);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param deliveryOrderQuery
     * @return
     */
    List<DeliveryOrderEntity> queryListWithDetail(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param id
     * @return
     */
    DeliveryOrderEntity queryWithItem(Long id);

    /**
     * 根据承运单id获取承运单点位信息
     *
     * @param deliveryOrderQuery
     * @return
     */
    List<DeliveryOrderEntity> queryListWithItem(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 根据承运单获取信息
     *
     * @param deliveryOrderQuery
     * @return
     */
    List<DeliveryOrderEntity> queryListWithSiteName(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 配送单的保存
     * 标准 有则覆盖 没有则新增
     *
     * @param deliveryOrderEntity
     */
    void saveOrUpdate(DeliveryOrderEntity deliveryOrderEntity);

    /**
     * 配送单的更新
     * 有则更新 无则报错
     *
     * @param deliveryOrderEntity
     */
    void update(DeliveryOrderEntity deliveryOrderEntity);


    /**
     * 根据委托单ID删除配送单
     * 按主键删除
     */
    void remove(Long distId);

    /**
     * 根据委托单ID删除配送单
     * batchRemoveByXXX
     *
     * @param distId 按委托单ID为条件删除
     */
    void batchRemoveByDistId(Long distId);

    /**
     * 根据更新配送单批次id
     *
     * @param deliveryOrderEntity 配送单信息
     * @param batchId             批次信息
     */
    void updateBatchId(DeliveryOrderEntity deliveryOrderEntity, Long batchId);

    /**
     * 配送单新增
     *
     * @param orderEntity 配送单信息
     */
    void save(DeliveryOrderEntity orderEntity);

    /**
     * 根据主键删除
     *
     * @param deliveryOrderId 配送单id
     */
    void removeByPrimaryId(Long deliveryOrderId);

    /**
     * 查询配送单和配送批次详情
     * @param deliveryOrderQuery 查询条件
     * @return 结果
     */
    List<DeliveryOrderEntity> queryListWithBatchDetail(DeliveryOrderQuery deliveryOrderQuery);

    /**
     * 根据配送时间查询相同起点、终点、配送时间配送单不同批次数据
     * @param deliveryTime 配送时间
     * @return 结果
     */
    Boolean diffBatchHandle(LocalDateTime deliveryTime);

    /**
     * 更新配送单的批次ID
     * @param deliveryOrderEntities 配送单集合
     * @param deliveryBatchId 批次ID
     */
    void updateDeliveryBatchId(List<DeliveryOrderEntity> deliveryOrderEntities, Long deliveryBatchId);

    /**
     * 批量保存
     * @param deliveryOrderEntities 配送单集合
     */
    void saveBatch(List<DeliveryOrderEntity> deliveryOrderEntities);

    /**
     * 批量更新
     * @param deliveryOrderEntities
     */
    void batchUpdate(List<DeliveryOrderEntity> deliveryOrderEntities);

    /**
     * 根据条件查询配送单相关数据
     *
     * <AUTHOR>
     * @date 2025/2/25 15:24
     */
    List<DeliveryOrderEntity> selectListByCondition(DeliveryOrderRelatedQuery deliveryOrderRelatedQuery);

}
