package net.summerfarm.tms.delivery;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.delivery.entity.DeliveryItemEntity;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemEntity;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistClientVO;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.enums.DeliveryOrderStatusEnum;
import net.summerfarm.tms.enums.DeliveryOrderTypeEnum;
import net.summerfarm.tms.enums.DistOrderCancelTypeEnum;
import net.summerfarm.tms.enums.DistOrderStatusEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.dist.DistOrderMark;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/9/13 18:24<br/>
 *
 * <AUTHOR> />
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DeliveryOrderDomainService {
    @Resource
    DistOrderRepository distOrderRepository;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final DistOrderDomainService distOrderDomainService;
    private final DeliveryItemRepository deliveryItemRepository;

    @Transactional
    public List<DeliveryOrderEntity> finishDelivery(Long deliveryBatchId, DeliverySiteEntity deliverySiteEntity) {
        DeliveryOrderQuery deliveryOrderQuery = new DeliveryOrderQuery();
        deliveryOrderQuery.setBatchId(deliveryBatchId);
        deliveryOrderQuery.setEndSiteId(deliverySiteEntity.getSiteId());
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithItem(deliveryOrderQuery);
        //按次序分配缺货量
        deliveryOrderEntityList.sort(Comparator.comparing(DeliveryOrderEntity::getSource)
                .thenComparing(DeliveryOrderEntity::getDistOrderId));
        Map<String, Integer> sku2ShortCntMap = new HashMap<>();
        Map<String, List<DeliverySiteItemEntity>> itemShortMap = deliverySiteEntity.getDeliverySiteItemEntityList().stream().collect(Collectors.groupingBy(DeliverySiteItemEntity::getOutItemId));
        for (String key : itemShortMap.keySet()) {
            int sum = itemShortMap.get(key).stream().mapToInt(DeliverySiteItemEntity::getShortCount).sum();
            sku2ShortCntMap.put(key, sum);
        }
        //处理每个配送单的每个配送SKU的数量
        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
            //关闭了无需处理
            if(deliveryOrderEntity.getStatus().getCode() == DeliveryOrderStatusEnum.CLOSE.getCode()){
                continue;
            }
            deliveryOrderEntity.setStatus(DeliveryOrderStatusEnum.SIGN_SUC);
            if (deliveryOrderEntity.getType() != DeliveryOrderTypeEnum.send.getCode()) {
                continue;
            }
            for (DeliveryItemEntity deliveryItemEntity : deliveryOrderEntity.getDeliveryItemEntityList()) {
                Integer shortCnt = sku2ShortCntMap.get(deliveryItemEntity.getOutItemId());
                if (shortCnt == null) {
                    shortCnt = 0;
                }
                //计算配送的各个数量
                int realCnt = deliveryItemEntity.getPlanReceiptCount() - deliveryItemEntity.getInterceptCount()
                        - deliveryItemEntity.getRejectCount();
                TmsAssert.isTrue(realCnt >= 0, ErrorCodeEnum.DB_DATA_ERROR, "实际配送数量<0");
                int realReceipt = realCnt - shortCnt;
                deliveryItemEntity.setRealReceiptCount(Math.max(realReceipt, 0));
                deliveryItemEntity.setShortCount(realCnt - deliveryItemEntity.getRealReceiptCount());
                shortCnt -= deliveryItemEntity.getShortCount();
                sku2ShortCntMap.put(deliveryItemEntity.getOutItemId(), shortCnt);
                //判断配送单状态
                if (deliveryItemEntity.getShortCount() > 0 || deliveryItemEntity.getRejectCount() > 0) {
                    deliveryOrderEntity.setStatus(DeliveryOrderStatusEnum.SIGN_ERROR);
                }
            }
        }
        //持久化更新配送单
        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
            deliveryOrderRepository.update(deliveryOrderEntity);
        }
        return deliveryOrderEntityList;
    }

    /**
     * 配送单的保存
     *
     * @param deliveryOrderEntity
     */
    public DeliveryOrderEntity create(DeliveryOrderEntity deliveryOrderEntity) {
        deliveryOrderRepository.saveOrUpdate(deliveryOrderEntity);
        distOrderDomainService.bindDeliveryBatchSuc(deliveryOrderEntity.getDistOrderId());
        return deliveryOrderEntity;
    }


    /**
     * 重置配送关系
     *
     * @param distId
     */
    @Transactional
    public void reset4DistOrder(Long distId) {
        DistOrderEntity distOrderEntity = distOrderDomainService.resetInitState(distId);

        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().distOrderId(distOrderEntity.getDistId()).build());
        deliveryOrderRepository.batchRemoveByDistId(distId);

        DeliveryOrderEntity updateTemp = deliveryOrderEntityList.get(0);
        updateTemp.setDeliveryBatchId(null);
        updateTemp.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
        if (distOrderEntity.getMidSite() != null && distOrderEntity.getMidSite().getId() != null) {
            updateTemp.setId(null);
            updateTemp.setBeginSiteId(distOrderEntity.getBeginSite().getId());
            updateTemp.setEndSiteId(distOrderEntity.getMidSite().getId());
            deliveryOrderRepository.saveOrUpdate(updateTemp);
            updateTemp.setId(null);
            updateTemp.setBeginSiteId(distOrderEntity.getMidSite().getId());
            updateTemp.setEndSiteId(distOrderEntity.getEndSite().getId());
            deliveryOrderRepository.saveOrUpdate(updateTemp);
        } else {
            updateTemp.setId(null);
            updateTemp.setBeginSiteId(distOrderEntity.getBeginSite().getId());
            updateTemp.setEndSiteId(distOrderEntity.getEndSite().getId());
            deliveryOrderRepository.saveOrUpdate(updateTemp);
        }
    }

    /**
     * 更新配送单信息
     *
     * @param deliveryOrderEntities
     */
    public void updateBatchId(List<DeliveryOrderEntity> deliveryOrderEntities, Long deliveryBatchId) {
        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntities) {
            deliveryOrderEntity.setDeliveryBatchId(deliveryBatchId);
            try {
                deliveryOrderRepository.update(deliveryOrderEntity);
            } catch (DuplicateKeyException e) {
                if (deliveryOrderEntity.getId() != null) {
                    deliveryOrderRepository.removeByPrimaryId(deliveryOrderEntity.getId());
                }
            }
        }
    }

    /**
     * @param batchId                 批次id
     * @param deliveryOrderEntityList 配送单集合
     * @param toBeWirdeList           需变成带承运委托单集合
     * @param inDeliveryList          需变成承运在委托单集合
     */
    public void confirmDeliveryPlan(Long batchId, List<DeliveryOrderEntity> deliveryOrderEntityList,
                                    List<DistOrderEntity> toBeWirdeList, List<DistOrderEntity> inDeliveryList) {

        List<Long> deliveryOrderIds = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList());
        //查询状态不是关闭的承运单信息
        DeliveryOrderQuery deliveryOrderQuery = DeliveryOrderQuery.builder()
                .batchId(batchId)
                .neState(DeliveryOrderStatusEnum.CLOSE.getCode())
                .notInDeliveryOrderIds(deliveryOrderIds).build();
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(deliveryOrderQuery);
        if (!CollectionUtils.isEmpty(deliveryOrderEntities)) {
            //先删除
            deliveryOrderEntities.forEach(deliveryOrderEntity -> {
                DeliveryOrderQuery orderQuery = DeliveryOrderQuery.builder()
                        .distOrderId(deliveryOrderEntity.getDistOrderId())
                        .beginSiteId(deliveryOrderEntity.getBeginSiteId())
                        .endSiteId(deliveryOrderEntity.getEndSiteId())
                        .neState(DeliveryOrderStatusEnum.CLOSE.getCode()).build();
                List<DeliveryOrderEntity> orderEntities = deliveryOrderRepository.queryList(orderQuery);
                if (!CollectionUtils.isEmpty(orderEntities)) {
                    if (orderEntities.size() > 1) {
                        deliveryOrderRepository.removeByPrimaryId(deliveryOrderEntity.getId());
                    } else {
                        deliveryOrderRepository.updateBatchId(deliveryOrderEntity, null);
                        toBeWirdeList.add(distOrderRepository.query(deliveryOrderEntity.getDistOrderId()));
                    }
                }
            });
        }

        //保存
        deliveryOrderEntityList.forEach(deliveryOrderEntity -> {
            DeliveryOrderEntity deliveryOrder = deliveryOrderRepository.query(deliveryOrderEntity.getId());
            //说明已经被删除，承运单被关闭了
            if (deliveryOrder == null) {
                throw new TmsRuntimeException(ErrorCodeEnum.DIST_BIND_ERROR);
            }
            if (deliveryOrder.getDeliveryBatchId() == null) {
                deliveryOrderRepository.updateBatchId(deliveryOrder, batchId);
                inDeliveryList.add(distOrderRepository.query(deliveryOrder.getDistOrderId()));
            } else {
                //查询是否存在
                if (!Objects.equals(deliveryOrder.getDeliveryBatchId(), batchId)) {
                    //新增
                    deliveryOrder.setId(null);
                    deliveryOrder.setDeliveryBatchId(batchId);
                    deliveryOrder.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
                    deliveryOrder.setType(DeliveryOrderTypeEnum.send.getCode());

                    deliveryOrderRepository.save(deliveryOrder);

                    inDeliveryList.add(distOrderRepository.query(deliveryOrder.getDistOrderId()));
                }
            }
        });

    }

    /**
     * 关闭配送单
     *
     * @param deliveryOrderEntities 配送单信息
     */
    public void deliveryOrderClose(List<DeliveryOrderEntity> deliveryOrderEntities) {
        if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
            return;
        }
        deliveryOrderEntities.forEach(deliveryOrderVO -> {
            deliveryOrderVO.setStatus(DeliveryOrderStatusEnum.CLOSE);
            deliveryOrderRepository.update(deliveryOrderVO);

            //查询承运单是否还有其他调度承运
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryListWithSiteName(
                    DeliveryOrderQuery.builder().distOrderId(deliveryOrderVO.getDistOrderId()).build());
            List<DeliveryOrderEntity> openDeliveryOrders = deliveryOrderEntityList.stream()
                    .filter(d -> d.getDeliveryBatchId() != null)
                    .filter(d -> d.getStatus().getCode() != DeliveryOrderStatusEnum.CLOSE.getCode()).collect(Collectors.toList());

            //没有的话更改承运单状态
            if (openDeliveryOrders.size() == 0) {
                DistOrderEntity distOrderEntity = distOrderRepository.query(deliveryOrderVO.getDistOrderId());
                distOrderEntity.setStatus(DistOrderStatusEnum.TO_BE_WIRED);
                distOrderRepository.update(distOrderEntity);
            }
        });
    }

    /**
     * 订单拦截
     *
     * @param distId 委托单id
     * @return
     */
    public DeliveryOrderEntity intercept(Long distId) {
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder().distOrderId(distId).build());
        List<DeliveryOrderEntity> orderEntities = deliveryOrderEntities.stream().filter(deliveryOrderEntity -> Objects.equals(deliveryOrderEntity.getType(), DeliveryOrderTypeEnum.send.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderEntities)) {
            return null;
        }
        for (DeliveryOrderEntity orderEntity : orderEntities) {
            orderEntity.setStatus(DeliveryOrderStatusEnum.CLOSE);
            deliveryOrderRepository.update(orderEntity);
        }

        DeliveryOrderEntity deliveryOrderEntity = orderEntities.get(0);
        List<DeliveryItemEntity> deliveryItemEntityList = deliveryOrderEntity.getDeliveryItemEntityList();
        if (CollectionUtils.isEmpty(deliveryItemEntityList)) {
            return deliveryOrderEntity;
        }
        for (DeliveryItemEntity deliveryItemEntity : deliveryItemEntityList) {
            deliveryItemEntity.setInterceptCount(deliveryItemEntity.getPlanReceiptCount());
            deliveryItemRepository.update(deliveryItemEntity);
        }
        return deliveryOrderEntity;
    }

    /**
     * 完成排线--生成配送单详情、修改委托单状态
     *
     * @param deliveryOrderEntityList
     */
    public void completePath(List<DeliveryOrderEntity> deliveryOrderEntityList) {
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return;
        }

        List<Long> distIdList = deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(DistOrderQuery.builder().distIdList(distIdList).build());
        Map<Long, DistOrderEntity> distOrderEntityMap = distOrderEntities.stream().collect(Collectors.toMap(DistOrderEntity::getDistId, Function.identity()));

        ArrayList<DeliveryItemEntity> saveDeliveryItemList = new ArrayList<>();
        ArrayList<DistOrderEntity> updateDistOrderEntityList = new ArrayList<>();

        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
            DistOrderEntity distOrderEntity = distOrderEntityMap.get(deliveryOrderEntity.getDistOrderId());
            //DistOrderEntity distOrderEntity = distOrderRepository.queryWithItem(deliveryOrderEntity.getDistOrderId());
            List<DistItemVO> distItems = distOrderEntity.getDistItems();

            List<DeliveryItemEntity> deliveryItemEntities = new ArrayList<>();
            for (DistItemVO distItem : distItems) {
                //配送类型相同添加
                if (Objects.equals(deliveryOrderEntity.getType(), distItem.getDeliveryType())) {
                    DeliveryItemEntity deliveryItemEntity = new DeliveryItemEntity();

                    deliveryItemEntity.setDeliveryOrderId(deliveryOrderEntity.getId());
                    deliveryItemEntity.setDistOrderId(distItem.getDistOrderId());
                    deliveryItemEntity.setOutOrderId(deliveryOrderEntity.getOuterOrderId());
                    deliveryItemEntity.setOutItemId(distItem.getOutItemId());
                    deliveryItemEntity.setPlanReceiptCount(distItem.getQuantity());
                    deliveryItemEntity.setInterceptCount(distOrderEntity.getCancelType() == DistOrderCancelTypeEnum.intercept ? distItem.getQuantity() : 0);
                    deliveryItemEntity.setOutItemName(distItem.getOutItemName());
                    deliveryItemEntity.setOutItemType(distItem.getItemTypeEnum().getCode());
                    deliveryItemEntity.setPackType(distItem.getPackType());
                    deliveryItemEntity.setTemperature(distItem.getTemperatureEnum() != null ? distItem.getTemperatureEnum().getCode() : null);
                    saveDeliveryItemList.add(deliveryItemEntity);
                    //deliveryItemRepository.save(deliveryItemEntity);
                    deliveryItemEntities.add(deliveryItemEntity);
                }
            }

            //修改委托单状态为待拣货,不为取消的不改状态
            if (!Objects.equals(distOrderEntity.getStatus().getCode(), DistOrderStatusEnum.CANCEL_BEFORE_WIRED.getCode())) {
                distOrderEntity.setStatus(DistOrderStatusEnum.TO_BE_PICKED);
                updateDistOrderEntityList.add(distOrderEntity);
            }
            //distOrderDomainService.editDistOrder(distOrderEntity);

            deliveryOrderEntity.setDeliveryItemEntityList(deliveryItemEntities);
        }
        deliveryItemRepository.saveBatch(saveDeliveryItemList);
        distOrderDomainService.completePath(updateDistOrderEntityList,DistOrderStatusEnum.TO_BE_PICKED);
    }

    public void changeSiteBatch(List<DeliveryOrderEntity> deliveryOrderEntities, Long deliveryBatchId) {
        if(CollectionUtils.isEmpty(deliveryOrderEntities) || deliveryBatchId == null){
            return;
        }
        List<Long> deliveryOrderIds = deliveryOrderEntities.stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList());
        log.info("DeliveryOrderDomainService changeSiteBatch req data deliveryOrderIds:{},deliveryBatchId:{}",JSON.toJSONString(deliveryOrderIds),deliveryBatchId);
        //过滤出需要修改委托单集合信息
        List<DeliveryOrderEntity> needChangeBatchDeliveryOrders = deliveryOrderEntities.stream()
                .filter(deliveryOrderEntity -> deliveryOrderEntity.getId() != null)
                .filter(deliveryOrderEntity -> !Objects.equals(deliveryOrderEntity.getDeliveryBatchId(), deliveryBatchId))
                .collect(Collectors.toList());
        //批量更新
        deliveryOrderRepository.updateDeliveryBatchId(needChangeBatchDeliveryOrders,deliveryBatchId);
    }

    /**
     * 配送点位移除
     *
     * @param deliveryOrderEntities 配送单
     */
    public void siteRemove(List<DeliveryOrderEntity> deliveryOrderEntities) {
        if (CollectionUtils.isEmpty(deliveryOrderEntities)) {
            return;
        }
        deliveryOrderRepository.updateDeliveryBatchId(deliveryOrderEntities,deliveryOrderEntities.get(0).getDeliveryBatchId());
    }

    /**
     * 批量拦截 为延期处理（和当前拦截逻辑不一样的是回收单也会拦截）
     * @param haveCompletedDeliveryOrderEntityList 已经完成排线的配送单
     */
    public void batchInterceptToDelayedDelivery(List<DeliveryOrderEntity> haveCompletedDeliveryOrderEntityList) {
        if(CollectionUtils.isEmpty(haveCompletedDeliveryOrderEntityList)){
            return;
        }
        log.info("DeliveryOrderDomainService batchInterceptToDelayedDelivery req :{}", JSON.toJSONString(haveCompletedDeliveryOrderEntityList));
        // 1.状态变更
        List<DeliveryOrderEntity> interceptDeliveryOrderEntities = new ArrayList<>();
        haveCompletedDeliveryOrderEntityList.forEach(deliveryOrderEntity -> {
            interceptDeliveryOrderEntities.add(deliveryOrderEntity.interceptDeliveryOrder());
        });
        log.info("DeliveryOrderDomainService batchInterceptToDelayedDelivery update deliveryOrder :{}", JSON.toJSONString(interceptDeliveryOrderEntities));
        deliveryOrderRepository.batchUpdate(interceptDeliveryOrderEntities);

        // 2.订单项拦截数量处理
        List<DeliveryItemEntity> deliveryItemEntities = haveCompletedDeliveryOrderEntityList.stream().map(deliveryOrder -> deliveryOrder.getDeliveryItemEntityList()).flatMap(Collection::stream).collect(Collectors.toList());
        List<DeliveryItemEntity> deliveryItemInterceptList = new ArrayList<>();
        deliveryItemEntities.forEach(item ->{
            deliveryItemInterceptList.add(item.interceptItem());
        });
        log.info("DeliveryOrderDomainService batchInterceptToDelayedDelivery update deliveryItem :{}", JSON.toJSONString(deliveryItemInterceptList));
        deliveryItemRepository.batchUpdate(deliveryItemInterceptList);

    }

    /**
     * 根据UK查询对应的委托单信息
     * @param reqDistOrderEntities 委托单集合
     * @return 结果
     */
    public List<DistOrderEntity> queryListByUK(List<DistOrderEntity> reqDistOrderEntities) {
        if(CollectionUtils.isEmpty(reqDistOrderEntities)){
            return Collections.emptyList();
        }
        List<String> outOrderIdList = reqDistOrderEntities.stream()
                .map(e -> e.getDistClientVO() != null ? e.getDistClientVO().getOutOrderId() : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(outOrderIdList)){
            return Collections.emptyList();
        }

        // 根据外部单号进行查询
        List<DistOrderEntity> dbDistOrderList = distOrderRepository.queryListWithDeliveryOrder(DistOrderQuery.builder().outerOrderIds(outOrderIdList).build());
        if(CollectionUtils.isEmpty(dbDistOrderList)){
            return Collections.emptyList();
        }

        // UK进行匹配
        List<String> reqUkList = reqDistOrderEntities.stream().map(DistOrderEntity::buildUk).collect(Collectors.toList());
        return dbDistOrderList.stream().filter(dbDistOrder -> reqUkList.contains(dbDistOrder.buildUk())).collect(Collectors.toList());
    }

    /**
     * 根据委托单查询对应的配送单信息
     * @param distOrderList 委托单集合
     * @return 结果
     */
    public List<DeliveryOrderEntity> queryListWithItemUseDistSomeFieldByDistOrderList(List<DistOrderEntity> distOrderList) {
        if(CollectionUtils.isEmpty(distOrderList)){
            return Collections.emptyList();
        }
        Map<Long, DistOrderEntity> distId2DistOrderMap = distOrderList.stream().collect(Collectors.toMap(DistOrderEntity::getDistId, Function.identity()));
        List<Long> haveCompleteDistIdList = distOrderList.stream().map(DistOrderEntity::getDistId).collect(Collectors.toList());

        List<DeliveryOrderEntity> haveCompletedCouldInterceptDeliveryOrderEntityList = deliveryOrderRepository.queryListWithItem(DeliveryOrderQuery.builder().distIdList(haveCompleteDistIdList).build());

        // 拣货模式、租户信息、品牌名称
        haveCompletedCouldInterceptDeliveryOrderEntityList.forEach(deliveryOrderEntity -> {
            DistOrderEntity distOrderEntity = distId2DistOrderMap.get(deliveryOrderEntity.getDistOrderId());
            if(distOrderEntity == null){
                return;
            }
            DistClientVO distClientVO = distOrderEntity.getDistClientVO();
            deliveryOrderEntity.setPickType(distOrderEntity.getPickType());
            deliveryOrderEntity.setOutTenantId(distClientVO.getOutTenantId());
            deliveryOrderEntity.setOutBrandName(distClientVO.getOutBrandName());
        });

        return haveCompletedCouldInterceptDeliveryOrderEntityList;
    }
}
