package net.summerfarm.tms.delivery;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.Constants;
import net.summerfarm.tms.base.driver.DriverRepository;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.path.PathRepository;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.common.EventBusService;
import net.summerfarm.tms.delivery.entity.*;
import net.summerfarm.tms.delivery.vo.BatchLoadRatioVO;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistStaticVO;
import net.summerfarm.tms.enums.*;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.message.out.CalcBatchLoadRatioMessage;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.query.base.carrier.SiteQuery;
import net.summerfarm.tms.query.base.driver.DriverQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;
import net.summerfarm.tms.query.dist.DistOrderQuery;
import net.summerfarm.tms.util.DistanceUtil;
import net.xianmu.common.exception.BizException;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Date:2022/7/8
 * <p>
 */
@Slf4j
@Service
public class DeliveryBatchDomainService {

    @Resource
    private DeliveryBatchRepository deliveryBatchRepository;
    @Resource
    private DeliverySiteRepository deliverySiteRepository;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;
    @Resource
    private SiteRepository siteRepository;
    @Resource
    private DistOrderDomainService distOrderDomainService;
    @Resource
    private DeliveryOrderDomainService deliveryOrderDomainService;
    @Resource
    private DeliverySectionRepository deliverySectionRepository;
    @Resource
    private EventBusService eventBusService;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private DeliveryBatchValidator deliveryBatchValidator;
    @Resource
    private DriverRepository driverRepository;
    @Resource
    private PathRepository pathRepository;

    public void finishPick(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        deliveryBatchEntity.setId(deliveryBatchId);
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.IN_DELIVERY);
        deliveryBatchEntity.setPickUpTime(LocalDateTime.now());
        deliveryBatchRepository.update(deliveryBatchEntity);
        //查询批次下面对应的委托单
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().batchId(deliveryBatchId).build());
        List<Long> distOrderIdList = deliveryOrderEntities.stream()
                .filter(deliveryOrderEntity -> deliveryOrderEntity.getStatus() != DeliveryOrderStatusEnum.CLOSE)
                .map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList());
        distOrderDomainService.finishPick(distOrderIdList);
    }

    public DeliveryBatchEntity finishDelivery(Long deliveryBatchId) {
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchId);

        if (deliveryBatchEntity.getType() != DeliveryBatchTypeEnum.city.getCode()) {
            deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
            deliveryBatchEntity.setFinishDeliveryTime(LocalDateTime.now());
            deliveryBatchRepository.update(deliveryBatchEntity);
        } else {
            DeliverySiteQuery deliverySiteQuery = new DeliverySiteQuery();
            deliverySiteQuery.setBatchId(deliveryBatchId);
            deliverySiteQuery.setNotStatus(DeliverySiteStatusEnum.FINISH_DELIVERY.getCode());
            deliverySiteQuery.setNotInterceptState(DeliverySiteInterceptStateEnum.allIntecept.getCode());
            deliverySiteQuery.setSendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());
            //忽略城配仓的点位,普通配送没有完成的点位
            long normalSendNotFinishNum = deliverySiteRepository.queryCount(deliverySiteQuery);

            deliverySiteQuery.setSendWay(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue());
            //专车配送没有完成的点位
            long specialSendNotFinishNum = deliverySiteRepository.queryCount(deliverySiteQuery);

            deliverySiteQuery.setSendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue());
            //获取正常配送的所有点位数量
            deliverySiteQuery.setNotStatus(null);
            long totalNormalSendNum = deliverySiteRepository.queryCount(deliverySiteQuery);
            if (normalSendNotFinishNum == 1L && specialSendNotFinishNum == 0L && totalNormalSendNum > 1) {
                updateCompleteDeliveryBatch(deliveryBatchId, deliveryBatchEntity, DeliverySiteEnums.SendWay.NORMAL_SEND);
            } else if (specialSendNotFinishNum == 0L && totalNormalSendNum == 1L) {
                // 当正常配送的点位数量为1，且专车未配送的数量为1的时候计算专车的里程
                updateCompleteDeliveryBatch(deliveryBatchId, deliveryBatchEntity, DeliverySiteEnums.SendWay.SPECIAL_SEND);

            }
        }
        return deliveryBatchRepository.getBatchDetail(deliveryBatchId);
    }

    private void updateCompleteDeliveryBatch(Long deliveryBatchId, DeliveryBatchEntity deliveryBatchEntity, DeliverySiteEnums.SendWay sendWay) {
        // 普通配送的时候，城配仓的配送方式是普通配送，所以当全部专车配送的时候需要查询出所有的点位信息，此时不需要加上配送方式的查询条件
        DeliverySiteQuery queryBuilder = DeliverySiteQuery.builder()
                .batchId(deliveryBatchId)
                .build();
        if (Objects.equals(DeliverySiteEnums.SendWay.NORMAL_SEND, sendWay)) {
            queryBuilder.setSendWay(sendWay.getValue());
        }

        List<DeliverySiteEntity> siteEntities = deliverySiteRepository.queryList(queryBuilder);

        //获取城配仓点位，城配仓点位可能是无需打卡没有打卡时间
        List<DeliverySiteEntity> citySiteList = siteEntities.stream()
                .filter(deliverySiteEntity -> Objects.equals(deliverySiteEntity.getSiteId(), deliveryBatchEntity.getBeginSiteId())).collect(Collectors.toList());
        //客户配送点位
        List<DeliverySiteEntity> sendSiteList = siteEntities.stream()
                .filter(deliverySiteEntity -> !Objects.equals(deliverySiteEntity.getSiteId(), deliveryBatchEntity.getBeginSiteId()))
                .filter(deliverySiteEntity -> !Objects.equals(DeliverySiteInterceptStateEnum.allIntecept.getCode(), deliverySiteEntity.getInterceptState()))
                .filter(deliverySiteEntity -> DeliverySiteStatusEnum.FINISH_DELIVERY == deliverySiteEntity.getStatus())
                .filter(deliverySiteEntity -> deliverySiteEntity.getSignInTime() != null)
                .collect(Collectors.toList());

        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.COMPLETE_DELIVERY);
        deliveryBatchEntity.setFinishDeliveryTime(LocalDateTime.now());

        sendSiteList.sort(Comparator.comparing(DeliverySiteEntity::getSignInTime));
        List<Long> siteIds = Lists.newArrayList();
        siteIds.add(citySiteList.get(0).getSiteId());
        siteIds.addAll(sendSiteList.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()));

        List<SiteEntity> siteEntitys = siteRepository.queryPrimaryIdList(siteIds);
        Map<Long, SiteEntity> siteEntityMap = siteEntitys.stream().collect(Collectors.toMap(SiteEntity::getId, Function.identity()));
        

        //保存路段信息和实际配送距离
        List<WaypointsInput> waypointsInputList = new ArrayList<>();
        SiteEntity beginSite = siteEntityMap.get(citySiteList.get(0).getSiteId());
        waypointsInputList.add(WaypointsInput.builder().siteId(beginSite.getId()).poi(beginSite.getPoi()).build());
        for (DeliverySiteEntity siteEntity : sendSiteList) {
            SiteEntity sendSite = siteEntityMap.get(siteEntity.getSiteId());
            if(Objects.nonNull(sendSite)) {
                waypointsInputList.add(WaypointsInput.builder().siteId(sendSite.getId()).poi(sendSite.getPoi()).build());
            }
        }
        deliveryBatchRepository.update(deliveryBatchEntity);

        //异步处理
        CalcTmsPathDistanceMessage calcIntelligencePath = CalcTmsPathDistanceMessage.builder()
                .batchId(deliveryBatchEntity.getId())
                .type(DeliverySectionEnums.Type.FINISH_SEND).waypointsInputList(waypointsInputList).build();
        eventBusService.calcPathDistance(calcIntelligencePath);
    }

    /**
     * 批次单的保存
     *
     * @param deliveryBatchEntity 保存
     */
    public void createDeliveryBatch(DeliveryBatchEntity deliveryBatchEntity) {
        deliveryBatchRepository.save(deliveryBatchEntity);
        //配送路线的保存
        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliverySiteList())) {
            //城配特殊处理
            if (Objects.equals(deliveryBatchEntity.getType(), DeliveryBatchTypeEnum.city.getCode())) {
                List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
                for (int i = 0; i < deliverySiteList.size(); i++) {
                    if (i == 0) {
                        deliverySiteList.get(i).setSequence(0);
                    } else {
                        //查询数量
                        long num = deliverySiteRepository.queryCount(DeliverySiteQuery.builder()
                                .batchId(deliveryBatchEntity.getId())
                                .sendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())
                                .interceptState(DeliverySiteInterceptStateEnum.normal.getCode())
                                .build());
                        if (num == 0) {
                            deliverySiteList.get(i).setSequence(1);
                        } else {
                            deliverySiteList.get(i).setSequence(Integer.parseInt(String.valueOf(num)));
                        }
                    }
                }
            } else {
                List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
                for (int i = 0; i < deliverySiteList.size(); i++) {
                    deliverySiteList.get(i).setSequence(i + 1);
                }
            }
            deliverySiteRepository.save(deliveryBatchEntity.getDeliverySiteList(), deliveryBatchEntity.getId());
        }
        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliveryOrderEntityList())) {
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
            //配送单是新增还是更新
            for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
                //判断数据批次为空是否存在，存在更新，不存在新增
                DeliveryOrderEntity orderEntity = deliveryOrderRepository.query(deliveryOrderEntity.getId());
                //说明已经被删除，承运单被关闭了
                if (orderEntity.getId() == null) {
                    throw new TmsRuntimeException(ErrorCodeEnum.DIST_BIND_ERROR);
                }
                if (orderEntity.getDeliveryBatchId() == null) {
                    deliveryOrderDomainService.updateBatchId(Arrays.asList(orderEntity), deliveryBatchEntity.getId());
                } else {
                    if (!Objects.equals(orderEntity.getDeliveryBatchId(), deliveryBatchEntity.getId())) {
                        //新增
                        orderEntity.setId(null);
                        orderEntity.setDeliveryBatchId(deliveryBatchEntity.getId());
                        orderEntity.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
                       // orderEntity.setType(DeliveryOrderTypeEnum.send.getCode());
                        deliveryOrderRepository.save(orderEntity);
                    }
                }
            }
        }

    }


    /**
     * 路由保存调度单信息
     *
     * @param deliveryBatchEntity 保存
     */
    public void createDeliveryBatchByPath(DeliveryBatchEntity deliveryBatchEntity) {
        deliveryBatchRepository.saveByPath(deliveryBatchEntity);
        //配送路线的保存
        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliverySiteList())) {
            //城配特殊处理
            if (Objects.equals(deliveryBatchEntity.getType(), DeliveryBatchTypeEnum.city.getCode())) {
                List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
                for (int i = 0; i < deliverySiteList.size(); i++) {
                    if (i == 0) {
                        deliverySiteList.get(i).setSequence(0);
                    } else {
                        //查询数量
                        long num = deliverySiteRepository.queryCount(DeliverySiteQuery.builder()
                                .batchId(deliveryBatchEntity.getId())
                                .sendWay(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue())
                                .interceptState(DeliverySiteInterceptStateEnum.normal.getCode())
                                .build());
                        if (num == 0) {
                            deliverySiteList.get(i).setSequence(1);
                        } else {
                            deliverySiteList.get(i).setSequence(Integer.parseInt(String.valueOf(num)));
                        }
                    }
                }
            } else {
                List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();
                for (int i = 0; i < deliverySiteList.size(); i++) {
                    deliverySiteList.get(i).setSequence(i + 1);
                }
            }
            deliverySiteRepository.save(deliveryBatchEntity.getDeliverySiteList(), deliveryBatchEntity.getId());
        }
        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliveryOrderEntityList())) {
            List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryBatchEntity.getDeliveryOrderEntityList();
            //配送单是新增还是更新
            for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntityList) {
                //判断数据批次为空是否存在，存在更新，不存在新增
                DeliveryOrderEntity orderEntity = deliveryOrderRepository.query(deliveryOrderEntity.getId());
                //说明已经被删除，承运单被关闭了
                if (orderEntity.getId() == null) {
                    throw new TmsRuntimeException(ErrorCodeEnum.DIST_BIND_ERROR);
                }
                if (orderEntity.getDeliveryBatchId() == null) {
                    deliveryOrderDomainService.updateBatchId(Arrays.asList(orderEntity), deliveryBatchEntity.getId());
                } else {
                    if (!Objects.equals(orderEntity.getDeliveryBatchId(), deliveryBatchEntity.getId())) {
                        //新增
                        orderEntity.setId(null);
                        orderEntity.setDeliveryBatchId(deliveryBatchEntity.getId());
                        orderEntity.setStatus(DeliveryOrderStatusEnum.NO_SIGN);
                        // orderEntity.setType(DeliveryOrderTypeEnum.send.getCode());
                        deliveryOrderRepository.save(orderEntity);
                    }
                }
            }
        }

    }
    

    /**
     * 根据批次id查询详情
     */
    public DeliveryBatchEntity deliveryBatchDetail(Long deliveryBatchId) {
        if(deliveryBatchId == null){
            throw new BizException("根据批次id查询详情 批次Id不能为空");
        }
        //获取批次详情
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.getBatchDetail(deliveryBatchId);
        //根据批次id获取运输线路
        deliveryBatchEntity.setDeliverySiteList(deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder().batchId(deliveryBatchId).build()));
        //获取运输明细
        deliveryBatchEntity.setDeliveryOrderEntityList(deliveryOrderRepository.queryList(
                DeliveryOrderQuery.builder().batchId(deliveryBatchId).build()));
        return deliveryBatchEntity;
    }

    /**
     * 关闭调度单
     *
     * @param deliveryBatchId 调度单id
     * @param closeReason     关闭原因
     */
    public DeliveryBatchEntity deliveryBatchClose(Long deliveryBatchId, String closeReason, String closeUser) {
        //先查询调度单信息
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchId);
        DeliveryBatchStatusEnum status = deliveryBatchEntity.getStatus();
        TmsAssert.condition(Objects.equals(DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode(), status.getCode()) ||
                Objects.equals(DeliveryBatchStatusEnum.DELIVERY_CLOSED.getCode(), status.getCode()), ErrorCodeEnum.BATCH_ERROR, "deliveryBatchId");
        TmsAssert.condition(Objects.equals(DeliveryBatchStatusEnum.IN_DELIVERY.getCode(), status.getCode()) &&
                Objects.equals(deliveryBatchEntity.getType(), DeliveryBatchTypeEnum.all_category_pick.getCode()), ErrorCodeEnum.ALL_CATEGORY_PICK_BATCH_CLOSE_ERROR, "deliveryBatchId");
        //关闭
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.DELIVERY_CLOSED);
        deliveryBatchEntity.setCloseReason(closeReason);
        deliveryBatchEntity.setCloseUser(closeUser);
        deliveryBatchRepository.update(deliveryBatchEntity);
        return deliveryBatchEntity;
    }

    /**
     * 分页查询数据
     *
     * @param deliveryBatchQuery
     * @return
     */
    public PageInfo<DeliveryBatchEntity> queryListWithSite(DeliveryBatchQuery deliveryBatchQuery) {
        int pageIndex = deliveryBatchQuery.getPageIndex();
        int pageSize = deliveryBatchQuery.getPageSize();

        TmsAssert.notNull(pageIndex, ErrorCodeEnum.PARAM_NOT_NULL, "pageIndex");
        TmsAssert.notNull(pageSize, ErrorCodeEnum.PARAM_NOT_NULL, "pageSize");
        //查询主表数据
        PageInfo<DeliveryBatchEntity> deliveryBatchEntityPage = deliveryBatchRepository.queryPage(deliveryBatchQuery);
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchEntityPage.getList();

        if (CollectionUtils.isEmpty(deliveryBatchEntityList)) {
            return deliveryBatchEntityPage;
        }
        List<DeliverySiteEntity> deliverySiteEntityList = deliverySiteRepository.queryList(DeliverySiteQuery.builder()
                .batchIdList(deliveryBatchEntityList.stream()
                        .map(DeliveryBatchEntity::getId).collect(Collectors.toList())).build());

        Map<Long, List<DeliverySiteEntity>> deliverySiteMap = getDeliverySiteMap(deliverySiteEntityList);
        Map<Long, SiteEntity> siteMap = siteRepository.queryMapByIds(deliverySiteEntityList.stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList()));

        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            Long id = deliveryBatchEntity.getId();
            //获取点位信息
            List<DeliverySiteEntity> batchDeliverySiteEntity = deliverySiteMap.get(id);
            if (!CollectionUtils.isEmpty(batchDeliverySiteEntity)) {
                batchDeliverySiteEntity.forEach(deliverySiteVO -> {
                    SiteEntity siteEntity = siteMap.get(deliverySiteVO.getSiteId());
                    if (Objects.nonNull(siteEntity)) {
                        deliverySiteVO.setSiteName(siteEntity.getSiteName());
                    }
                });
                List<DeliverySiteEntity> deliverySiteEntitySortList = batchDeliverySiteEntity.stream()
                        .sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
                deliveryBatchEntity.setDeliverySiteList(deliverySiteEntitySortList);
            }
        }
        return deliveryBatchEntityPage;
    }


    private Map<Long, List<DeliverySiteEntity>> getDeliverySiteMap(List<DeliverySiteEntity> deliverySiteEntities) {
        if (CollectionUtils.isEmpty(deliverySiteEntities)) {
            return Maps.newHashMap();
        }
        return deliverySiteEntities.stream().collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));
    }


    /**
     * 根据委托单查询调度单
     *
     * @param distId
     */
    public List<DeliveryBatchEntity> getDeliveryBatchByDistId(Long distId) {
        TmsAssert.notNull(distId, ErrorCodeEnum.PARAM_NOT_NULL, "distId");
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.getDeliveryBatchByDistId(distId);
        for (DeliveryBatchEntity deliveryBatchEntity : deliveryBatchEntityList) {
            //查询点位信息
            Long id = deliveryBatchEntity.getId();
            List<DeliverySiteEntity> deliverySiteEntityList = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(id).build());
            deliverySiteEntityList.forEach(deliverySiteVO -> {
                SiteEntity siteEntity = siteRepository.query(deliverySiteVO.getSiteId());
                deliverySiteVO.setSiteEntity(siteEntity);
                deliverySiteVO.setSiteName(siteEntity.getSiteName());
            });
            List<DeliverySiteEntity> deliverySiteEntityListSort = deliverySiteEntityList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
            deliveryBatchEntity.setDeliverySiteList(deliverySiteEntityListSort);
        }

        return deliveryBatchEntityList;
    }

    /**
     * 根据承运单id查询关联的调度单是否都已经完成
     *
     * @param distOrderId 承运单id
     * @return Boolean
     * ture   表示承运单关联的调度单已全部完成
     * false 表示存在没有完成的调度单
     */
    public Boolean queryBatchFinishByDistId(Long distOrderId) {
        Boolean flag = true;
        List<DeliveryOrderEntity> deliveryOrderEntities = deliveryOrderRepository.queryListWithSiteName(
                DeliveryOrderQuery.builder().distOrderId(distOrderId).build());
        //需要过滤已关闭的
        deliveryOrderEntities = deliveryOrderEntities.stream().filter(deliveryOrder -> deliveryOrder.getDeliveryBatchId() != null)
                .filter(deliveryOrder -> deliveryOrder.getStatus() != DeliveryOrderStatusEnum.CLOSE).collect(Collectors.toList());
        for (DeliveryOrderEntity deliveryOrderEntity : deliveryOrderEntities) {
            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryOrderEntity.getDeliveryBatchId());
            if (deliveryBatchEntity.getStatus().getCode() != DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode()) {
                flag = false;
            }
        }
        return flag;
    }

//    /**
//     * 创建配送批次
//     * @param deliveryBatchEntity 配送批次实体
//     */
//    public void createDeliveryBatch(DeliveryBatchEntity deliveryBatchEntity) {
//        //配送批次的保存
//        deliveryBatchRepository.saveBatch(deliveryBatchEntity);
//        //配送点位的保存
//        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliverySiteList())) {
//            deliverySiteRepository.saveDeliverySite(deliveryBatchEntity);
//        }
//        //配送单的保存
//        if (CollectionUtils.isNotEmpty(deliveryBatchEntity.getDeliveryOrderVOList())) {
//            deliveryOrderRepository.save(deliveryBatchEntity);
//        }
//    }

    /**
     * 删除配送批次
     *
     * @param deliveryBatchIds 配送批次ID集合
     * @param distId           委托单ID
     */
    public void removeDeliveryBatch(List<Long> deliveryBatchIds, Long distId) {
        if (deliveryBatchIds == null || deliveryBatchIds.isEmpty()) {
            return;
        }
        //查询委托单
        DistOrderEntity distOrderEntity = distOrderDomainService.queryDistOrderById(distId);
        if (distOrderEntity == null) {
            return;
        }
        Long beginSiteId = distOrderEntity.getBeginSite().getId();
        Long endSiteId = distOrderEntity.getEndSite().getId();
        LocalDateTime expectBeginTime = distOrderEntity.getDistFlowVO().getExpectBeginTime();
        for (Long deliveryBatchId : deliveryBatchIds) {
            DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchId);
            if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null) {
                continue;
            }

            // 在删除前先查询要删除的配送点位信息，获取其sequence
            DeliverySiteQuery deliverySiteQuery = DeliverySiteQuery.builder()
                    .batchId(deliveryBatchId)
                    .siteId(endSiteId)
                    .build();
            DeliverySiteEntity deliverySiteToDelete = deliverySiteRepository.query(deliverySiteQuery);

            //配送点位的删除
            int removeRow = deliverySiteRepository.remove(deliveryBatchId, distId, beginSiteId, endSiteId, expectBeginTime);

            // 如果成功找到了要删除的配送点位，需要重新调整排序顺序
            if (deliverySiteToDelete != null && deliverySiteToDelete.getSequence() != null && removeRow > 0) {
                log.info("配送点位删除后，开始调整排序顺序，deliveryBatchId:{}, distId:{},removeRow:{}, 被删除点位sequence：{}",deliveryBatchId, distId, removeRow ,deliverySiteToDelete.getSequence());
                // 调用updateSortSub，将被删除点位sequence之后的所有点位sequence减1
                try {
                    deliverySiteRepository.updateSortSub(deliveryBatchId, deliverySiteToDelete.getSequence(), 10000);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                log.info("排序顺序调整完成，deliveryBatchId：{}, 调整范围：{} - {}",deliveryBatchId, deliverySiteToDelete.getSequence(), 10000);
            }
        }

    }


    public void updatePathName(DeliveryBatchEntity batchEntity, String pathName) {
        batchEntity.setPathName(pathName);
        //配送批次更新
        deliveryBatchRepository.update(batchEntity);
    }

    public void updatePathDriver(DeliveryBatchEntity batchEntity, DriverEntity driverEntity) {
        batchEntity.setDriverId(driverEntity.getId());
        batchEntity.setCarId(driverEntity.getCityCarId());
        batchEntity.setCarrierId(driverEntity.getCityCarrierId());

        //配送批次更新
        deliveryBatchRepository.update(batchEntity);
    }

    public void confirmDeliveryPlan(DeliveryBatchEntity needUpdateBatch) {
        DeliveryBatchEntity deliveryBatch = deliveryBatchRepository.query(needUpdateBatch.getId());
        if(deliveryBatch.getStatus() == DeliveryBatchStatusEnum.TO_BE_WIRED){
            needUpdateBatch.setStatus(DeliveryBatchStatusEnum.TO_BE_PICKED);
            //调度单自动关联处理
            this.autoRelateBatch(needUpdateBatch);
            //保存干线路段距离
            this.deliveryBatchPathSectionDistanceSave(needUpdateBatch);
        }

        Long pathId = needUpdateBatch.getPathId() == null ? deliveryBatch.getPathId() : needUpdateBatch.getPathId();
        LocalDateTime deliveryTime = needUpdateBatch.getDeliveryTime() == null ? deliveryBatch.getDeliveryTime() : needUpdateBatch.getDeliveryTime();
        Long beginSiteId = needUpdateBatch.getBeginSiteId() == null ? deliveryBatch.getBeginSiteId() : needUpdateBatch.getBeginSiteId();
        Long carId = needUpdateBatch.getCarId() == null ? deliveryBatch.getCarId() : needUpdateBatch.getCarId();

        log.info("查询参数为：pathId:{},deliveryTime:{},beginSiteId:{},carId:{}",pathId,deliveryTime,beginSiteId,carId);
        DeliveryBatchEntity ukDeliveryBatchEntity = deliveryBatchRepository.queryByUk(pathId,deliveryTime,beginSiteId,carId);
        if(ukDeliveryBatchEntity != null && ukDeliveryBatchEntity.getId() != null && !Objects.equals(ukDeliveryBatchEntity.getId(), needUpdateBatch.getId())){
            throw new TmsRuntimeException("已存在相同路线、配送时间、起点、车辆的数据");
        }
        //配送批次更新
        deliveryBatchRepository.update(needUpdateBatch);
    }

    public void deliveryBatchPathSectionDistanceSave(DeliveryBatchEntity deliveryBatchEntity) {
        try {
            deliveryBatchEntity = deliveryBatchDetail(deliveryBatchEntity.getId());
            //计算距离和保存路段
            List<DeliverySiteEntity> deliverySiteList = deliveryBatchEntity.getDeliverySiteList();

            List<DeliverySiteEntity> sortedSites = deliverySiteList.stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence)).collect(Collectors.toList());
            List<WaypointsInput> waypointsInputList = new ArrayList<>();
            for (DeliverySiteEntity deliverySiteEntity : sortedSites) {
                SiteEntity siteEntity = siteRepository.query(deliverySiteEntity.getSiteId());
                waypointsInputList.add(WaypointsInput.builder().siteId(siteEntity.getId()).poi(siteEntity.getPoi()).build());
            }
            //异步处理
            CalcTmsPathDistanceMessage calcIntelligencePath = CalcTmsPathDistanceMessage.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .type(DeliverySectionEnums.Type.complete_path).waypointsInputList(waypointsInputList).build();
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        eventBusService.calcPathDistance(calcIntelligencePath);
                    } catch (Throwable e) {
                        log.error("deliveryBatchPathSectionDistanceSave 计算智能路段信息消息发送失败:{}", JSONObject.toJSONString(calcIntelligencePath), e);
                    }
                }
            });
//            deliveryBatchRepository.asyncCalculateDistance(waypointsInputList, deliveryBatchEntity.getId(), DeliverySectionEnums.Type.complete_path);
        } catch (Exception e) {
            log.error("计算智能排线公里数异常", e);
        }
    }

    /**
     * 完成排线
     *
     * @param deliveryBatchEntity 批次
     */
    public void completePath(DeliveryBatchEntity deliveryBatchEntity) {
        //删除批次里面配送点位数量为零（会有一个默认城配仓点位）的批次，排除未排线批次
        if(deliveryBatchEntity.getDeliverySiteList().size() == 1 && deliveryBatchEntity.getPathId() > 0){
            deliveryBatchRepository.remove(deliveryBatchEntity.getId(),deliveryBatchEntity.getBeginSiteId(),deliveryBatchEntity.getEndSiteId());
            return;
        }
        try {
            // 消息计算公里数
            CalcTmsPathDistanceMessage calcTmsPathDistanceMessage = CalcTmsPathDistanceMessage.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .type(DeliverySectionEnums.Type.complete_path)
                    .build();
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        eventBusService.calcPathDistance(calcTmsPathDistanceMessage);
                    } catch (Throwable e) {
                        log.error("计算路段信息消息发送失败:{}", JSONObject.toJSONString(calcTmsPathDistanceMessage), e);
                    }
                }
            });

            // 智能排线公里数
            CalcTmsPathDistanceMessage calcIntelligencePath = CalcTmsPathDistanceMessage.builder()
                    .batchId(deliveryBatchEntity.getId())
                    .type(DeliverySectionEnums.Type.INTELLIGENCE_PATH).build();
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    try {
                        eventBusService.calcPathDistance(calcIntelligencePath);
                    } catch (Throwable e) {
                        log.error("计算智能路段信息消息发送失败:{}", JSONObject.toJSONString(calcTmsPathDistanceMessage), e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("计算智能排线公里数异常",e);
        }
        ArrayList<DeliverySiteEntity> deliverySiteEntities = new ArrayList<>();
        deliverySiteEntities.addAll(deliveryBatchEntity.getDeliverySiteList());
        deliveryBatchEntity.setDeliverySiteList(null);
        deliveryBatchRepository.update(deliveryBatchEntity);
        deliveryBatchEntity.setDeliverySiteList(deliverySiteEntities);
        log.info("完成排线方法结束，批次id：{}，点位顺序:{}",deliveryBatchEntity.getId(), JSON.toJSONString(deliverySiteEntities));
    }

    /**
     * 生成路线code
     *
     * @param deliveryBatchEntityList 批次集合
     * @return 路线code
     */
    public String createPathCode(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        StringBuffer pathCodeSb = new StringBuffer();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(deliveryBatchEntityList)) {
            //路线根据ASCII码倒排
            List<String> paths = deliveryBatchEntityList.stream().map(o -> o.getPathCode()).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
            Integer length = 1;
            if (paths.get(0).contains("Z") || paths.get(0).contains("z")) {
                ArrayList<Integer> pathNumber = new ArrayList<>();
                paths.stream().forEach(pa -> {
                    String pat = pa.toUpperCase();
                    if (pat.contains("Z") && pat.length() > 1) {
                        String[] zpa = pat.split("Z");
                        pathNumber.add(Integer.valueOf(zpa[1]));
                    }
                });
                if (!org.springframework.util.CollectionUtils.isEmpty(pathNumber)) {
                    List<Integer> collect = pathNumber.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
                    length = collect.get(0) + 1;
                }
                pathCodeSb.append("Z").append(length);

            } else {
                char path = (char) (paths.get(0).toCharArray()[0] + 1);
                pathCodeSb.append(path);
            }
        } else {
            //第一条路线默认为A
            pathCodeSb.append("A");
        }

        return pathCodeSb.toString();
    }

    /**
     * 点位变为专车配送
     * @param deliveryBatchEntity 批次信息
     */
    public void changeSpecialCarSendWay(DeliveryBatchEntity deliveryBatchEntity) {
        Long id = deliveryBatchEntity.getId();
        //获取点位信息
        List<DeliverySiteEntity> deliverySiteEntityList = deliverySiteRepository.queryList(DeliverySiteQuery.builder().batchId(id).build());
        if(CollectionUtils.isEmpty(deliverySiteEntityList)){
            throw new TmsRuntimeException("点位批次不存在");
        }
        deliverySiteEntityList.forEach(deliverySite -> {
            deliverySite.setSiteEntity(siteRepository.query(deliverySite.getSiteEntity().getId()));
        });
        deliveryBatchEntity.setDeliverySiteList(deliverySiteEntityList);

        //删除排线路段信息
        List<DeliverySectionEntity> deliverySectionEntityList = deliverySectionRepository.queryList(DeliverySectionQuery.builder()
                .batchId(deliveryBatchEntity.getId())
                .types(Arrays.asList(DeliverySectionEnums.Type.complete_path.getValue(),
                        DeliverySectionEnums.Type.INTELLIGENCE_PATH.getValue()))
                .build());
        deliverySectionRepository.removeList(deliverySectionEntityList);
        //重新计算路段信息
        this.completePath(deliveryBatchEntity);
        //完成配送计算
        this.finishDelivery(deliveryBatchEntity.getId());
    }

    /**
     * 自动关联调度单处理
     * @param deliveryBatchEntity 批次信息
     */
    public void autoRelateBatch(DeliveryBatchEntity deliveryBatchEntity) {
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return;
        }
        //干线用车类型支持自动匹配
        if (!Objects.equals(deliveryBatchEntity.getType(), DeliveryBatchTypeEnum.trunk.getCode())){
            return;
        }
        log.info("调度单ID:{},发起自动关联", deliveryBatchEntity.getId());
        //查询可自动关联的调度单信息
        //存在用车类型为“干线用车”；履约日期相同；线路完全相同；状态为“待配送、配送中、配送完成”的调度单；系统自动建立绑定关系
        List<DeliveryBatchEntity> supportMatchDeliveryBatches = deliveryBatchRepository.queryWithSite(DeliveryBatchQuery.builder()
                .type(deliveryBatchEntity.getType())
                .deliveryTime(deliveryBatchEntity.getDeliveryTime())
                .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                .endSiteId(deliveryBatchEntity.getEndSiteId())
                .deliveryBatchStatusList(Arrays.asList(DeliveryBatchStatusEnum.TO_BE_PICKED.getCode(), DeliveryBatchStatusEnum.IN_DELIVERY.getCode(), DeliveryBatchStatusEnum.COMPLETE_DELIVERY.getCode())).build());
        if (CollectionUtils.isEmpty(supportMatchDeliveryBatches)){
            return;
        }
        //当前路线 前端传参保证路线顺序未传排序字段
        List<Long> currentRoute = deliveryBatchEntity.getDeliverySiteList().stream().map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentRoute)){
            return;
        }
        //找出线路完成相同的调度单
        List<Long> relateBatchIds = supportMatchDeliveryBatches.stream().filter(batch -> {
            if (CollectionUtils.isEmpty(batch.getDeliverySiteList())) {
                return false;
            }
            //目标可匹配路线
            List<Long> matchRoute = batch.getDeliverySiteList().stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence))
                    .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
            if (currentRoute.size() != matchRoute.size()) {
                return false;
            }
            return Objects.equals(currentRoute, matchRoute);
        }).map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relateBatchIds)){
            log.info("调度单ID:{},自动关联无可匹配调度单", deliveryBatchEntity.getId());
            return;
        }
        //自动关联移除自身
        relateBatchIds.remove(deliveryBatchEntity.getId());
        log.info("调度单ID:{},自动关联可匹配调度单:{}", deliveryBatchEntity.getId(), JSON.toJSONString(relateBatchIds));
        List<DeliveryBatchRelationEntity> autoRelateEntities = relateBatchIds.stream().map(relateBatchId -> new DeliveryBatchRelationEntity(deliveryBatchEntity.getId(), relateBatchId, Constants.SYSTEM)).collect(Collectors.toList());
        deliveryBatchRepository.saveBatchRelation(autoRelateEntities);
        //处理自动关联优先级高于手动关联的情况
        List<DeliveryBatchRelationEntity> newRelationEntities = deliveryBatchEntity.getDeliveryBatchRelationEntityList();
        if (CollectionUtils.isEmpty(newRelationEntities)){
            return;
        }
        Set<Long> autoRelateUkSet = autoRelateEntities.stream().map(DeliveryBatchRelationEntity::buildUk).collect(Collectors.toSet());
        Iterator<DeliveryBatchRelationEntity> iterator = newRelationEntities.iterator();
        while (iterator.hasNext()) {
            DeliveryBatchRelationEntity nextRelate = iterator.next();
            if (autoRelateUkSet.contains(nextRelate.buildUk())) {
                log.info("调度单ID:{},触发自动关联优先级高于手动关联", deliveryBatchEntity.getId());
                iterator.remove();
            }
        }
    }

    /**
     * 手动关联调度单处理
     *
     * @param deliveryBatchEntity 批次信息
     * @param deliveryBatchId 配送批次ID
     * @param operator            操作人
     */
    public void handRelateBatch(DeliveryBatchEntity deliveryBatchEntity, Long deliveryBatchId, String operator) {
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return;
        }
        //获取所有关联的调度单
        List<DeliveryBatchRelationEntity> allDeliveryBatchRelationEntities = deliveryBatchEntity.getDeliveryBatchRelationEntityList();
        //新增关联调度单设置操作人
        List<DeliveryBatchRelationEntity> newDeliveryBatchRelationEntities = allDeliveryBatchRelationEntities.stream().filter(e -> StrUtil.isBlank(e.getCreator())).collect(Collectors.toList());
        newDeliveryBatchRelationEntities.forEach(e -> e.setCreator(operator));
        log.info("调度单ID:{},发起手动关联", deliveryBatchEntity.getId());
        deliveryBatchRepository.saveOrUpdateBatchRelation(allDeliveryBatchRelationEntities, deliveryBatchId);
        log.info("调度单ID:{},手动关联调度单:{}", deliveryBatchEntity.getId(), JSON.toJSONString(newDeliveryBatchRelationEntities));
    }

    /**
     * 配送批次关联关系关闭
     * @param deliveryBatchId 配送批次ID
     * @return 有变动的关联批次ID集合
     */
    public void deliveryBatchRelationClose(Long deliveryBatchId) {
        if (deliveryBatchId == null){
            return;
        }
        List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities = deliveryBatchRepository.queryBatchRelationList(deliveryBatchId);
        if (CollectionUtils.isEmpty(deliveryBatchRelationEntities)){
            return;
        }
        List<Long> needDeleteRelationIds = deliveryBatchRelationEntities.stream().map(DeliveryBatchRelationEntity::getId).collect(Collectors.toList());
        deliveryBatchRepository.removeBatchRelation(needDeleteRelationIds);
    }

    /**
     * 查询装载率确认变更的批次ID集合
     * @param deliveryBatchEntity 配送批次实体
     * @return 装载率确认变更的批次ID集合
     */
    public List<Long> confirmBatchLoadRatio(DeliveryBatchEntity deliveryBatchEntity) {
        List<Long> allRelateBatchIds = new ArrayList<>();
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            return allRelateBatchIds;
        }
        allRelateBatchIds.add(deliveryBatchEntity.getId());

        List<Long> oldRelateBatchIds = this.queryBatchLoadRatioRelate(Collections.singletonList(deliveryBatchEntity.getId()));
        allRelateBatchIds.addAll(oldRelateBatchIds);

        List<DeliveryBatchRelationEntity> deliveryBatchRelationEntityList = deliveryBatchEntity.getDeliveryBatchRelationEntityList();
        if (CollectionUtils.isEmpty(deliveryBatchRelationEntityList)){
            return allRelateBatchIds;
        }
        List<Long> newRelateBatchIds = deliveryBatchRelationEntityList.stream()
                .filter(e -> StrUtil.isBlank(e.getCreator())).map(DeliveryBatchRelationEntity::getRelateBatchId)
                .collect(Collectors.toList());
        allRelateBatchIds.addAll(newRelateBatchIds);
        return allRelateBatchIds;
    }

//    public List<Long> confirmBatchLoadRatio(DeliveryBatchEntity deliveryBatchEntity) {
//        boolean batchLoadRatioChange = this.confirmBatchLoadRatioChange(deliveryBatchEntity);
//        if (!batchLoadRatioChange){
//            return Collections.emptyList();
//        }
//        return this.queryBatchLoadRatioRelate(Collections.singletonList(deliveryBatchEntity.getId()));
//    }

    /**
     * 精细化统计是否发生装载率变更
     * @param deliveryBatchEntity 配送批次实体
     * @return
     */
    public boolean confirmBatchLoadRatioChange(DeliveryBatchEntity deliveryBatchEntity) {
        DeliveryBatchEntity deliveryBatch = deliveryBatchRepository.query(deliveryBatchEntity.getId());
        if (deliveryBatch == null || deliveryBatch.getId() == null){
            throw new TmsRuntimeException("无效调度单");
        }
        //车辆是否发生变化
        if (!Objects.equals(deliveryBatchEntity.getCarId(), deliveryBatch.getCarId())){
            return true;
        }
        List<Long> newDeliveryOrderIds = deliveryBatchEntity.getDeliveryOrderEntityList().stream().map(DeliveryOrderEntity::getId).collect(Collectors.toList());
        //查询状态不是关闭的承运单信息
        DeliveryOrderQuery deliveryOrderQuery = DeliveryOrderQuery.builder()
                .batchId(deliveryBatchEntity.getId())
                .neState(DeliveryOrderStatusEnum.CLOSE.getCode()).build();
        List<DeliveryOrderEntity> existedDeliveryOrderEntities = deliveryOrderRepository.queryList(deliveryOrderQuery);
        List<Long> existedDeliveryOrderIds = existedDeliveryOrderEntities.stream().map(DeliveryOrderEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
        //承运明细是否发生变化
        if (!CollectionUtils.isEqualCollection(newDeliveryOrderIds, existedDeliveryOrderIds)){
            return true;
        }
        return false;
    }

    /**
     * 查询配送批次关联关系
     * @param batchIds 配送批次ID集合
     * @return 配送批次关联关系
     */
    public List<Long> queryBatchLoadRatioRelate(List<Long> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)){
            return Collections.emptyList();
        }
        List<Long> allRelateBatchIds = new ArrayList<>();
        for (Long batchId : batchIds) {
            allRelateBatchIds.add(batchId);
            List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities = deliveryBatchRepository.queryBatchRelationList(batchId);
            List<Long> relateBatchIds = deliveryBatchRelationEntities.stream().map(DeliveryBatchRelationEntity::getDeliveryBatchId).filter(Objects::nonNull).collect(Collectors.toList());
            allRelateBatchIds.addAll(relateBatchIds);
        }
        return allRelateBatchIds;
    }

    /**
     * 触发批次装载率计算
     * @param relateBatchIds 需重新计算装载率的批次ID集合
     */
    public void calcBatchLoadRatio(List<Long> relateBatchIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(relateBatchIds)) {
            return;
        }
        log.info("装载率变更涉及批次:{}", JSONObject.toJSONString(relateBatchIds));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                try {
                    eventBusService.calcBatchLoadRatio(CalcBatchLoadRatioMessage.builder().deliveryBatchIds(relateBatchIds).build());
                } catch (Throwable e) {
                    log.error("装载率计算异常:{}", JSONObject.toJSONString(relateBatchIds), e);
                }
            }
        });
    }

    /**
     * 触发批次装载率计算(承运单侧变更导致调度单承运明细变更)
     * @param matchBatchIds 匹配变更的批次ID集合
     */
    public void calcBatchLoadRatioByDistChange(List<Long> matchBatchIds) {
        if (CollectionUtils.isEmpty(matchBatchIds)) {
            return;
        }
        //查询对应关联批次ID
        List<Long> relateBatchIds = this.queryBatchLoadRatioRelate(matchBatchIds);
        this.calcBatchLoadRatio(relateBatchIds);
    }

    /**
     * 计算并且更新装载率数据
     * @param deliveryBatchId 配送批次ID
     */
    public void calculateAndUpdateLoadRatio(Long deliveryBatchId) {
        if (deliveryBatchId == null){
            return;
        }
        BatchLoadRatioVO batchLoadRatioVO = deliveryBatchRepository.queryBatchLoadData(deliveryBatchId);
        if (batchLoadRatioVO == null){
            return;
        }
        batchLoadRatioVO.calculate();
        log.info("装载率计算数据汇总:{},", JSON.toJSONString(batchLoadRatioVO));
        deliveryBatchRepository.updateBatchLoadRatioById(batchLoadRatioVO);
    }

    /**
     * 处理批次重量、体积、数量相关信息
     * @param deliveryBatchEntityList 批次信息
     */
    public void installTotalInfo(List<DeliveryBatchEntity> deliveryBatchEntityList) {
        if(CollectionUtils.isEmpty(deliveryBatchEntityList)){
            return;
        }

        List<Long> batchIdList = deliveryBatchEntityList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(batchIdList)){
            return;
        }
        Map<Long, DeliveryBatchEntity> batchIdMap = deliveryBatchEntityList.stream().collect(Collectors.toMap(DeliveryBatchEntity::getId, Function.identity()));

        //根据批次ID查询delivery_order
        List<DeliveryOrderEntity> deliveryOrderEntityList = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder().batchIds(batchIdList).build());
        if(CollectionUtils.isEmpty(deliveryOrderEntityList)){
            return;
        }
        //获取委托单ID和批次ID的映射关系
        Map<Long, List<DeliveryOrderEntity>> distIdDeliveryOrderListMap = deliveryOrderEntityList.stream().collect(Collectors.groupingBy(DeliveryOrderEntity::getDistOrderId));
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setDistIdList(deliveryOrderEntityList.stream().map(DeliveryOrderEntity::getDistOrderId).collect(Collectors.toList()));
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryListWithItem(distOrderQuery);
        distOrderEntities.forEach(dist->{
            List<DeliveryOrderEntity> deliveryOrders = distIdDeliveryOrderListMap.get(dist.getDistId());
            if(CollectionUtils.isEmpty(deliveryOrders)){
                return;
            }
            Set<Long> batchIdSet = deliveryOrders.stream().map(DeliveryOrderEntity::getDeliveryBatchId).collect(Collectors.toSet());
            for (Long batchId : batchIdSet) {
                DeliveryBatchEntity deliveryBatchEntity = batchIdMap.get(batchId);
                if(deliveryBatchEntity == null){
                    return;
                }
                List<DistOrderEntity> distOrderList = deliveryBatchEntity.getDistOrderEntities();
                if(CollectionUtils.isEmpty(distOrderList)){
                    distOrderList = new ArrayList<>();
                    deliveryBatchEntity.setDistOrderEntities(distOrderList);
                }
                distOrderList.add(dist);
            }
        });
        deliveryBatchEntityList.forEach(deliveryBatchEntity->{
            List<DistOrderEntity> bindDistOrdersWithBatch = deliveryBatchEntity.getDistOrderEntities();
            if(bindDistOrdersWithBatch == null){
                return;
            }
            List<DistStaticVO> distStaticVOS = bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(distStaticVOS)){
                return;
            }
            deliveryBatchEntity.setTotalQuantity(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).mapToInt(DistStaticVO::getTotalQuantity).sum());
            deliveryBatchEntity.setTotalVolume(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).map(DistStaticVO::getTotalVolume).reduce(BigDecimal.ZERO, BigDecimal::add));
            deliveryBatchEntity.setTotalWeight(bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).map(DistStaticVO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        });
    }

    /**
     * 查询城配仓是否完成排线
     * @param storeNo
     * @param expectBeginTime
     */
    public void storeNoCompletePathValidate(Integer storeNo, LocalDateTime expectBeginTime) {
        if(storeNo == null || expectBeginTime == null){
            throw new TmsRuntimeException("城配仓编号和期望配送日期不能为空");
        }
        SiteEntity beginSiteEntity = siteRepository.query(SiteQuery.builder()
                .outBusinessNo(storeNo.toString())
                .type(TmsSiteTypeEnum.STORE.getCode())
                .build());
        if (beginSiteEntity == null){
            throw new TmsRuntimeException("起始点位不存在，创建委托单失败");
        }
        //查询是否完成排线操作中或已经完成排线
        if (deliveryBatchValidator.validateCompletePathIng(beginSiteEntity.getId(), expectBeginTime) ||
                deliveryBatchValidator.validateCompletePath(beginSiteEntity.getId(), expectBeginTime)){
            throw new TmsRuntimeException(ErrorCodeEnum.COMPLETE_PATH_DIST_OPERATE_FAIL);
        }
    }

    /**
     * 批量 城配批次检查是否需要完结状态
     * @param batchEntities 批次集合
     */
    public void batchCityFinishDelivery(List<DeliveryBatchEntity> batchEntities) {
        if(CollectionUtils.isEmpty(batchEntities)){
            return;
        }
        log.info("deliveryBatchDomainSerice batchCityFinishDelivery:{}", JSON.toJSONString(batchEntities));
        List<Long> batchIds = batchEntities.stream().map(DeliveryBatchEntity::getId).distinct().collect(Collectors.toList());

        List<DeliverySiteEntity> deliverySiteEntities = deliverySiteRepository.queryWithSite(DeliverySiteQuery.builder().batchIdList(batchIds).build());

        // 可以完结路线批次
        List<DeliveryBatchEntity> couldFinishDeliveryBatchList = batchEntities.stream().filter(deliveryBatchEntity-> {
            List<DeliverySiteEntity> batchHaveDeliverySiteList = deliverySiteEntities.stream().filter(deliverySiteEntity -> deliverySiteEntity.getDeliveryBatchId().equals(deliveryBatchEntity.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(batchHaveDeliverySiteList)) {
                return false;
            }
            // 获取未完成配送的点位
            List<DeliverySiteEntity> notFinishDeliverySiteList = batchHaveDeliverySiteList.stream().filter(deliverySite ->
                    !Objects.equals(deliverySite.getStatus(), DeliverySiteStatusEnum.FINISH_DELIVERY) &&
                            deliverySite.getInterceptState() != DeliverySiteInterceptStateEnum.allIntecept.getCode()
            ).collect(Collectors.toList());
            // 排除城配仓点位，还有其他点位没有完成配送，无需完结路线批次
            return notFinishDeliverySiteList.size() <= 1;
        }).collect(Collectors.toList());

        // 批量完结批次状态
        deliveryBatchRepository.batchUpdate(couldFinishDeliveryBatchList.stream().map(DeliveryBatchEntity::cityFinishDelivery).collect(Collectors.toList()));

        // 计算批次状态为【完结】的路段距离
        List<Long> couldFinishDeliveryBatchIdList = couldFinishDeliveryBatchList.stream().map(DeliveryBatchEntity::getId).collect(Collectors.toList());
        Map<Long, List<DeliverySiteEntity>> batchId2CouldFinishDeliverySiteListMap = deliverySiteEntities.stream()
                .filter(deliverySite -> couldFinishDeliveryBatchIdList.contains(deliverySite.getDeliveryBatchId()))
                .collect(Collectors.groupingBy(DeliverySiteEntity::getDeliveryBatchId));

        batchId2CouldFinishDeliverySiteListMap.forEach((batchId, couldFinishDeliverySiteList) ->{
            SiteEntity beginSite = couldFinishDeliverySiteList.stream().map(DeliverySiteEntity::getSiteEntity)
                    .filter(Objects::nonNull)
                    .filter(siteEntity -> siteEntity.getType().equals(TmsSiteTypeEnum.STORE.getCode())).findFirst()
                    .orElseGet(null);
            if(beginSite == null){
                log.error("城市仓批次：{}，没有找到起始点位，无法计算距离", batchId);
                return;
            }

            // 获取路线对应的配送点位(排除城配仓站点) 正常配送数量
            List<DeliverySiteEntity> normalSendDeliverySiteList = couldFinishDeliverySiteList.stream()
                    .filter(deliverySite -> deliverySite.getSendWay().equals(DeliverySiteEnums.SendWay.NORMAL_SEND.getValue()))
                    .filter(deliverySite -> !Objects.equals(deliverySite.getSiteId(),beginSite.getId()))
                    .collect(Collectors.toList());

            // 专车完成配送站点
            List<DeliverySiteEntity> specialSendFinishDeliverySiteList = couldFinishDeliverySiteList.stream()
                    .filter(deliverySite -> deliverySite.getSendWay().equals(DeliverySiteEnums.SendWay.SPECIAL_SEND.getValue()))
                    .filter(deliverySite -> deliverySite.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY))
                    .collect(Collectors.toList());

            // 普通配送完成配送的站点
            List<DeliverySiteEntity> normalSendFinishDeliverySiteList = normalSendDeliverySiteList.stream()
                    .filter(deliverySite -> deliverySite.getStatus().equals(DeliverySiteStatusEnum.FINISH_DELIVERY))
                    .collect(Collectors.toList());

            List<WaypointsInput> waypointsInputList = new ArrayList<>();
            waypointsInputList.add(WaypointsInput.builder().siteId(beginSite.getId()).poi(beginSite.getPoi()).build());
            // 只有路线均为专车配送才需要算距离，其他的情况需要排除专车点位
            if(normalSendDeliverySiteList.size() == 0){
                specialSendFinishDeliverySiteList.sort(Comparator.comparing(DeliverySiteEntity::getSignInTime, Comparator.nullsLast(Comparator.naturalOrder())));;
                // 说明只有城配仓点位为普通配送，其他均为专车配送点位
                specialSendFinishDeliverySiteList.forEach(deliverySite -> {
                    if(deliverySite.getSiteEntity() == null){
                        return;
                    }
                    waypointsInputList.add(WaypointsInput.builder()
                            .siteId(deliverySite.getSiteId())
                            .poi(deliverySite.getSiteEntity()
                                    .getPoi()).build());
                });
            }else{
                normalSendFinishDeliverySiteList.sort(Comparator.comparing(DeliverySiteEntity::getSignInTime, Comparator.nullsLast(Comparator.naturalOrder())));
                // 均为普通配送点位
                normalSendFinishDeliverySiteList.forEach(deliverySite -> {
                    if(deliverySite.getSiteEntity() == null){
                        return;
                    }
                    waypointsInputList.add(WaypointsInput.builder()
                            .siteId(deliverySite.getSiteId())
                            .poi(deliverySite.getSiteEntity().getPoi())
                            .build());
                });
            }
            if(waypointsInputList.size() == 1){
                // 只有城配仓站点不需要处理
                return;
            }
            //异步处理
            CalcTmsPathDistanceMessage calcIntelligencePath = CalcTmsPathDistanceMessage.builder()
                    .batchId(batchId)
                    .type(DeliverySectionEnums.Type.FINISH_SEND).waypointsInputList(waypointsInputList).build();
            eventBusService.calcPathDistance(calcIntelligencePath);
        });
    }

    /**
     * 路线重新分配司机
     * @param changeToDriverId 司机ID
     * @param changeDriverBatchEntity 批次实体
     */
    public void routeReassignmentDriver(Long changeToDriverId, DeliveryBatchEntity changeDriverBatchEntity) {
        if(changeToDriverId == null || changeDriverBatchEntity == null){
            return;
        }
        DriverEntity driverEntity = driverRepository.queryWithCityCar(DriverQuery.builder().id(changeToDriverId).build());
        if (driverEntity == null) {
            throw new TmsRuntimeException("司机不存在");
        }

        if (driverEntity.getCityCarId() == null) {
            throw new TmsRuntimeException("司机不存在车辆信息");
        }

        // 更新司机、车辆信息
        DeliveryBatchEntity batchEntity = new DeliveryBatchEntity();
        batchEntity.setId(changeDriverBatchEntity.getId());
        batchEntity.setDriverId(changeToDriverId);
        batchEntity.setCarId(driverEntity.getCityCarId());
        deliveryBatchRepository.update(batchEntity);

        // 装载率计算
        this.calculateAndUpdateLoadRatio(changeDriverBatchEntity.getId());
    }

    /**
     * 为委托单生成预排路线批次
     * @param distOrderEntity 承运单
     * @return 结果
     */
    public DeliveryBatchEntity prePathBatchForDistOrder(DistOrderEntity distOrderEntity) {
        if(distOrderEntity == null){
            return null;
        }
        SiteEntity beginSite = distOrderEntity.getBeginSite();
        SiteEntity endSite = distOrderEntity.getEndSite();
        LocalDateTime expectBeginTime = distOrderEntity.getDistFlowVO().getExpectBeginTime();
        //查询起始点位、终止点位是否有已排路线
        TmsPathEntity prePath = pathRepository.getPrePath(beginSite.getId(), endSite.getId(), TmsPathTypeEnum.DELIVERY_ROAD);
        DeliveryBatchEntity deliveryBatchEntity = new DeliveryBatchEntity();
        if (prePath == null) {
            deliveryBatchEntity.setPathId(Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID);
        } else {
            //查询是否已经完成排线
            List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder().beginSiteId(beginSite.getId()).deliveryTime(expectBeginTime)
                    .type(DeliveryBatchTypeEnum.city.getCode()).build());
            boolean isFinishWired = deliveryBatchEntityList.stream().anyMatch(e -> DeliveryBatchStatusEnum.TO_BE_PICKED.equals(e.getStatus()));
            if (isFinishWired) {
                deliveryBatchEntity.setPathId(Constants.Delivery_Batch.DEF_DELIVERY_PATH_ID);
            } else {
                deliveryBatchEntity.setPathCode(prePath.getPathCode());
                deliveryBatchEntity.setPathId(prePath.getPathId());
                deliveryBatchEntity.setPathName(prePath.getPathName());
                deliveryBatchEntity.setCarrierId(prePath.getCarrierId());
                deliveryBatchEntity.setCarId(prePath.getCarId());
                deliveryBatchEntity.setDriverId(prePath.getDriverId());
            }

        }
        deliveryBatchEntity.setType(DeliveryBatchTypeEnum.city.getCode());
        deliveryBatchEntity.setDeliveryTime(expectBeginTime);
        deliveryBatchEntity.setStatus(DeliveryBatchStatusEnum.TO_BE_WIRED);
        List<DeliverySiteEntity> deliverySiteList = new ArrayList<>(2);
        DeliverySiteEntity beginDeliverySite = new DeliverySiteEntity();
        beginDeliverySite.setSiteId(beginSite.getId());
        beginDeliverySite.setPlanArriveTime(expectBeginTime);
        deliverySiteList.add(beginDeliverySite);
        DeliverySiteEntity endDeliverySite = new DeliverySiteEntity();
        endDeliverySite.setSiteId(endSite.getId());
        endDeliverySite.setPlanArriveTime(expectBeginTime);
        endDeliverySite.setDistance(BigDecimal.valueOf(DistanceUtil.getPoiDistance(beginSite.getPoi(), endSite.getPoi())));
        endDeliverySite.setOuterClientId(distOrderEntity.getDistClientVO().getOutClientId());
        endDeliverySite.setOuterClientName(distOrderEntity.getDistClientVO().getOutClientName());
        endDeliverySite.setOuterBrandId(distOrderEntity.getDistClientVO().getOutTenantId());
        endDeliverySite.setOuterBrandName(distOrderEntity.getDistClientVO().getOutBrandName());
        deliverySiteList.add(endDeliverySite);
        deliveryBatchEntity.setDeliverySiteList(deliverySiteList);
        deliveryBatchEntity.setDeliveryOrderEntityList(distOrderEntity.getDeliveryOrders());
        // 车辆为空的时候，给予车辆默认值，防止数据库的唯一键不生效
        if (Objects.isNull(deliveryBatchEntity.getCarId())) {
            deliveryBatchEntity.setCarId(Constants.Delivery_Batch.DEF_CAR_ID);
        }
        return deliveryBatchEntity;
    }
}
