package net.summerfarm.tms.delivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.delivery.entity.BoardEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchRelationEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.vo.BatchLoadRatioVO;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.summerfarm.tms.query.board.BoardQuery;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import net.xianmu.gaode.support.enums.XMDriverRoutPlanStrategyEnum;
import net.xianmu.gaode.support.service.input.WaypointsInput;
import net.xianmu.gaode.support.service.vo.PathSection;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public interface DeliveryBatchRepository {

    /**
     * 调度单保存
     *
     * @param deliveryBatchEntity
     */
    void save(DeliveryBatchEntity deliveryBatchEntity);


    /**
     * 路由保存调度单
     *
     * @param deliveryBatchEntity
     */
    void saveByPath(DeliveryBatchEntity deliveryBatchEntity);

    /**
     * 根据批次id获取批次详情
     *
     * @param deliveryBatchId
     * @return
     */
    DeliveryBatchEntity query(Long deliveryBatchId);

    /**
     * 根据批次id获取批次
     *
     * @param deliveryBatchId
     * @return
     */
    DeliveryBatchEntity getBatchDetail(Long deliveryBatchId);

    /**
     * 调度单更新
     *
     * @param deliveryBatchEntity
     */
    void update(DeliveryBatchEntity deliveryBatchEntity);

    /**
     * 分页查询调度单信息
     *
     * @param deliveryBatchQuery
     * @return
     */
    PageInfo<DeliveryBatchEntity> queryPage(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 根据委托单id查询调度单信息
     *
     * @param distId 委托单id
     * @return 批次
     */
    List<DeliveryBatchEntity> getDeliveryBatchByDistId(Long distId);

    /**
     * 根据开始点位id和配送日期获取批次信息
     * @param deliveryBatchQuery
     * @return
     */
    List<DeliveryBatchEntity> queryListWithDriver(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 根据查询条件获取唯一
     * @return
     */
    DeliveryBatchEntity query(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 根据查询
     * @return
     */
    List<DeliveryBatchEntity> queryList(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 保存批次
     * @param deliveryBatchEntity 配送批次实体
     */
    void saveBatch(DeliveryBatchEntity deliveryBatchEntity);

    /**
     * 删除配送批次
     *
     * @param deliveryBatchId 配送批次ID集合
     * @param beginSiteId 起始点位ID
     * @param endSiteId 终止点位ID
     */
    void remove(Long deliveryBatchId, Long beginSiteId, Long endSiteId);

    /**
     * 异步计算距离（调用高德接口）
     *  @param waypointsInputList 点位信息
     * @param batchId            批次id
     * @param complete_path      类型
     * @param xMDriverRoutPlanStrategyEnum
     */
    void calculateDistance(List<WaypointsInput> waypointsInputList, Long batchId, DeliverySectionEnums.Type complete_path, XMDriverRoutPlanStrategyEnum xMDriverRoutPlanStrategyEnum);

    /**
     * 更新预计配送距离
     * @param deliveryBatchEntity 条件
     */
    void updatePlanDistance(DeliveryBatchEntity deliveryBatchEntity);

    /**
     * 查询相关接口
     * @param deliveryBatchId
     * @return
     */
    DeliveryBatchEntity queryWithDriver(Long deliveryBatchId);

    /**
     * 查询批次和配送点位
     * @param deliveryBatchId 配送批次ID
     * @return 结果
     */
    DeliveryBatchEntity queryWithSite(Long deliveryBatchId);

    /**
     * 查询批次和配送点位
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    List<DeliveryBatchEntity> queryWithSiteWithBase(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询批次和配送点位
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    List<DeliveryBatchEntity> queryWithSite(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 查询数据看板信息
     * @param boardQuery 查询
     * @return 返回数据看板信息
     */
    List<BoardEntity> queryBoardData(BoardQuery boardQuery);

    /**
     * 查询批次信息 包含司机信息
     * 批量查询
     * @param deliveryBatchQuery 查询
     * @return 结果
     */
    List<DeliveryBatchEntity> queryListWithBatchDriver(DeliveryBatchQuery deliveryBatchQuery);

    /**
     * 根据ID强制查询主库
     * @param batchId 批次ID
     * @return 批次信息
     */
    DeliveryBatchEntity queryByIdForceMaster(Long batchId);

    /**
     * 查询批次关联关系
     * @param batchId 批次ID
     * @return 关联批次ID集合
     */
    List<DeliveryBatchRelationEntity> queryBatchRelationList(Long batchId);

    /**
     * 查询批次关联关系
     * @param batchId 批次ID
     * @param isForceMaster 是否强制查询主库
     * @return 关联批次ID集合
     */
    List<DeliveryBatchRelationEntity> queryBatchRelationList(Long batchId, boolean isForceMaster);

    /**
     * 查询批次关联关系
     * @param batchId 批次ID
     * @return 关联批次ID集合
     */
    List<DeliveryBatchRelationEntity> queryBatchRelationListWithDetail(Long batchId);

    /**
     * 保存配送批次关联关系
     * @param deliveryBatchRelationEntities 配送批次关联关系
     * @return 影响行数
     */
    int saveBatchRelation(List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities);

    /**
     * 保存或更新配送批次关联关系
     *
     * @param deliveryBatchRelationEntities 配送批次关联关系
     * @param deliveryBatchId 配送批次ID
     * @return 影响行数
     */
    int saveOrUpdateBatchRelation(List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities, Long deliveryBatchId);

    /**
     * 删除批次关联关系
     * @param relationIds 关联表主键集合
     * @return 影响行数
     */
    int removeBatchRelation(List<Long> relationIds);

    /**
     * 计更新批次装载率
     * @param batchLoadRatioVO 批次装载数据
     */
    void updateBatchLoadRatioById(BatchLoadRatioVO batchLoadRatioVO);

    /**
     * 查询批次装载数据
     * @param batchId 批次ID
     * @return 批次装载数据
     */
    BatchLoadRatioVO queryBatchLoadData(Long batchId);

    /**
     * 根据唯一索引 路径ID、配送时间、起始点位ID、车辆ID查询
     * @param pathId 路线ID
     * @param deliveryTime 配送日期
     * @param beginSiteId 开始点位
     * @param carId 车辆
     * @return 结果
     */
    DeliveryBatchEntity queryByUk(Long pathId, LocalDateTime deliveryTime, Long beginSiteId, Long carId);

    /**
     * 批量更新
     * @param batchEntities 集合
     */
    void batchUpdate(List<DeliveryBatchEntity> batchEntities);

    /**
     * 高德计算路径
     * @param deliverySiteEntities 点位集合
     * @param batchId 批次ID
     * @return 结果
     */
    PathSection queryPathGaoDeDistance(List<DeliverySiteEntity> deliverySiteEntities, Long batchId);
}
