package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliveryItemEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/11/7 14:49<br/>
 *
 * <AUTHOR> />
 */
public interface DeliveryItemRepository {
    /**
     * 更新
     * @param deliveryItemEntity
     */
    void update(DeliveryItemEntity deliveryItemEntity);

    void save(DeliveryItemEntity deliveryItemEntity);

    /**
     * 批量保存
     * @param saveDeliveryItemList 批量集合
     */
    void saveBatch(ArrayList<DeliveryItemEntity> saveDeliveryItemList);

    /**
     * 批量更新
     * @param deliveryItemEntities
     */
    void batchUpdate(List<DeliveryItemEntity> deliveryItemEntities);
}
