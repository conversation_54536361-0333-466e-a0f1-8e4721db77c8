package net.summerfarm.tms.delivery;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.delivery.entity.CitySitePerformanceVO;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.query.delivery.DeliverySiteQuery;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


/**
 * Description: <br/>
 * date: 2022/9/13 18:14<br/>
 *
 * <AUTHOR> />
 */
public interface DeliverySiteRepository {

    /**
     * 根据批次id获取路线信息
     *
     * @param deliverySiteQuery
     * @return
     */
    List<DeliverySiteEntity> queryList(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据批次id获取路线信息 包含点位信息
     *
     * @param deliverySiteQuery
     * @return
     */
    List<DeliverySiteEntity> queryListWithSite(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据批次id获取路线信息
     *
     * @param deliverySiteId
     * @return
     */
    DeliverySiteEntity query(Long deliverySiteId);

    /**
     * 根据批次id获取路线信息 包含点位信息
     *
     * @param deliverySiteId
     * @return
     */
    DeliverySiteEntity queryWithSite(Long deliverySiteId);

    /**
     * 根据批次id获取路线信息 包含配送信息
     *
     * @param deliverySiteId
     * @return
     */
    DeliverySiteEntity queryWithItems(Long deliverySiteId);

    /**
     * 根据条件查询 包含配送信息
     *
     * @param deliverySiteQuery
     * @return
     */
    DeliverySiteEntity queryWithItems(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据条件查询 包含配送信息
     *
     * @param deliverySiteQuery
     * @return
     */
    DeliverySiteEntity queryWithItemsWithRecycle(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据批次id获取路线信息 包含签发拣货信息
     *
     * @param deliverySiteId
     * @return
     */
    DeliverySiteEntity queryWithPicks(Long deliverySiteId);

    /**
     * 路线点位的保存
     *
     * @param deliverySiteList
     */
    void save(List<DeliverySiteEntity> deliverySiteList, Long deliveryBatchId);

    /**
     * 路线点位的保存
     *
     * @param deliverySiteEntity
     */
    void update(DeliverySiteEntity deliverySiteEntity);

    /**
     * 根据配送单id查询配送信息
     *
     * @param id 配送单id
     * @return 配送信息
     */
    DeliverySiteEntity queryById(Long id);

    /**
     * 查询当前批次下面的点位信息
     *
     * @param deliverySiteQuery 点位信息
     * @return 点位数量
     */
    long queryCount(DeliverySiteQuery deliverySiteQuery);

    /**
     * 顺序加
     *
     * @param deliveryBatchId 批次
     * @param sourceSeq       原序号
     * @param newSequence     新序号
     */
    void updateSortAdd(Long deliveryBatchId, Integer sourceSeq, Integer newSequence);

    /**
     * 顺序减
     *  @param deliveryBatchId 批次
     * @param sourceSeq 原序号
     * @param newSequence        新序号
     */
    void updateSortSub(Long deliveryBatchId, Integer sourceSeq, Integer newSequence);

    /**
     * 根据批次id批量删除配送点位
     *
     * @param batchId         批次id
     * @param deliverySideIds
     */
    void batchRemoveByBatchId(Long batchId, List<Long> deliverySideIds);

    /**
     * 批量配送路线的保存
     *
     * @param deliverySiteList 配送路线点集合
     */
    void batchSave(List<DeliverySiteEntity> deliverySiteList);

    /**
     * 根据条件查询
     *
     * @param deliverySiteQuery
     * @return
     */
    DeliverySiteEntity query(DeliverySiteQuery deliverySiteQuery);

    /**
     * 删除配送点位
     *
     * @param deliveryBatchId 配送批次ID集合
     * @param distId          委托单ID
     * @param beginSiteId       起始点位ID
     * @param endSiteId       终止点位ID
     * @param expectBeginTime 期待开始时间
     */
    int remove(Long deliveryBatchId, Long distId, Long beginSiteId, Long endSiteId, LocalDateTime expectBeginTime);

    /**
     * 删除配送点位
     *
     * @param deliverySiteId 配送批次ID集合
     */
    void removeById(Long deliverySiteId);

    /**
     * 查询配送点位
     *
     * @param query 条件
     * @return 结果
     */
    List<DeliverySiteEntity> queryWithSite(DeliverySiteQuery query);

    /**
     * 根据ID查询信息
     * @param siteId 点位ID
     * @return 结果
     */
    DeliverySiteEntity queryWithDistOrders(Long siteId);

    /**
     * 查询待发送钉钉消息提示的配送点位集合
     *
     * <pr>调度单类型为干线用车、调拨用车</pr>
     * <pr>调度单状态为待配送、配送中</pr>
     * <pr>配送点位的消息发送记录标识为空，当前时间在计划签收时间之前</pr>
     *
     * @param batchIds 调度单id集合
     * @param arriveMsg 是否是入仓到达消息提示
     * @return
     */
    List<DeliverySiteEntity> queryWaitSendMsgList(Set<Long> batchIds, boolean arriveMsg);

    /**
     * 更新点位的消息发送标识
     *
     * @param siteIds   点位id集合
     * @param arriveMsg 是否是入仓消息
     */
    void updateSendMsgFlag(Set<Long> siteIds, boolean arriveMsg);

    /**
     * 查询配送点位 包含点位信息(批量查询)
     *
     * @param deliverySiteQuery 条件
     * @return 结果
     */
    List<DeliverySiteEntity> queryListWithBatchSite(DeliverySiteQuery deliverySiteQuery);

    /**
     * 分页查询
     * @param deliverySiteQuery 查询
     * @return 结果
     */
    PageInfo<CitySitePerformanceVO> queryCitySitePerformancePage(DeliverySiteQuery deliverySiteQuery);

    /**
     * 根据批次ID和点位查询配送点位
     * @param deliveryBatchId 批次
     * @param siteId 点位ID
     * @return 配送点位
     */
    DeliverySiteEntity queryForceMasterByBatchIdAndSiteId(Long deliveryBatchId, Long siteId);

    /**
     *  根据批次ID批量查询配送单项
     * @param deliveryBatchIds 批次ID集合
     * @return 点位
     */
    List<DeliverySiteEntity> queryListWithItemsByBatchIds(List<Long> deliveryBatchIds);

    /**
     * 批量更新
     * @param deliverySiteEntities 点位信息
     */
    void batchUpdate(List<DeliverySiteEntity> deliverySiteEntities);

    /**
     * 相同起点、终点、配送时间多个配送点位补偿处理
     * @param deliveryTime 配送时间
     * @return 结果
     */
    Boolean sameDeliverySiteHandle(LocalDateTime deliveryTime);

    /**
     * 更新配送点位，并保存回收信息
     * @param deliverySiteEntity 点位信息
     */
    void updateAndSaveRecycle(DeliverySiteEntity deliverySiteEntity);

    /**
     * 查询最近完成配送的点位信息
     * @param siteId 点位ID
     * @return 近完成配送的点位信息
     */
    DeliverySiteEntity queryRecentDeliveryFinishSite(Long siteId);

    /**
     * 存在配送点位无对应配送单补偿处理
     * @param deliveryTime 配送时间
     * @return 结果
     */
    Boolean deliverySiteWithNoOrderHandle(LocalDateTime deliveryTime);

    /**
     * 查询配送点位标准品温度条件
     * @param deliverySiteIds 配送站点ID集合
     * @return 结果
     */
    List<DeliverySiteStandardTempCondition> queryStandardItemTemperatureConditionsByIds(List<Long> deliverySiteIds);

    /**
     * 根据条件查询点位配送详情
     * @param query 查询
     * @return 结果
     */
    List<DeliverySiteEntity> queryListWithItems(DeliverySiteQuery query);

    /**
     * 批量更新点位配送顺序
     * @param needUpdateDeliverySitesSequenceList 点位信息
     */
    void batchUpdateAntSequences(List<DeliverySiteEntity> needUpdateDeliverySitesSequenceList);

    /**
     * 根据批次id查询强制主库,配送站点和点位信息
     * @param batchId 批次id
     * @return
     */
    List<DeliverySiteEntity> queryForceMasterWithSiteByBatchId(Long batchId);

    /**
     * 查询有专车配送的城配批次ID集合
     * @param deliveryTime 配送日期
     * @return 批次ID集合
     */
    List<Long> queryCityDeliveryHaveSpecialSendSiteBatchIds(LocalDateTime deliveryTime);

    /**
     * 查询最近日期配送门店POI列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param city 城市
     * @param area 区域
     * @return POI列表
     */
    List<String> queryRecentlyDateDeliveryStorePoi(LocalDate startTime, LocalDate endTime, String city, String area);
}
