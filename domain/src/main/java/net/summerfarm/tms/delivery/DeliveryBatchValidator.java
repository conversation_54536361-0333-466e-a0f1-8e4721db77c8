package net.summerfarm.tms.delivery;

import lombok.RequiredArgsConstructor;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchFareEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchRelationEntity;
import net.summerfarm.tms.delivery.entity.DeliverySiteEntity;
import net.summerfarm.tms.enums.DeliveryBatchEnums;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.query.delivery.DeliveryBatchQuery;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:配送批次校验器
 * date: 2023/2/20 18:24
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DeliveryBatchValidator {

    private final RedissonClient redissonClient;
    private final DeliveryBatchRepository deliveryBatchRepository;

    public boolean validateCompletePathIng(Long beginSiteId, LocalDateTime deliveryTime){
        TmsAssert.notNull(beginSiteId, ErrorCodeEnum.PARAM_NOT_NULL, "BeginSiteId");
        TmsAssert.notNull(beginSiteId, ErrorCodeEnum.PARAM_NOT_NULL, "DeliveryTime");
        StringJoiner key = new StringJoiner("-");
        String deliveryTimeStr = deliveryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        key.add(RedisConstants.Delivery.TMS_COMPLETE_PATH).add(deliveryTimeStr).add(beginSiteId.toString());
        RLock redissonLock = redissonClient.getLock(key.toString());
        return redissonLock.isLocked();
    }

    public boolean validateCompletePath(Long beginSiteId, LocalDateTime deliveryTime){
        TmsAssert.notNull(beginSiteId, ErrorCodeEnum.PARAM_NOT_NULL, "BeginSiteId");
        TmsAssert.notNull(deliveryTime, ErrorCodeEnum.PARAM_NOT_NULL, "DeliveryTime");
        List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                .beginSiteId(beginSiteId)
                .deliveryTime(deliveryTime)
                .type(DeliveryBatchTypeEnum.city.getCode()).build());
        return deliveryBatchEntityList.stream().anyMatch(e -> e.getStatus().getCode() >= DeliveryBatchStatusEnum.TO_BE_PICKED.getCode());
    }

    public boolean validateCompletePathForDeliveryBatch(DeliveryBatchEntity deliveryBatchEntity){
        StringJoiner key = new StringJoiner("-");
        String deliveryTimeStr = deliveryBatchEntity.getDeliveryTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        key.add(RedisConstants.Delivery.TMS_COMPLETE_PATH).add(deliveryTimeStr).add(deliveryBatchEntity.getBeginSiteId().toString());
        RLock redissonLock = redissonClient.getLock(key.toString());
        if(redissonLock.isLocked()){
            return true;
        }else{
            List<DeliveryBatchEntity> deliveryBatchEntityList = deliveryBatchRepository.queryList(DeliveryBatchQuery.builder()
                    .beginSiteId(deliveryBatchEntity.getBeginSiteId())
                    .deliveryTime(deliveryBatchEntity.getDeliveryTime())
                    .type(DeliveryBatchTypeEnum.city.getCode()).build());
            return deliveryBatchEntityList.stream().anyMatch(e -> e.getStatus().getCode() >= DeliveryBatchStatusEnum.TO_BE_PICKED.getCode());
        }
    }

    public void validateBatchFare(List<DeliveryBatchFareEntity> deliveryBatchFareEntityList, BigDecimal estimateFare){
        if (CollectionUtils.isEmpty(deliveryBatchFareEntityList)){
            return;
        }
        if (estimateFare == null){
            estimateFare =  BigDecimal.ZERO;
        }

        Map<DeliveryBatchEnums.FareType, Long> fareTypeMap = deliveryBatchFareEntityList.stream().collect(Collectors.groupingBy(DeliveryBatchFareEntity::getFareType, Collectors.counting()));
        BigDecimal sumFare = BigDecimal.ZERO;
        for (DeliveryBatchFareEntity deliveryBatchFareEntity : deliveryBatchFareEntityList) {
            sumFare = sumFare.add(deliveryBatchFareEntity.getAmount());
            if (fareTypeMap.get(deliveryBatchFareEntity.getFareType()) > 1){
                throw new TmsRuntimeException(deliveryBatchFareEntity.getFareType().getContent() + "重复配置");
            }
        }
        if (sumFare.compareTo(estimateFare) > 0){
            throw new TmsRuntimeException("运费明细总计已超出预估总运费");
        }
    }

    public boolean validateBatchRouteRelate(DeliveryBatchEntity currentDeliveryBatch, DeliveryBatchEntity targetDeliveryBatch){
        boolean isValidatePass = this.validateBatchSubRoute(currentDeliveryBatch, targetDeliveryBatch);
        if (isValidatePass){
            return true;
        }
        return this.validateBatchSubRoute(targetDeliveryBatch, currentDeliveryBatch);
    }

    public boolean validateBatchSubRoute(DeliveryBatchEntity currentDeliveryBatch, DeliveryBatchEntity targetDeliveryBatch){
        if (currentDeliveryBatch == null || currentDeliveryBatch.getId() == null || CollectionUtils.isEmpty(currentDeliveryBatch.getDeliverySiteList())){
            return false;
        }
        if (targetDeliveryBatch == null || targetDeliveryBatch.getId() == null || CollectionUtils.isEmpty(targetDeliveryBatch.getDeliverySiteList())){
            return false;
        }
        //当前路线
        List<Long> currentRoute = currentDeliveryBatch.getDeliverySiteList().stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence))
                .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        //目标路线
        List<Long> targetRoute = targetDeliveryBatch.getDeliverySiteList().stream().sorted(Comparator.comparing(DeliverySiteEntity::getSequence))
                .map(DeliverySiteEntity::getSiteId).collect(Collectors.toList());
        Set<List<Long>> subRoutes = this.getAllSubRoutes(currentRoute);
        if (subRoutes.contains(targetRoute)){
            return true;
        }
        return false;
    }

    private Set<List<Long>> getAllSubRoutes(List<Long> currentRoute) {
        Set<List<Long>> subRoutes = new HashSet<>();

        // 添加自身路线
        subRoutes.add(currentRoute);

        // 递归获取子集
        for (int i = 0; i < currentRoute.size(); i++) {
            List<Long> subRoute = new ArrayList<>(currentRoute);
            subRoute.remove(i);
            if (subRoute.size() <= 1){
                continue;
            }
            subRoutes.addAll(getAllSubRoutes(subRoute));
        }
        return subRoutes;
    }

    public boolean validateBatchRelateExist(Long currentDeliveryBatchId, Long targetDeliveryBatchId){
        if (currentDeliveryBatchId == null || targetDeliveryBatchId == null){
            return false;
        }
        List<DeliveryBatchRelationEntity> deliveryBatchRelationEntities = deliveryBatchRepository.queryBatchRelationList(currentDeliveryBatchId);
        if (CollectionUtils.isEmpty(deliveryBatchRelationEntities)){
            return false;
        }
        Set<Long> relateBatchIds = deliveryBatchRelationEntities.stream().map(DeliveryBatchRelationEntity::getDeliveryBatchId).collect(Collectors.toSet());
        return relateBatchIds.contains(targetDeliveryBatchId);
    }

    public void validateBatchRelate(List<DeliveryBatchRelationEntity> newBatchRelations, Long deliveryBatchId) {
        if (CollectionUtils.isEmpty(newBatchRelations) || deliveryBatchId == null){
            return;
        }
        //新关系构建uk关系 防止已存在的关系多次提交
        Map<Long, Long> ukRelateCountMap = newBatchRelations.stream().collect(Collectors.groupingBy(DeliveryBatchRelationEntity::buildUk, Collectors.counting()));
        //构建uk关系
        for (DeliveryBatchRelationEntity newBatchRelation : newBatchRelations) {
            //新的关系需要进行校验是否已存在
            if (ukRelateCountMap.get(newBatchRelation.buildUk()) > 1){
                throw new TmsRuntimeException(String.format("关联调度单:%d已存在,请重新选择!", newBatchRelation.getRelateBatchId()));
            }
            this.validateBatchRelateBase(newBatchRelation.getBatchId(), newBatchRelation.getRelateBatchId());
        }

    }

    public void validateBatchRelateBase(Long currentDeliveryBatchId, Long targetDeliveryBatchId) {
        DeliveryBatchEntity currentDeliveryBatch = deliveryBatchRepository.queryWithSite(currentDeliveryBatchId);
        if (currentDeliveryBatch == null){
            throw new TmsRuntimeException("当前调度单不存在");
        }
        DeliveryBatchEntity targetDeliveryBatch = deliveryBatchRepository.queryWithSite(targetDeliveryBatchId);
        if (targetDeliveryBatch == null || !targetDeliveryBatch.isCompletePath()){
            throw new TmsRuntimeException("调度单号或调度状态有误,请重新选择!");
        }
        if (!Objects.equals(currentDeliveryBatch.getDeliveryTime(), targetDeliveryBatch.getDeliveryTime())){
            throw new TmsRuntimeException("调度单履约日期不同,请重新选择!");
        }
        if (!Objects.equals(currentDeliveryBatch.getType(), targetDeliveryBatch.getType())){
            throw new TmsRuntimeException("调度单用车类型不同,请重新选择!");
        }
//        boolean isSubPath = deliveryBatchValidator.validateBatchSubRoute(currentDeliveryBatch, targetDeliveryBatch);
//        if (!isSubPath){
//            throw new TmsRuntimeException("调度单线路不匹配,请重新选择!");
//        }
        if (Objects.equals(currentDeliveryBatch.getId(), targetDeliveryBatch.getId())){
            throw new TmsRuntimeException("调度单相同,请重新选择!");
        }
    }
}
