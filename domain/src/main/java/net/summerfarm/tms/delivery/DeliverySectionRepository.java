package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.DeliverySectionEntity;
import net.summerfarm.tms.query.delivery.DeliverySectionQuery;

import java.util.List;

/**
 * Description: <br/>
 * date: 2022/12/8 14:06<br/>
 *
 * <AUTHOR> />
 */
public interface DeliverySectionRepository {
    /**
     * 保存
     * @param deliverySectionEntity 路段
     */
    void save(DeliverySectionEntity deliverySectionEntity);

    /**
     * 批量保存
     * @param deliverySectionEntities 路段集合
     */
    void saveBatch(List<DeliverySectionEntity> deliverySectionEntities);

    /**
     * 查询路段信息
     * @param deliverySectionQuery 查询条件
     * @return 结果
     */
    List<DeliverySectionEntity> queryList(DeliverySectionQuery deliverySectionQuery);

    /**
     * 批量删除
     * @param deliverySectionEntityList 路段
     */
    void removeList(List<DeliverySectionEntity> deliverySectionEntityList);

    /**
     * 查询集合点位信息
     * @param deliverySectionQuery 查询
     * @return 结果
     */
    List<DeliverySectionEntity> queryListWithSite(DeliverySectionQuery deliverySectionQuery);

    /**
     * 根据批次和类型查询路段信息
     * @param deliveryBatchIdList 批次ID
     * @param type 类型
     * @return 结果
     */
    List<DeliverySectionEntity> queryListByBatchIdsAndType(List<Long> deliveryBatchIdList, Integer type);
}
