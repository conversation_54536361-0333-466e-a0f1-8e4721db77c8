package net.summerfarm.tms.delivery;

import net.summerfarm.tms.delivery.entity.BatchPickDetailEntity;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempUnit;
import net.summerfarm.tms.query.delivery.DeliveryPickQuery;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface DeliveryPickRepository {
    void update(DeliveryPickEntity deliveryPickEntity);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    DeliveryPickEntity query(Long id);

    /**
     * 查询
     *
     * @param deliveryPickQuery 查询条件
     * @return 拣货
     */
    List<DeliveryPickEntity> queryList(DeliveryPickQuery deliveryPickQuery);

    /**
     * 保存
     *
     * @param deliveryPickEntity
     */
    void save(DeliveryPickEntity deliveryPickEntity);

    /**
     * 批量保存
     * @param deliveryPickEntities 实体
     */
    void saveBatch(ArrayList<DeliveryPickEntity> deliveryPickEntities);

    /**
     * 删除操作
     * @param idList 主键id集合
     */
    void removeByIdList(List<Long> idList);

    /**
     * 通过批次id删除
     * @param batchId
     */
    void removeByBatchId(Long batchId);

    /**
     * 查询拣货详情
     * @param batchIds 批次ID集合
     * @return 批次拣货详情集合
     */
    List<BatchPickDetailEntity> queryByBatch(List<Long> batchIds);

    /**
     * 根据批次和sku查询拣货信息
     * @param deliveryBatchId 批次ID
     * @param sku sku
     * @return 结果
     */
    List<DeliveryPickEntity> queryByBatchIdSku(Long deliveryBatchId, String sku);

    /**
     * 根据拣货站点信息查询标品温度条件
     * @param deliverySiteIds 站点ID集合
     * @return 结果
     */
    List<DeliverySiteStandardTempCondition> queryPickStandardItemTemperatureConditionsByDelSiteIds(List<Long> deliverySiteIds);

    /**
     * 根据拣货站点信息查询标品温度单位
     * @param deliverySiteIds
     * @return
     */
    Map<Long, List<DeliverySiteStandardTempUnit>> queryPickStandardItemTemperatureUnitByDelSiteIds(List<Long> deliverySiteIds);

    /**
     * 扫码数量增加
     * @param deliveryPickEntity 拣货
     */
    void scanCodeAdd(DeliveryPickEntity deliveryPickEntity);

    /**
     * 根据批次ID查询拣货信息
     * @param deliveryBatchIdList 批次ID
     * @return 结果
     */
    List<DeliveryPickEntity> queryByBatchIdList(List<Long> deliveryBatchIdList);

    /**
     * 批量更新
     * @param deliveryPickEntityList
     */
    void batchUpdate(List<DeliveryPickEntity> deliveryPickEntityList);
}
