package net.summerfarm.tms.cost;

import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.cost.entity.DeliveryCostEntity;
import net.summerfarm.tms.delivery.DeliveryBatchRepository;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.dist.DistOrderDomainService;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.dist.vo.DistItemVO;
import net.summerfarm.tms.dist.vo.DistStaticVO;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description:配送成本 domain服务
 * date: 2023/2/10 16:46
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryCostDomainService {

    private final DistOrderDomainService distOrderDomainService;
    private final DistOrderRepository distOrderRepository;
    private final DeliveryBatchRepository deliveryBatchRepository;
    private final DeliveryCostRepository deliveryCostRepository;

    public void costCalculate(Long distOrderId, DeliveryCostEnums.ChangeType changeType) {
        //校验承运单有效性
        DistOrderEntity distOrderEntity = distOrderDomainService.getDistOrderDetail(distOrderId);
        List<DistOrderSourceEnum> costSources = Arrays.asList(DistOrderSourceEnum.ALLOCATION, DistOrderSourceEnum.PURCHASE);
        if (!costSources.contains(distOrderEntity.getSource())){
            log.info("承运单:【{}】,来源:【{}】,无需发起成本计算", distOrderId, distOrderEntity.getSource().getName());
            return;
        }
        //查询已绑定的调度单集合
        List<DeliveryBatchEntity> bindDeliveryBatches = deliveryBatchRepository.getDeliveryBatchByDistId(distOrderEntity.getDistId());
        //过滤已绑定的有效调度单集合
        List<DeliveryBatchEntity> bindValidDeliveryBatches = bindDeliveryBatches.stream().filter(e -> !DeliveryBatchStatusEnum.DELIVERY_CLOSED.equals(e.getStatus())).collect(Collectors.toList());
        log.info("承运单:【{}】,已绑定的调度单:【{}】,条数:【{}】,已绑定的有效调度单:【{}】,条数:【{}】", distOrderId, JSON.toJSONString(bindDeliveryBatches), bindDeliveryBatches.size(), JSON.toJSONString(bindValidDeliveryBatches), bindValidDeliveryBatches.size());
        //委托单总重量
        BigDecimal totalWeightWithDistOrder = distOrderEntity.getDistStaticVO().getTotalWeight();
        //委托单总成本
        BigDecimal totalCostWithDistOrder = calculateDistOrderTotalCost(totalWeightWithDistOrder, bindValidDeliveryBatches);
        //获取成本数据实体
        List<DeliveryCostEntity> newDeliveryCostEntities = getDeliveryCostEntities(distOrderEntity, totalWeightWithDistOrder, totalCostWithDistOrder);
        //成本数据持久化
        deliveryCostRepository.saveOrUpdate(newDeliveryCostEntities, changeType);
    }

    private List<DeliveryCostEntity> getDeliveryCostEntities(DistOrderEntity distOrderEntity, BigDecimal totalWeightWithDistOrder, BigDecimal totalCostWithDistOrder) {
        if (BigDecimal.ZERO.compareTo(totalWeightWithDistOrder) == 0){
            totalWeightWithDistOrder = BigDecimal.ONE;
        }
        List<DeliveryCostEntity> newDeliveryCostEntities = new ArrayList<>(distOrderEntity.getDistItems().size());
        for (DistItemVO distItem : distOrderEntity.getDistItems()) {
            DeliveryCostEntity deliveryCostEntity = new DeliveryCostEntity();
            BigDecimal costWithOneSku = distItem.getWeight().multiply(totalCostWithDistOrder).divide(totalWeightWithDistOrder, 2, RoundingMode.UP);
            deliveryCostEntity.setDistOrderId(distOrderEntity.getDistId());
            deliveryCostEntity.setOutItemId(distItem.getOutItemId());
            deliveryCostEntity.setSource(distOrderEntity.getSource());
            deliveryCostEntity.setQuantity(distItem.getQuantity());
            deliveryCostEntity.setAverageCost(costWithOneSku);
            newDeliveryCostEntities.add(deliveryCostEntity);
        }
        return newDeliveryCostEntities;
    }

    private BigDecimal calculateDistOrderTotalCost(BigDecimal totalWeightWithDistOrder, List<DeliveryBatchEntity> bindValidDeliveryBatches) {
        if (totalWeightWithDistOrder == null){
            totalWeightWithDistOrder = BigDecimal.ZERO;
        }
        BigDecimal totalCostWithDistOrder = BigDecimal.ZERO;
        for (DeliveryBatchEntity bindValidDeliveryBatch : bindValidDeliveryBatches) {
            //查询当前配送批次绑定的承运单集合
            List<DistOrderEntity> bindDistOrdersWithBatch = distOrderRepository.queryDistOrdersWithItemByBatchId(bindValidDeliveryBatch.getId());
            //当前批次总重量
            BigDecimal totalWeightWithBatch = bindDistOrdersWithBatch.stream().map(DistOrderEntity::getDistStaticVO).map(DistStaticVO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(totalWeightWithBatch) == 0){
                totalWeightWithBatch = BigDecimal.ONE;
            }
            //当前批次总成本
            BigDecimal totalCostWithBatch = totalWeightWithDistOrder.multiply(bindValidDeliveryBatch.getEstimateFare()).divide(totalWeightWithBatch, 4, RoundingMode.UP);
            totalCostWithDistOrder = totalCostWithDistOrder.add(totalCostWithBatch);
        }
        return totalCostWithDistOrder;
    }

    public void costChange(Long deliveryBatchId, DeliveryCostEnums.ChangeType changeType) {
        //校验配送批次有效性
        DeliveryBatchEntity deliveryBatchEntity = deliveryBatchRepository.query(deliveryBatchId);
        if (deliveryBatchEntity == null || deliveryBatchEntity.getId() == null){
            log.info("调度单不存在:{},无法发起成本变更", deliveryBatchId);
            return;
        }
        //查询当前配送批次绑定的承运单集合 绑定关系存在承运单一定有效
        List<DistOrderEntity> bindDistOrdersWithBatch = distOrderRepository.queryDistOrdersByBatchId(deliveryBatchEntity.getId());
        log.info("调度单:{},已绑定的承运单集合:{},条数:{}", deliveryBatchId, JSON.toJSONString(bindDistOrdersWithBatch), bindDistOrdersWithBatch.size());
        for (DistOrderEntity distOrder : bindDistOrdersWithBatch) {
            costCalculate(distOrder.getDistId(), changeType);
        }
    }
}
