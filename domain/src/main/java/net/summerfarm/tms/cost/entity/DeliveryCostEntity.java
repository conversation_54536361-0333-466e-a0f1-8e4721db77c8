package net.summerfarm.tms.cost.entity;

import lombok.Data;
import net.summerfarm.tms.enums.DistOrderSourceEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description:配送成本实体
 * date: 2023/2/10 16:48
 *
 * <AUTHOR>
 */
@Data
public class DeliveryCostEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 外部成本批次ID
     */
    private String outBatchId;

    /**
     * 委托单ID
     */
    private Long distOrderId;

    /**
     * 外部货品ID(sku)
     */
    private String outItemId;

    /**
     * 来源，100：调拨，101：采购，102：销售出库，103，出样出库，104，补货出库，105，自提销售
     */
    private DistOrderSourceEnum source;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 平均成本
     */
    private BigDecimal averageCost;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
