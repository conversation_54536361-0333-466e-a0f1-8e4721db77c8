package net.summerfarm.tms.cost.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Description:配送成本关系实体
 * date: 2023/2/14 19:07
 *
 * <AUTHOR>
 */
@Data
public class DeliveryCostRelationEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 委托单ID
     */
    private Long distOrderId;

    /**
     * 外部货品ID(sku)
     */
    private String outItemId;

    /**
     * 外部成本批次ID
     */
    private String outBatchId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
