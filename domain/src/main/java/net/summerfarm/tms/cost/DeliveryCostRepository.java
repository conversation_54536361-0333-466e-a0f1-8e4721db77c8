package net.summerfarm.tms.cost;

import net.summerfarm.tms.cost.entity.DeliveryCostEntity;
import net.summerfarm.tms.cost.entity.DeliveryCostRelationEntity;
import net.summerfarm.tms.enums.DeliveryCostEnums;
import net.summerfarm.tms.query.delivery.DeliveryCostQuery;

import java.util.Collection;
import java.util.List;

/**
 * Description:配送成本仓库接口
 * date: 2023/2/10 18:23
 *
 * <AUTHOR>
 */
public interface DeliveryCostRepository {

    /**
     * 查询集合
     * @param deliveryCostQuery 查询
     * @return 结果
     */
    List<DeliveryCostEntity> queryList(DeliveryCostQuery deliveryCostQuery);

    /**
     * 保存成本批次
     * 标准 有则覆盖 没有则新增
     *
     * @param newDeliveryCostEntities 最新配送成本集合
     * @param changeType 成本变更类型
     */
    void saveOrUpdate(List<DeliveryCostEntity> newDeliveryCostEntities, DeliveryCostEnums.ChangeType changeType);

    /**
     * 查询成本 关联sku成本关系表
     * @param deliveryCostQuery 查询
     * @return 结果
     */
    List<DeliveryCostEntity> queryCostList(DeliveryCostQuery deliveryCostQuery);

    /**
     * 保存成本关系
     * @param costRelations 成本关系集合
     */
    void saveCostRelation(List<DeliveryCostRelationEntity> costRelations);

    /**
     * 查询成本关联关系
     * @param outBatchIds 外部批次ID集合
     * @return 结果
     */
    List<DeliveryCostRelationEntity> queryRelationsByOutBatchIds(Collection<String> outBatchIds);
}
