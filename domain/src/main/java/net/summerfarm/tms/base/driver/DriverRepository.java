package net.summerfarm.tms.base.driver;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.query.base.driver.DriverQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Description: <br/>
 * date: 2022/7/13 15:09<br/>
 *
 * <AUTHOR> />
 */

public interface DriverRepository {

    /**
     * 司机的保存
     *
     * @param driverEntity
     * @return
     */
    Long save(DriverEntity driverEntity);

    /**
     * 司机的编辑
     *
     * @param driverEntity
     */
    void edit(DriverEntity driverEntity);

    /**
     * 通过手机号查询司机信息
     *
     * @param phone
     * @return
     */
    int findByPhone(String phone);

    /**
     * 获取司机分页信息
     *
     * @param driverQuery
     * @return
     */
    PageInfo<DriverEntity> queryPage(DriverQuery driverQuery);

    /**
     * 通过车辆获取司机信息
     *
     * @param id
     * @return
     */
    List<DriverEntity> getDriverByCarId(Long id);

    /**
     * 通过id集合查询司机
     *
     * @param ids 车辆id集合
     * @return key: 车辆id，value司机集合
     */
    Map<Long, List<DriverEntity>> getDriverByCarIds(Set<Long> ids);

    /**
     * 删除绑定信息
     *
     * @param cityMappingId
     */
    void deleteBindRelation(Long cityMappingId);

    /**
     * 获取司机详情信息
     *
     * @param id
     * @return
     */
    DriverEntity driverDetail(Long id);

    /**
     * 是否存在此司机
     *
     * @param id
     * @return
     */
    Boolean isDriverById(Long id);

    /**
     * 获取司机城配的映射关系信息
     *
     * @param driverId
     * @return
     */
    DriverEntity getDriverCityMapping(Long driverId);

    /**
     * 通过点位获取当前城配仓下的所有司机信息
     *
     * @param siteId
     * @return
     */
    List<DriverEntity> listAllByCitySiteId(Long siteId);

    /**
     * 根据司机名称或者电话查询司机id
     *
     * @param nameOrPhone
     * @return
     */
    List<DriverEntity> searchByNameOrPhone(String nameOrPhone);


    /**
     * 获取司机信息
     *
     * @param driverQuery
     * @return
     */
    List<DriverEntity> queryList(DriverQuery driverQuery);

    /**
     * 查询司机信息
     * @param driverId 司机id
     * @return 结果
     */
    DriverEntity query(Long driverId);

    /**
     * 查询
     * @param driverQuery 查询
     * @return 结果
     */
    DriverEntity queryWithCityCar(DriverQuery driverQuery);

    /**
     * 查询司机绑定车辆信息
     * @param driverIdList 司机ID集合
     * @return
     */
    List<DriverEntity> queryMappingListByDriverIdList(List<Long> driverIdList);
}
