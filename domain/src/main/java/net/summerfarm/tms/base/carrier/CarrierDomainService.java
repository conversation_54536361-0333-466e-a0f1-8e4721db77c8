package net.summerfarm.tms.base.carrier;

import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Description: <br/>
 * date: 2022/9/16 10:57<br/>
 *
 * <AUTHOR> />
 */
@Service
public class CarrierDomainService {

    @Resource
    private CarrierRepository carrierRepository;

    /**
     * 根据承运商id获取承运商名称
     *
     * @param carrierId
     * @return
     */
    public String getNameById(Long carrierId) {
        return carrierRepository.getNameById(carrierId);
    }

    /**
     * 更新承运商信息
     * @param entity 承运商信息
     */
    public void editCarrier(CarrierEntity entity) {
        //查询承运商是否存在相同的名字
        CarrierEntity carrierEntity = carrierRepository.queryById(entity.getId());
        if(carrierEntity == null){
            throw new TmsRuntimeException("不存在此承运商信息");
        }
        if(!Objects.equals(carrierEntity.getCarrierName(),entity.getCarrierName())){
             long num = carrierRepository.queryCountByName(entity.getCarrierName());
             if(num > 0){
                 throw new TmsRuntimeException("名称已存在");
             }
        }
        carrierRepository.update(entity);
    }

    /**
     * 保存承运商信息
     * @param carrierEntity 承运商信息
     */
    public void save(CarrierEntity carrierEntity) {
        long num = carrierRepository.queryCountByName(carrierEntity.getCarrierName());
        if(num > 0){
            throw new TmsRuntimeException("名称已存在");
        }
        carrierRepository.save(carrierEntity);
    }
}
