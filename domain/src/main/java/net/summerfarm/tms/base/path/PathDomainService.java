package net.summerfarm.tms.base.path;

import cn.hutool.core.collection.CollectionUtil;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.delivery.entity.DeliveryBatchEntity;
import net.summerfarm.tms.enums.PathSectionEnums;
import net.summerfarm.tms.enums.TmsPathStatusEnum;
import net.summerfarm.tms.enums.TmsPathTypeEnum;
import net.summerfarm.tms.query.path.PathQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PathDomainService {
    @Resource
    PathRepository pathRepository;
    @Resource
    PathSectionRepository pathSectionRepository;


    public Boolean disablePath(TmsPathEntity tmsPathEntity) {
        return pathRepository.updateStatus(tmsPathEntity.getPathId(),
                TmsPathStatusEnum.DISABLE.code,
                Objects.nonNull(tmsPathEntity.getUpdaterId()) ? Integer.valueOf(tmsPathEntity.getUpdaterId()) : null, tmsPathEntity.getUpdater());
    }

    public Boolean deletePath(TmsPathEntity tmsPathEntity) {
        return pathRepository.updateStatus(tmsPathEntity.getPathId(),
                TmsPathStatusEnum.DELETED.code, null,null);
    }

    public TmsPathEntity temporarySave(TmsPathEntity tmsPathEntity) {
        return pathRepository.temporarySave(tmsPathEntity);
    }

    /**
     * 完成排线固定路线信息
     *
     * @param deliveryBatchEntity
     */
    public void completePath(DeliveryBatchEntity deliveryBatchEntity) {
        TmsPathEntity tmsPathEntity = pathRepository.query(deliveryBatchEntity.getPathId());

        tmsPathEntity.setPathCode(deliveryBatchEntity.getPathCode());
        tmsPathEntity.setPathName(deliveryBatchEntity.getPathName());
        tmsPathEntity.setDriverId(deliveryBatchEntity.getDriverId());
        tmsPathEntity.setCarId(deliveryBatchEntity.getCarId());
        tmsPathEntity.setCarrierId(deliveryBatchEntity.getCarrierId());

        pathRepository.temporaryUpdate(tmsPathEntity);
    }

    /**
     * 点位修改路线之后,刷新预排线
     *
     * @param beginSiteId
     * @param endSiteId
     * @param pathId
     */
    public void refreshPathSection(Long beginSiteId, Long endSiteId, Long pathId) {
        TmsPathSectionEntity tmsPathSectionEntity = new TmsPathSectionEntity();
        tmsPathSectionEntity.setBeginSiteId(beginSiteId);
        tmsPathSectionEntity.setEndSiteId(endSiteId);
        tmsPathSectionEntity.setPathId(pathId);
        pathSectionRepository.saveOrUpdate(tmsPathSectionEntity);
    }

    public TmsPathEntity addSiteBatch(List<TmsPathEntity> pathList, String pathCode, String creatorId, Long beginSiteId) {
        TmsPathEntity tmsPathEntity = null;
        if (CollectionUtils.isNotEmpty(pathList) && pathList.stream().map(TmsPathEntity::getPathCode)
                .collect(Collectors.toList()).contains(pathCode)) {
            //使用本路线新增批次
            List<TmsPathEntity> tmsPathEntities = pathList.stream().filter(path -> Objects.equals(path.getPathCode(), pathCode)).collect(Collectors.toList());
            tmsPathEntity = tmsPathEntities.get(0);
        } else {
            //新增路线和批次信息
            tmsPathEntity = new TmsPathEntity();
            tmsPathEntity.setPathCode(pathCode);
            tmsPathEntity.setStatus(TmsPathStatusEnum.ACTIVE.getCode());
            tmsPathEntity.setBeginSiteId(beginSiteId);
            tmsPathEntity.setCreatorId(creatorId);
            tmsPathEntity.setType(TmsPathTypeEnum.DELIVERY_ROAD.getCode());

            TmsPathSectionEntity tmsPathSectionEntity = new TmsPathSectionEntity();
            tmsPathSectionEntity.setType( PathSectionEnums.Type.CITY.getValue());
            tmsPathSectionEntity.setBeginSiteId(beginSiteId);
            tmsPathSectionEntity.setEndSiteId(beginSiteId);

            tmsPathEntity.setTmsPathSectionList(Arrays.asList(tmsPathSectionEntity));

            pathRepository.temporarySave(tmsPathEntity);
        }

        return tmsPathEntity;
    }

    /**
     * 保存路由
     *
     * @param pathEntity
     * @return
     */
    public TmsPathEntity save(TmsPathEntity pathEntity) {
        TmsPathEntity tmsPathEntity = pathRepository.save(pathEntity);
        return tmsPathEntity;
    }

    /**
     * 根据开始点位和结束点位查询路线id
     *
     * @param endSiteEntity 结束点位
     * @param beginSiteEntity 开始点位
     * @return 匹配的路由路线集合
     */
    public List<TmsPathEntity> findPathByBeginEndSite(SiteEntity endSiteEntity, SiteEntity beginSiteEntity) {
        if (endSiteEntity == null || beginSiteEntity == null) {
            return Collections.emptyList();
        }
        if(beginSiteEntity.getId().equals(endSiteEntity.getId())){
            return Collections.emptyList();
        }
        // 先根据终点查询匹配
        List<TmsPathSectionEntity> endPathSectionEntities =  pathSectionRepository.queryMatchEndSiteTrunkPath(endSiteEntity.getId());
        if(CollectionUtil.isEmpty(endPathSectionEntities)){
            return Collections.emptyList();
        }

        // 找出所有匹配的路线ID
        List<Long> pathIdList = endPathSectionEntities.stream().map(TmsPathSectionEntity::getPathId).distinct().collect(Collectors.toList());

        // 根据获取有效的路线
        List<TmsPathEntity> validPathEntities = pathRepository.queryList(PathQuery.builder()
                .pathIdList(pathIdList)
                .status(TmsPathStatusEnum.ACTIVE.code)
                .build());
        if(CollectionUtil.isEmpty(validPathEntities)){
            return Collections.emptyList();
        }

        List<Long> validPathIds = validPathEntities.stream().map(TmsPathEntity::getPathId).distinct().collect(Collectors.toList());
        Map<Long, TmsPathEntity> pathIdToPathEntityMap = validPathEntities.stream().collect(Collectors.toMap(TmsPathEntity::getPathId, Function.identity()));

        // 查询出路线的所有路段，找出是否有对应的路段
        Map<Long,List<TmsPathSectionEntity>> pathIdToSectionListMap = pathSectionRepository.queryByPathIds(validPathIds);

        List<TmsPathEntity> matchPathEntities = new ArrayList<>();
        pathIdToSectionListMap.forEach((pathId,sectionList) -> {
            if(CollectionUtil.isEmpty(sectionList)){
                return;
            }
            List<Integer> beginSiteSequenceList = sectionList.stream()
                    .filter(e -> e.getBeginSiteId().equals(beginSiteEntity.getId()))
                    .map(TmsPathSectionEntity::getSequence)
                    .collect(Collectors.toList());
            Integer beginSequence = CollectionUtil.isEmpty(beginSiteSequenceList) ? null : beginSiteSequenceList.get(0);

            List<Integer> endSiteSequenceList = sectionList.stream()
                    .filter(e -> e.getEndSiteId().equals(endSiteEntity.getId()))
                    .map(TmsPathSectionEntity::getSequence)
                    .collect(Collectors.toList());

            Integer endSequence = CollectionUtil.isEmpty(endSiteSequenceList) ? null : endSiteSequenceList.get(0);

            if(beginSequence == null || endSequence == null){
                return;
            }
            if(beginSequence <= endSequence){
                if(pathIdToPathEntityMap.get(pathId) != null){
                    matchPathEntities.add(pathIdToPathEntityMap.get(pathId));
                }
            }
        });

        return matchPathEntities;
    }
}
