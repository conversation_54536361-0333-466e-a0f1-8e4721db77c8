package net.summerfarm.tms.base.site;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.base.site.param.SiteRecordCommandParam;
import net.summerfarm.tms.dist.query.SiteTypeDetailAddressBusinessNoQuery;
import net.summerfarm.tms.query.base.carrier.SiteQuery;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/7/13 16:06<br/>
 *
 * <AUTHOR> />
 */
public interface SiteRepository {

    /**
     * 根据城配仓id获取城配仓点位id
     *
     * @param storeNo
     * @return
     */
    Long getCitySiteIdByOutBusinessNo(Integer storeNo);

    /**
     * 根据点位id获取点位信息
     *
     * @param siteId
     * @return
     */
    SiteEntity query(Long siteId);

    /**
     * 根据外部编号和类型查询点位信息
     *
     * @param outBusinessNoList
     * @param type
     * @return
     */
    List<SiteEntity> getSiteByOutBNosAndType(List<String> outBusinessNoList, Integer type);

    /**
     * 根据id批量更新点位信息
     *
     * @param siteEntities
     */
    void batchUpdateSite(List<SiteEntity> siteEntities);

    /**
     * 查询点位信息
     *
     * @param siteQuery
     * @return
     */
    List<SiteEntity> siteSearch(SiteQuery siteQuery);

    /**
     * 查询点位信息
     *
     * @param siteQuery
     * @return
     */
    PageInfo<SiteEntity> siteSearchPage(SiteQuery siteQuery);

    /**
     * 详细地址（采购地址）点位新增
     *
     * @param siteEntity
     * @return
     */
    Long siteAdd(SiteEntity siteEntity);

    /**
     * 根据外部编号和类型查询点位信息
     *
     * @param siteQuery
     * @return
     */
    SiteEntity query(SiteQuery siteQuery);

    SiteEntity siteDetail(Long id, Integer type);

    void checkDuplicate(SiteEntity siteEntity);

    /**
     * 根据主键集合查询点位信息
     * @param siteIdList 点位id集合
     * @return 点位信息
     */
    List<SiteEntity> queryPrimaryIdList(Collection<Long> siteIdList);;

    /**
     * 批量查询
     * @param siteQuery 查询
     * @return 结果
     */
    List<SiteEntity> queryList(SiteQuery siteQuery);

    /**
     * 查询城配点位集合
     *
     * @param regions 区域集合
     * @return 城配点位集合
     */
    List<SiteEntity> queryListByRegion(List<String> regions);

    /**
     * 查询map对象通过ID集合
     *
     * @param siteIds 点位id
     * @return key：siteId  value：SiteEntity
     */
    public Map<Long, SiteEntity> queryMapByIds(List<Long> siteIds);

    /**
     * 查询城配仓信息
     * @param storeNoList 城配仓编号
     * @return 结果
     */
    Map<String, SiteEntity> queryStoreMap(List<String> storeNoList);

    /**
     * 查询仓库仓信息
     * @param warehouseNoList 仓库编号
     * @return 结果
     */
    Map<String, SiteEntity> queryWarehouseMap(List<String> warehouseNoList);

    /**
     * 查询城配仓名称
     * @param storeNo 城配仓编号
     * @return 名字
     */
    String queryStoreName(Object storeNo);
    /**
     * 查询仓库名称
     * @param warehouseNo 库存仓编号
     * @return 名字
     */
    String queryWarehouseName(Object warehouseNo);

    /**
     * 查询主库信息
     * @param outBusinessNo 外部编号
     * @param type 类型
     * @return 结果
     */
    SiteEntity queryForceMasterByOutBusinessNoType(String outBusinessNo, Integer type);

    /**
     * 新增
     * @param siteEntity 点位
     */
    void save(SiteEntity siteEntity);

    /**
     * 更新
     * @param siteEntity 点位
     */
    void update(SiteEntity siteEntity);

    /**
     * 更新点位poi
     * @param siteId 点位id
     * @param poi poi信息
     * @param contactId 联系人ID
     */
    void updatePoiOutBusinessNo(Long siteId, String poi, String contactId);

    /**
     * 查询点位详情 带审核记录
     * @param siteId 点位ID
     * @return 点位详情
     */
    SiteEntity queryWithRecord(Long siteId);

    /**
     * 保存点位审核记录
     * @param param 参数
     * @return 主键ID
     */
    Long saveRecord(SiteRecordCommandParam param);

    /**
     * 分页查询
     * @param query 查询
     * @return 结构
     */
    PageInfo<SiteEntity> queryPage(SiteQuery query);

    /**
     * 根据类型、省市区、详细地址查询
     * @param type 类型
     * @param province 省
     * @param city 城市
     * @param area 区域
     * @param address 详细地址
     * @return
     */
    SiteEntity queryByTypeAndDetailAddress(Integer type, String province, String city, String area, String address);

    /**
     * 根据城配仓编号查询点位信息
     * @param popStoreNosList 城配仓编号集合
     * @return 点位Map key:点位id value:点位信息
     */
    Map<Long, SiteEntity> querySiteStoreMapByOutBusinessNos(List<String> popStoreNosList);

    /**
     * 根据类型、省市区详细地址、外部业务号查询
     * @param query 查询
     * @return 结果
     */
    SiteEntity querySite(SiteTypeDetailAddressBusinessNoQuery query);
}
