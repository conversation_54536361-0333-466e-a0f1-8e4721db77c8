package net.summerfarm.tms.base.path.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线路-路段
 */
@Data
public class TmsPathSectionEntity {
    /**
     * 主键
     */
    private Long id;
    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 路段起点
     */
    private Long beginSiteId;

    /**
     * 路段起点名
     */
    private String beginSiteName;

    /**
     * 路段终点
     */
    private Long endSiteId;

    /**
     * 路段终点名
     */
    private String endSiteName;

    /**
     * 单位 小时，预计花费时间
     */
    private BigDecimal planSpan;

    /**
     * 装货时长
     */
    private BigDecimal loadHour;

    /**
     * 路段在path中的次序
     */
    private Integer sequence;

    /**
     * 所属路线
     */
    private Long pathId;

    /**
     * 类型 0城配 1干线
     */
    private Integer type;

    /**
     * 线路
     */
    private TmsPathEntity pathEntity;

}
