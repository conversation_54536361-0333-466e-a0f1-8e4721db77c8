package net.summerfarm.tms.base.carrier;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.carrier.entity.CarrierEntity;
import net.summerfarm.tms.query.base.carrier.CarrierQuery;

import java.util.Collection;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/7/26 13:36<br/>
 *
 * <AUTHOR> />
 */
public interface CarrierRepository {

    /**
     * 根据承运商id获取承运商名称
     *
     * @param carrierId
     * @return
     */
    String getNameById(Long carrierId);


    List<CarrierEntity> searchByName(String name);

    /**
     * 通过id集合查询承运商列表
     *
     * @param idsList
     * @return
     */
    List<CarrierEntity> getListByIdList(Collection<Long> idsList);

    /**
     * 批量查询承运商信息
     *
     * @param carrierIdList 承运商ID集合
     * @return 结果
     */
    List<CarrierEntity> queryPrimaryIdList(List<Long> carrierIdList);

    /**
     * 分页查询承运商信息
     * @param carrierQuery 查询
     * @return 结果
     */
    PageInfo<CarrierEntity> queryPageList(CarrierQuery carrierQuery);

    /**
     * 根据Id查询承运商信息
     * @param id 主键Id
     * @return 结果
     */
    CarrierEntity queryById(Long id);

    /**
     * 根据Id查询承运商、账户、发票信息
     * @param id 主键Id
     * @return 承运商信息
     */
    CarrierEntity queryWithAccountInvoiceById(Long id);

    /**
     * 更新承运商信息
     * @param entity 承运商信息
     */
    void update(CarrierEntity entity);

    /**
     * 根据名称查询数量
     * @param carrierName 承运商名称
     * @return 数量
     */
    long queryCountByName(String carrierName);

    /**
     * 承运商保存
     * @param carrierEntity 承运商
     * @return 承运商编号
     */
    void save(CarrierEntity carrierEntity);

    /**
     * 查询承运商集合信息
     * @param carrierQuery 查询
     * @return 结果
     */
    List<CarrierEntity> queryList(CarrierQuery carrierQuery);

    /**
     * 查询承运商相关信息
     * @param carrierQuery 查询
     * @return 结果
     */
    List<CarrierEntity> queryListWithInvoiceAccount(CarrierQuery carrierQuery);
}
