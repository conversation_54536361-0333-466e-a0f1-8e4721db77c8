package net.summerfarm.tms.base.path;

import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.query.path.PathSectionQuery;

import java.util.List;
import java.util.Map;

public interface PathSectionRepository {
    /**
     * 保存或更新
     *
     * @param tmsPathSectionEntity
     * @return
     */
    void saveOrUpdate(TmsPathSectionEntity tmsPathSectionEntity);

    /**
     * 查询路段信息
     * @param pathSectionQuery 查询条件
     * @return 结果
     */
    List<TmsPathSectionEntity> queryList(PathSectionQuery pathSectionQuery);

    /**
     * 查询路段信息
     * @param pathSectionQuery 查询条件
     * @return 结果
     */
    TmsPathSectionEntity query(PathSectionQuery pathSectionQuery);

    /**
     * 查询路段信息
     * @param pathSectionQuery 查询条件
     * @return 结果
     */
    List<TmsPathSectionEntity> queryWithPath(PathSectionQuery pathSectionQuery);

    /**
     * 移除预排数据
     *
     * @param pathSectionQuery 需要移除的条件
     */
    void removeList(PathSectionQuery pathSectionQuery);

    /**
     * 根据路由id删除路段信息
     *
     * @param pathId 路由id
     */
    void removeByPathId(Long pathId);

    /**
     * 根据路由查询路段信息
     *
     * @param pathId 路由id
     * @return
     */
    List<TmsPathSectionEntity> queryListByPathId(Long pathId);

    /**
     * 根据路由id集合查询路段信息
     *
     * @param validPathIds 路由id集合
     * @return
     */
    Map<Long, List<TmsPathSectionEntity>> queryByPathIds(List<Long> validPathIds);

    /**
     * 根据终点查询干线匹配的路段信息
     *
     * @param endSiteId 终点点位id
     * @return 路段信息
     */
    List<TmsPathSectionEntity> queryMatchEndSiteTrunkPath(Long endSiteId);
}
