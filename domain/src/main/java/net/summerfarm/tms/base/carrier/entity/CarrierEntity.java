package net.summerfarm.tms.base.carrier.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/9/14 16:33<br/>
 *
 * <AUTHOR> />
 */
@Data
public class CarrierEntity {
       /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    private LocalDateTime updateTime;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 负责人
     */
    private String director;

    /**
     * 负责人电话
     */
    private String directorPhone;

    /**
     * 地址
     */
    private String address;

    /**
     * 合作协议地址
     */
    private String cooperationAgreement;

    /**
     * 业务类型 0干线 1城配 2干线、城配
     */
    private Integer businessType;

    /**
     * 二级类型 100仓储
     */
    private String subBusinessType;

    /**
     * 社会信用代码
     */
    private String socialCreditCode;

    /**
     * 发票信息
     */
    private CarrierInvoiceEntity carrierInvoiceEntity;

    /**
     * 账户信息
     */
    private List<CarrierAccountEntity> carrierAccountEntityList;
}
