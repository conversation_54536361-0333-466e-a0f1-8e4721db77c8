package net.summerfarm.tms.base.driver.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/7/13 15:53<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum StateEnum {
    valid(0, "有效"),
    invalid(1, "无效");
    private Integer code;

    private String desc;

    StateEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> map = new HashMap<>();

    static {
        StateEnum[] values = StateEnum.values();
        for (StateEnum StateEnum : values) {
            map.put(StateEnum.getCode(), StateEnum.getDesc());
        }
    }
}
