package net.summerfarm.tms.driver.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/9/2 16:02<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum CooperationCycleEnum {
    //合作周期 0 临时 1长期
    temporary(0, "临时"),
    longTime(1, "长期"),
    ;

    private Integer code;

    private String desc;

    CooperationCycleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> map = new HashMap<>();

    static {
        CooperationCycleEnum[] values = CooperationCycleEnum.values();
        for (CooperationCycleEnum cooperationCycleEnum : values) {
            map.put(cooperationCycleEnum.getCode(), cooperationCycleEnum.getDesc());
        }
    }
}
