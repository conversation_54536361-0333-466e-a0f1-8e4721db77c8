package net.summerfarm.tms.base.site;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.base.site.param.SiteRecordCommandParam;
import net.summerfarm.tms.constants.RedisConstants;
import net.summerfarm.tms.dist.DistOrderRepository;
import net.summerfarm.tms.dist.entity.DistOrderEntity;
import net.summerfarm.tms.enums.DistOrderSourceEnum;
import net.summerfarm.tms.enums.SiteTypeEnum;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.wnc.WncQueryFacade;
import net.summerfarm.tms.util.RedisCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Description: <br/>
 * date: 2022/9/16 14:42<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class SiteDomainService {

    @Autowired
    private SiteRepository siteRepository;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private DistOrderRepository distOrderRepository;
    @Resource
    private WncQueryFacade wncQueryFacade;
    @Resource
    private RedisCacheUtil redisCacheUtil;

    public Long siteAdd(SiteEntity siteEntity) {
        return siteRepository.siteAdd(siteEntity);
    }
    /**
     * 根据点位id获取点位信息
     *
     * @param siteId
     * @return
     */
    public SiteEntity query(Long siteId) {
        return siteRepository.query(siteId);
    }

    /**
     * 新增仓库点位信息
     * @param siteEntity 参数
     */
    public void createWarehouseSite(SiteEntity siteEntity) {
        if(StringUtils.isBlank(siteEntity.getOutBusinessNo()) || siteEntity.getType() == null || StringUtils.isBlank(siteEntity.getPoi())){
            throw new TmsRuntimeException("新增仓库点位参数不全");
        }
       SiteEntity siteData = siteRepository.queryForceMasterByOutBusinessNoType(siteEntity.getOutBusinessNo(),siteEntity.getType());
        if(siteData != null){
            log.info("仓库点位数据已存在:{}", JSON.toJSONString(siteData));
            return;
        }
        siteRepository.save(siteEntity);
    }

    public void updateWarehouseSite(SiteEntity siteEntity) {
        if(StringUtils.isBlank(siteEntity.getOutBusinessNo()) || siteEntity.getType() == null || StringUtils.isBlank(siteEntity.getPoi())){
            throw new TmsRuntimeException("新增仓库点位参数不全");
        }
        SiteEntity siteData = siteRepository.queryForceMasterByOutBusinessNoType(siteEntity.getOutBusinessNo(),siteEntity.getType());
        if(siteData == null){
            log.info("仓库点位数据不存在:{}", JSON.toJSONString(siteData));
            return;
        }
        siteEntity.setId(siteData.getId());
        siteRepository.update(siteEntity);
    }

    public void saveOrUpdate(SiteEntity siteEntity) {
        if(StringUtils.isBlank(siteEntity.getOutBusinessNo()) || siteEntity.getType() == null || StringUtils.isBlank(siteEntity.getPoi())){
            throw new TmsRuntimeException("更新新增城配仓点位参数不全");
        }
        SiteEntity siteData = siteRepository.queryForceMasterByOutBusinessNoType(siteEntity.getOutBusinessNo(),siteEntity.getType());
        if(siteData != null){
            //更新
            siteEntity.setId(siteData.getId());
            siteRepository.update(siteEntity);
        }else{
            RLock redissonLock = redissonClient.getLock(RedisConstants.Site.STORE_SITE_UPSERT+"-"+siteEntity.getOutBusinessNo());
            try {
                if (!redissonLock.tryLock(0L, 5L, TimeUnit.SECONDS)) {
                    throw new TmsRuntimeException("正在处理，请稍后");
                }
                //新增
                siteRepository.save(siteEntity);
            } catch (InterruptedException e) {
                log.error("城配仓点位新增更新异常",e);
            } finally {
                if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                    redissonLock.unlock();
                }
            }
        }
    }

    public Set<Long> queryPurchaseOwnSalePickDistSite() {
        //获取承运单带承运/承运中 采购/自提销售/提货
        List<DistOrderEntity> distOrderEntities = distOrderRepository.queryActDistOrderBySource(
                Lists.newArrayList(DistOrderSourceEnum.PURCHASE.getCode(),DistOrderSourceEnum.OWN_SALE_OUT.getCode()
                        ,DistOrderSourceEnum.ALL_CATEGORY_PICK.getCode()));
        Set<Long> siteIds = new HashSet<>();
        for (DistOrderEntity distOrderEntity : distOrderEntities) {
            siteIds.add(distOrderEntity.getBeginSite().getId());
            siteIds.add(distOrderEntity.getEndSite().getId());
        }
        return siteIds;
    }

    public void updateSitePics(SiteEntity siteEntity, String sitePics) {
        if (siteEntity == null){
            return;
        }
        if (StrUtil.isBlank(sitePics)){
            return;
        }
        if (Objects.equals(siteEntity.getSitePics(),sitePics)){
            return;
        }
        siteRepository.update(siteEntity.updateSitePics(sitePics));
    }

    public Long createSiteAudit(SiteRecordCommandParam param) {
        if (param == null){
            return null;
        }
        return siteRepository.saveRecord(param);
    }

    /**
     * 查询是否是POP城配仓 缓存版
     * @param siteId 点位ID
     * @return ture:POP城配仓  false:非POP城配仓
     */
    public boolean isPopBySiteIdCache(Long siteId) {
        return redisCacheUtil.getCacheObjectValue(RedisConstants.Key.POP_STORE_CACHE + ":" + siteId, 60L,
                () -> this.isPopBySiteId(siteId), boolean.class);
    }

    /**
     * 查询是否是POP城配仓
     * @param siteId 点位ID
     * @return ture:POP城配仓  false:非POP城配仓
     */
    public boolean isPopBySiteId(Long siteId) {
        if(siteId == null){
            throw new TmsRuntimeException("查询是否是POP城配仓siteId不能为空");
        }
        // 查询点位信息
        SiteEntity siteEntity = siteRepository.query(siteId);
        if(siteEntity == null){
            throw new TmsRuntimeException("点位不存在");
        }
        List<String> popStoreNos = wncQueryFacade.queryPopStoreNosList();
        return Objects.equals(siteEntity.getType(), SiteTypeEnum.store.getCode()) && popStoreNos.contains(siteEntity.getOutBusinessNo());
    }
}
