package net.summerfarm.tms.base.driver.entity;

import lombok.Data;
import net.summerfarm.tms.base.car.entity.CarEntity;

import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/7/15 14:52<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DriverEntity {

    private Long id;

    private String name;

    private String phone;

    private String password;
    /**
     * 合作周期 0 临时 1长期
     */
    private Integer cooperationCycle;

    /**
     * 状态 0有效 1无效
     */
    private Integer status;

    private String idCard;

    private String driverPics;

    private String idCardFrontPic;

    private String idCardBehindPic;

    private Integer adminId;

    private String adminName;
    /**
     * 城配绑定id
     */
    private Long cityMappingId;
    /**
     * 业务类型 0干线 1城配 2干线城配
     */
    private Integer businessType;

    private Long cityWarehouseSiteId;

    private Long cityCarrierId;

    private String cityCarrierName;

    private Long cityCarId;

    private Integer cityStoreNo;

    private String cityStoreName;

    private CarEntity cityCarEntity;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * base_user_id 用户基础表id
     */
    private Long baseUserId;

    /**
     * 司机打款信息
     */
    private DriverAccountEntity driverAccountEntity;

    /**
     *  保温措施稽查 0不稽查 1稽查
     */
    private Integer keepTemperatureMethodAudit;
}
