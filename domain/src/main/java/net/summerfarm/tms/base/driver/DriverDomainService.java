package net.summerfarm.tms.base.driver;

import net.summerfarm.tms.base.car.CarDomainService;
import net.summerfarm.tms.base.carrier.CarrierRepository;
import net.summerfarm.tms.base.driver.entity.DriverEntity;
import net.summerfarm.tms.base.driver.enums.BusinessTypeEnum;
import net.summerfarm.tms.base.site.SiteRepository;
import net.summerfarm.tms.base.site.entity.SiteEntity;
import net.summerfarm.tms.exceptions.TmsRuntimeException;
import net.summerfarm.tms.facade.auth.AuthUpateFacade;
import net.summerfarm.tms.facade.auth.dto.UserBaseDTO;
import net.summerfarm.tms.facade.auth.input.CreateDriverUserInput;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/7/15 15:41<br/>
 *
 * <AUTHOR> />
 */
@Service
public class DriverDomainService {
    @Autowired
    private DriverRepository driverRepository;
    @Autowired
    private SiteRepository siteRepository;

    @Autowired
    @Lazy
    private CarDomainService carDomainService;

    @Autowired
    private CarrierRepository carrierRepository;
    @Resource
    private AuthUpateFacade authUpateFacade;

    public Long create(DriverEntity driverEntity) {
        //城配 数据检查
        cityStoreCheak(driverEntity);
        //调用auth服务
        UserBaseDTO driverUser = authUpateFacade.createDriverUser(CreateDriverUserInput.builder()
                .phone(driverEntity.getPhone())
                .password(driverEntity.getPassword())
                .status(driverEntity.getStatus())
                .build());

        driverEntity.setId(driverUser.getId());
        driverEntity.setBaseUserId(driverUser.getUserBaseId());
        
        //保存账号信息
        return driverRepository.save(driverEntity);
    }

    /**
     * 业务类型 城配 参数校验
     */
    private void cityStoreCheak(DriverEntity driverEntity) {
        if (BusinessTypeEnum.city.getCode().equals(driverEntity.getBusinessType()) || BusinessTypeEnum.trunkAndCity.getCode().equals(driverEntity.getBusinessType())) {
            Integer cityStoreNo = driverEntity.getCityStoreNo();
            if (cityStoreNo == null) {
                throw new TmsRuntimeException("城配仓不能为空");
            }
            //根据城配仓编号查询相关城配仓点位信息
            Long siteId = siteRepository.getCitySiteIdByOutBusinessNo(cityStoreNo);
            driverEntity.setCityWarehouseSiteId(siteId);
        }

        //账号信息
        if (driverEntity.getId() == null) {
            int num = driverRepository.findByPhone(driverEntity.getPhone());
            if (num > 0) {
                throw new TmsRuntimeException("手机号已存在");
            }
        }

        if (driverEntity.getCityCarId() != null) {
            //判断车辆是否有效
            carDomainService.isValidCar(driverEntity.getCityCarId());
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void edit(DriverEntity driverEntity) {
        //参数检查
        cityStoreCheak(driverEntity);
        driverRepository.edit(driverEntity);
    }

    public DriverEntity driverDetail(Long id) {
        if (!driverRepository.isDriverById(id)) {
            throw new TmsRuntimeException("不存在此司机信息");
        }
        //获取司机详情
        DriverEntity driverEntity = driverRepository.driverDetail(id);
        //获取绑定关系
        DriverEntity driverCityMappingEntity = driverRepository.getDriverCityMapping(id);

        if (driverCityMappingEntity != null && driverCityMappingEntity.getCityCarId() != null) {
            //获取绑定车辆信息
            driverEntity.setCityCarEntity(carDomainService.getDetail(driverCityMappingEntity.getCityCarId()));
        }

        if (driverCityMappingEntity != null) {
            if (!StringUtils.isEmpty(driverCityMappingEntity.getCityWarehouseSiteId())) {
                //获取城配仓名称
                SiteEntity siteEntity = siteRepository.query(driverCityMappingEntity.getCityWarehouseSiteId());
                driverEntity.setCityStoreNo(Integer.parseInt(siteEntity.getOutBusinessNo()));
                driverEntity.setCityStoreName(siteEntity.getName());
            }
            driverEntity.setCityMappingId(driverCityMappingEntity.getCityMappingId());
            driverEntity.setCityCarrierId(driverCityMappingEntity.getCityCarrierId());
            driverEntity.setCityCarId(driverCityMappingEntity.getCityCarId());
            //获取承运商名称
            driverEntity.setCityCarrierName(carrierRepository.getNameById(driverCityMappingEntity.getCityCarrierId()));
        }
        
        
        return driverEntity;
    }

    public List<DriverEntity> getDriverByCarId(Long id) {
        //获取司机详情
        return driverRepository.getDriverByCarId(id);
    }

    /**
     * 根据司机id获取司机详情
     *
     * @param id
     * @return
     */
    public DriverEntity getDriverById(Long id) {
        //获取司机详情
        return driverRepository.driverDetail(id);
    }

}
