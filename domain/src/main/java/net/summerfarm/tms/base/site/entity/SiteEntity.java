package net.summerfarm.tms.base.site.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.enums.TmsSiteTypeEnum;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/7/26 13:26<br/>
 *
 * <AUTHOR> />
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SiteEntity implements Serializable {
    private Long id;

    private String province;

    private String city;

    private String area;

    private String address;

    private String poi;

    private String phone;

    private String name;

    /**
     * 点位类型，0：客户，1：城配仓，2：库存仓，3：监管仓，4：采购地址，5：Saas，6：指定地址
     */
    private Integer type;

    /**
     * 是否需要打卡0 不需要 1需要
     */
    private Integer state;

    private BigDecimal punchDistance;

    private String outBusinessNo;

    private String outTime;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String contactPerson;

    private Long superviseSiteId;

    private String fullAddress;

    private Long creator;

    private Integer intelligencePath;

    private String region;

    private String sitePics;

    private List<SiteRecordEntity> siteRecords;

    /**
     * 点位用途 0店铺门店
     */
    private Integer siteUse;

    public String getRealContactPerson(){
        List<Integer> types = Collections.singletonList(TmsSiteTypeEnum.CUSTOMER.getCode());
        if (types.contains(this.type)){
            return this.name;
        }
        return this.contactPerson;
    }

    public String getSiteName(){
        List<Integer> list = Arrays.asList(TmsSiteTypeEnum.CUSTOMER.getCode(), TmsSiteTypeEnum.SPECIFIED.getCode());
        return this.getSiteName(list);
    }

    public String getSiteName(List<Integer> types){
        if (types == null){
            types = new ArrayList<>();
        }
        if (types.contains(this.type)){
            return this.getFullAddress();
        }
        return this.name;
    }

    public String getDeliveryOrderName(){
        List<Integer> list = Arrays.asList(TmsSiteTypeEnum.CUSTOMER.getCode(), TmsSiteTypeEnum.PURCHASE_ADDRESS.getCode());
        if (list.contains(this.type)){
            return this.name;
        }
        return this.contactPerson;
    }

    public String getFullAddress(){
        String completeAddress = this.province + this.city + this.area + this.address;
        return completeAddress.replaceAll(" ", "").replaceAll("NULL", "").replaceAll("null", "");
    }

    public void create(Integer creator) {
        this.createTime = LocalDateTime.now();
        this.creator = creator == null ? null : creator.longValue();
    }

    public SiteEntity updateSitePics(String sitePics) {
        SiteEntity updateSiteEntity = new SiteEntity();
        updateSiteEntity.setId(this.id);
        updateSiteEntity.setSitePics(sitePics);
        return updateSiteEntity;
    }

    public SiteRecordEntity getRecentSubmitRecord(Long deliverySiteId){
        if (CollectionUtils.isEmpty(this.siteRecords)){
            return null;
        }
        List<SiteRecordEntity> existRecords = this.siteRecords.stream().filter(e -> Objects.equals(e.getDeliverySiteId(), deliverySiteId))
                .sorted(Comparator.comparing(SiteRecordEntity::getId, Comparator.reverseOrder())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existRecords)){
            return null;
        }
        return existRecords.get(0);
    }
}
