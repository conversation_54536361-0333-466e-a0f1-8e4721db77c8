package net.summerfarm.tms.base.path;

import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.base.path.entity.TmsPathEntity;
import net.summerfarm.tms.enums.PathSectionEnums;
import net.summerfarm.tms.enums.TmsPathTypeEnum;
import net.summerfarm.tms.query.path.PathQuery;
import net.summerfarm.tms.query.path.QueryPathFilterDTO;

import java.util.List;

public interface PathRepository {
    /**
     * 根据 条件查询 线路列表
     *
     * @param pathQuery
     * @return
     */
    PageInfo<TmsPathEntity> queryPage(PathQuery pathQuery);

    /**
     * 根据 主键 查询 线路
     *
     * @param id
     * @return
     */
    TmsPathEntity query(Long id);

    /**
     * 根据 主键 查询线路详情
     *
     * @param id
     * @return
     */
    TmsPathEntity queryDetail(Long id);

    /**
     * 更新状态
     *
     * @param pathId
     * @param newStatus
     * @param updater
     * @param updaterId
     * @return
     */
    Boolean updateStatus(Long pathId, Integer newStatus,Integer updaterId,String updater);

    /**
     * 查询已排线路信息
     *
     * @param beginSiteId        起点ID
     * @param endSiteId          终点ID
     * @param typeEnum           路线类型枚举
     * @return 线路信息
     */
    TmsPathEntity getPrePath(Long beginSiteId, Long endSiteId, TmsPathTypeEnum typeEnum);

    TmsPathEntity temporarySave(TmsPathEntity tmsPathEntity);

    /**
     * 更新
     * @param tmsPathEntity
     */
    void temporaryUpdate(TmsPathEntity tmsPathEntity);

    /**
     * 查询
     * @param pathQuery 查询条件
     * @return 结果
     */
    TmsPathEntity query(PathQuery pathQuery);

    /**
     * 查询集合
     * @param pathQuery 查询条件
     * @return 结果
     */
    List<TmsPathEntity> queryList(PathQuery pathQuery);

    /**
     * 批量更新或者保存
     *
     * @param tmsPathList 实体集合
     */
    void batchSaveOrUpdate(List<TmsPathEntity> tmsPathList);


    /**
     * 保存路由信息接口
     *
     * @param tmsPathEntity
     * @return
     */
    TmsPathEntity save(TmsPathEntity tmsPathEntity);

    /**
     * 编辑路由信息
     *
     * @param tmsPathEntity
     * @return
     */
    TmsPathEntity update(TmsPathEntity tmsPathEntity);

    /**
     * 根据线路代码查询已启用的路由
     *
     * @param pathCode
     * @param id
     * @return
     */
    TmsPathEntity queryByCode(String pathCode, Long id);

    /**
     * 查询路由列表
     *
     * @param filterDTO 查询路由过滤参数
     * @return
     */
    PageInfo<TmsPathEntity> queryList(QueryPathFilterDTO filterDTO);


    /**
     * 查询路线信息
     * @param pathIdList 主键id集合
     * @return 结果
     */
    List<TmsPathEntity> queryListByIdList(List<Long> pathIdList);
}
