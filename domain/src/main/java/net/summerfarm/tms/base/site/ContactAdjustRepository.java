package net.summerfarm.tms.base.site;

import net.summerfarm.tms.base.site.entity.ContactAdjustEntity;
import net.summerfarm.tms.base.site.param.ContactAdjustCommandParam;
import net.summerfarm.tms.query.site.ContactAdjustQuery;

/**
 * Description: <br/>
 * date: 2023/1/5 11:28<br/>
 *
 * <AUTHOR> />
 */
public interface ContactAdjustRepository {

    ContactAdjustEntity query(ContactAdjustQuery contactAdjustQuery);

    /**
     * 保存地址调整记录
     * @param param 参数
     */
    void save(ContactAdjustCommandParam param);
}
