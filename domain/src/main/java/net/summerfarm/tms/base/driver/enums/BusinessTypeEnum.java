package net.summerfarm.tms.base.driver.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * Description: <br/>
 * date: 2022/7/13 16:55<br/>
 *
 * <AUTHOR> />
 */
@Getter
public enum BusinessTypeEnum {
    //业务类型 0干线 1城配 2干线城配
    trunk(0, "干线"),
    city(1, "城配"),
    trunkAndCity(2, "干线城配"),
    ;

    private Integer code;

    private String desc;

    BusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Map<Integer, String> map = new HashMap<>();

    static {
        BusinessTypeEnum[] values = BusinessTypeEnum.values();
        for (BusinessTypeEnum businessTypeEnum : values) {
            map.put(businessTypeEnum.getCode(), businessTypeEnum.getDesc());
        }
    }

}
