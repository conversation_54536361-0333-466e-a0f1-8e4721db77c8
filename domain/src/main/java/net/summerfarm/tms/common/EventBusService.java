package net.summerfarm.tms.common;


import net.summerfarm.tms.event.DeliveryPickLackGoodsMessage;
import net.summerfarm.tms.event.StockEvent;
import net.summerfarm.tms.event.TmsDeliveryEvent;
import net.summerfarm.tms.message.in.DeliveryPerformanceReviewAppealCreateMessage;
import net.summerfarm.tms.message.out.CalcBatchLoadRatioMessage;
import net.summerfarm.tms.message.out.CalcTmsPathDistanceMessage;
import net.summerfarm.tms.message.out.CompletePathMessage;
import net.summerfarm.tms.message.out.TrunkDistOrderStateMessage;
import net.summerfarm.tms.message.out.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EventBusService {

    /**
     * 发送配送过程状态消息到 MQ
     * @param tmsDeliveryEvent 配送事件实体
     */
    void notifyDeliveryEvent(TmsDeliveryEvent tmsDeliveryEvent);

    /**
     * 发送拣货缺货信息
     *
     * @param deliveryPickLackGoodsMessages
     */
    void notifyDeliveryPickLackGoodsMessage(List<DeliveryPickLackGoodsMessage> deliveryPickLackGoodsMessages);

    /**
     * 发送配送过程状态消息到 MQ
     * @param tmsDeliveryEvent 配送事件实体
     */
    void notifyTrunkDeliveryEvent(net.summerfarm.tms.client.event.TmsDeliveryEvent tmsDeliveryEvent);

    /**
     * 出发同步数据到老模型
     *
     * @param distOrderId
     */
    void notifySync2OldTms(Long distOrderId);

    /**
     * 出发同步数据到老模型
     *
     * @param distOrderId
     */
    void notifySync2OldTmsDelayTime(Long distOrderId);

    /**
     * 通知WMS
     *
     * @param lackGoodsApprovedEvent
     */
    void notifyWms(StockEvent lackGoodsApprovedEvent);

    /**
     * 完成排线通知tms
     * @param batchId 批次id
     */
    void finishCompletePathNotifyTms(Long batchId);

    /**
     * 顺序同步老模型
     * @param distOrderId
     */
    void notifySync2OldTmsSendOrderly(Long distOrderId);

    /**
     * 完成排线通知总线
     *
     * @param completePathMessage 消息
     */
    void finishCompletePathAllMessageSend(CompletePathMessage completePathMessage);

    /**
     * 计算路线距离
     *
     * @param calcTmsPathDistanceMessage
     */
    void calcPathDistance(CalcTmsPathDistanceMessage calcTmsPathDistanceMessage);

    /**
     * 计算装载率
     *
     * @param calcBatchLoadRatioMessage 计算批次装载率消息
     */
    void calcBatchLoadRatio(CalcBatchLoadRatioMessage calcBatchLoadRatioMessage);

    /**
     * 干线委托单状态消息
     *
     * @param trunkDistOrderMessage 干线委托单消息
     */
    void trunkDistOrderState(TrunkDistOrderStateMessage trunkDistOrderMessage);

    /**
     * 履约审核申诉创建消息
     * @param msg 履约审核申诉创建消息
     */
    void notifyDeliveryPerformanceReviewAppealCreateMsg(DeliveryPerformanceReviewAppealCreateMessage msg);


    /**
     * 干线拣货缺货消息
     *
     * @param trunkBatchPickUpShortGoodsMsg 干线拣货缺货消息
     */
    void trunkBatchPickUpShortGoods(TrunkBatchPickUpShortGoodsMsg trunkBatchPickUpShortGoodsMsg);
}
