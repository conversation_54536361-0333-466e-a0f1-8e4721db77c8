package tms.dist;
/*import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.base.site.dto.SiteDTO;
import net.summerfarm.tms.dist.DistOrderService;*/
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * Description:委托单应用服务测试类
 * date: 2022/9/15 10:22
 *
 * <AUTHOR>
 */
@SpringBootTest
public class DistOrderServiceTest {

    /*@Resource
    private DistOrderService distOrderService;*/
   /* @Resource
    private DistConfigRepository distConfigRepository;
    @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;*/
    /*@Resource
    private Old2NewSyncService old2NewSyncService;

    @Test
    public void queryOrderDeliveryPlanListTest(){
        System.out.println(old2NewSyncService.queryOrderDeliveryPlanDetail(5597997));
        System.out.println(old2NewSyncService.queryOrderDeliveryPlanDetail(5597996));

    }

    @Test
    public void queryBatchFinishByDistIdTest(){
        //deliveryBatchDomainService.queryBatchFinishByDistId(79L);

    }

    @Test
    public void configInitTest(){
        //distConfigRepository.configInit(null);

    }

    @Test
    public void completeDistOrderTest(){
        distOrderService.completeDistOrder(1L);

    }

    @Test
    public void submitDistOrderTest(){
        DistOrderCommand distOrderCommand = getDistOrderCommand();
        TmsResult<Long> result = distOrderService.submitDistOrder(distOrderCommand);
        Assertions.assertTrue(result.isSuccess());
        System.out.println(JSON.toJSONString(result.getData()));

    }

    @Test
    public void queryPageDistOrderTest(){
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        TmsResult<PageInfo<DistOrderDTO>> result = distOrderService.queryPage(distOrderQuery);
        Assertions.assertTrue(result.isSuccess());
        System.out.println(JSON.toJSONString(result.getData()));

    }

    @Test
    public void queryDetailByDistIdTest(){
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setDistId(1L);
        TmsResult<DistOrderDTO> result = distOrderService.queryDetail(distOrderQuery);
        Assertions.assertTrue(result.isSuccess());
        System.out.println(JSON.toJSONString(result.getData()));
    }

    @Test
    public void queryDetailByOutOrderIdTest(){
        DistOrderQuery distOrderQuery = new DistOrderQuery();
        distOrderQuery.setOuterOrderId("123");
        distOrderQuery.setSource(DistOrderSourceEnum.SALE_OUT.getCode());
        TmsResult<DistOrderDTO> result = distOrderService.queryDetail(distOrderQuery);
        Assertions.assertTrue(result.isSuccess());
        System.out.println(JSON.toJSONString(result.getData()));
    }

    @Test
    public void cancelDistOrderTest(){
        DistOrderEndCommand distOrderEndCommand = new DistOrderEndCommand();
        distOrderEndCommand.setOuterOrderId("123");
        distOrderEndCommand.setSource(DistOrderSourceEnum.SALE_OUT.getCode());
        distOrderEndCommand.setUpdater("更新人");
        distOrderEndCommand.setUpdaterId("321");
        TmsResult<Void> result = distOrderService.cancelDistOrder(distOrderEndCommand);
        Assertions.assertTrue(result.isSuccess());
    }

    @Test
    public void closeDistOrderTest(){
        DistOrderEndCommand distOrderEndCommand = new DistOrderEndCommand();
        distOrderEndCommand.setDistId(18L);
        distOrderEndCommand.setCloseReason("....");
        TmsResult<Void> result = distOrderService.closeDistOrder(distOrderEndCommand);
        Assertions.assertTrue(result.isSuccess());
    }

    @Test
    public void editMidSiteTest(){
        //295~298
        TmsResult<Void> result = distOrderService.editMidSite(7L, 298L);
        Assertions.assertTrue(result.isSuccess());
    }

    @Test
    public void isAutoSubmitDistOrderTest(){
        DistBlackConfigDTO distBlackConfigDTO = new DistBlackConfigDTO();
        SiteDTO beginSiteDto = new SiteDTO();
        beginSiteDto.setOutBusinessNo("10");
        beginSiteDto.setProvince("浙江省");
        beginSiteDto.setCity("嘉兴市");
        beginSiteDto.setArea("南湖区");
        beginSiteDto.setAddress("纺工路中央公园(富民路口)");
        beginSiteDto.setPoi("120.767666,30.729035");
        beginSiteDto.setPhone("***********");
        beginSiteDto.setName("嘉兴库存仓");
        beginSiteDto.setContactPerson("嘉兴仓库联系人");
        beginSiteDto.setType(TmsSiteTypeEnum.WAREHOUSE.getCode());

        SiteDTO endSiteDto = new SiteDTO();
        endSiteDto.setOutBusinessNo("1");
        endSiteDto.setProvince("浙江省");
        endSiteDto.setCity("杭州市");
        endSiteDto.setArea("西湖区");
        endSiteDto.setAddress("龙章路");
        endSiteDto.setPoi("120.058591,30.279943");
        endSiteDto.setPhone("***********");
        endSiteDto.setName("杭州城配仓");
        endSiteDto.setContactPerson("杭州仓库联系人");
        endSiteDto.setType(TmsSiteTypeEnum.STORE.getCode());
        distBlackConfigDTO.setSource(DistOrderSourceEnum.SALE_OUT.getCode());
        distBlackConfigDTO.setBeginSite(beginSiteDto);
        distBlackConfigDTO.setEndSite(endSiteDto);
        TmsResult<Boolean> result = distOrderService.isAutoSubmitDistOrder(distBlackConfigDTO);
        Assertions.assertTrue(result.isSuccess());
        System.out.println(JSON.toJSONString(result.getData()));

    }

    private DistOrderCommand getDistOrderCommand() {
        DistOrderCommand distOrderCommand = new DistOrderCommand();
        distOrderCommand.setOuterOrderId("123");
        distOrderCommand.setSource(DistOrderSourceEnum.SALE_OUT.getCode());

        SiteDTO beginSiteDto = new SiteDTO();
        beginSiteDto.setOutBusinessNo("10");
        beginSiteDto.setProvince("浙江省");
        beginSiteDto.setCity("嘉兴市");
        beginSiteDto.setArea("南湖区");
        beginSiteDto.setAddress("纺工路中央公园(富民路口)");
        beginSiteDto.setPoi("120.767666,30.729035");
        beginSiteDto.setPhone("***********");
        beginSiteDto.setName("嘉兴库存仓");
        beginSiteDto.setContactPerson("嘉兴仓库联系人");
        beginSiteDto.setType(TmsSiteTypeEnum.WAREHOUSE.getCode());

        SiteDTO endSiteDto = new SiteDTO();
        beginSiteDto.setOutBusinessNo("1");
        endSiteDto.setProvince("浙江省");
        endSiteDto.setCity("杭州市");
        endSiteDto.setArea("西湖区");
        endSiteDto.setAddress("龙章路");
        endSiteDto.setPoi("120.058591,30.279943");
        endSiteDto.setPhone("***********");
        endSiteDto.setName("客户联系人");
        endSiteDto.setContactPerson("客户联系人");
        endSiteDto.setType(TmsSiteTypeEnum.CUSTOMER.getCode());
        distOrderCommand.setBeginSite(beginSiteDto);
        distOrderCommand.setEndSite(endSiteDto);
        distOrderCommand.setExpectBeginTime(LocalDateTime.now());
        distOrderCommand.setCreator("测试");
        distOrderCommand.setCreatorId("123");
        distOrderCommand.setPickType(DistPickTypeEnum.DEFAULT.getCode());

        DistOrderItemDTO distOrderItemDTO1 = new DistOrderItemDTO();
        distOrderItemDTO1.setOuterItemId("***********");
        distOrderItemDTO1.setOuterItemType("品类1");
        distOrderItemDTO1.setOuterItemName("可口可乐o");
        distOrderItemDTO1.setVolume(new BigDecimal("2.111"));
        distOrderItemDTO1.setWeight(new BigDecimal("2.222"));
        distOrderItemDTO1.setQuantity(5);
        distOrderItemDTO1.setTemperature(TmsTemperatureEnum.NORMAL.getCode());
        distOrderItemDTO1.setSpecification("1L*1L");
        distOrderItemDTO1.setUnit("包");
        distOrderItemDTO1.setType(DistItemTypeEnum.DEFAULT.getCode());

        DistOrderItemDTO distOrderItemDTO2 = new DistOrderItemDTO();
        distOrderItemDTO2.setOuterItemId("5483773214");
        distOrderItemDTO2.setOuterItemType("品类2");
        distOrderItemDTO2.setOuterItemName("水果");
        distOrderItemDTO2.setVolume(new BigDecimal("5.333"));
        distOrderItemDTO2.setWeight(new BigDecimal("5.333"));
        distOrderItemDTO2.setQuantity(2);
        distOrderItemDTO2.setTemperature(TmsTemperatureEnum.COLD.getCode());
        distOrderItemDTO2.setSpecification("0.01个*1个");
        distOrderItemDTO2.setUnit("个");
        distOrderItemDTO2.setType(DistItemTypeEnum.FRUIT.getCode());

        DistOrderItemDTO distOrderItemDTO3 = new DistOrderItemDTO();
        distOrderItemDTO3.setOuterItemId("19271202732");
        distOrderItemDTO3.setOuterItemType("品类3");
        distOrderItemDTO3.setOuterItemName("安慕希奶油");
        distOrderItemDTO3.setVolume(new BigDecimal("10.111"));
        distOrderItemDTO3.setWeight(new BigDecimal("10.222"));
        distOrderItemDTO3.setQuantity(1);
        distOrderItemDTO3.setTemperature(TmsTemperatureEnum.FREEZE.getCode());
        distOrderItemDTO3.setSpecification("200ml*24盒");
        distOrderItemDTO3.setUnit("盒");
        distOrderItemDTO3.setType(DistItemTypeEnum.DEFAULT.getCode());
        distOrderCommand.setDistOrderItemList(Lists.newArrayList(distOrderItemDTO1,distOrderItemDTO2,distOrderItemDTO3));
        return distOrderCommand;
    }*/

}
