package tms;


import net.summerfarm.tms.common.AjaxResult;

import net.summerfarm.tms.expense.ExpenseService;
import net.summerfarm.tms.expense.dto.ExpenseDetailVO;
import net.summerfarm.tms.expense.dto.ExpenseVO;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> xrt
 * create at:  2021/11/19
 */

public class ExpenseServerTest extends BaseTest{
    @Resource
    ExpenseService expenseService;


    @Test
    public void getMileage(){
//        AjaxResult result = expenseService.getMileage("杭州市余杭区未来科技城", "浙江省杭州市余杭区杭州昊方控制设备有限公司");
//        System.out.println(result);
    }
    @Test
    public void selectTmsTaskDetail(){
        ExpenseVO expense = new ExpenseVO();
        expense.setDeliveryPathId(3125);
        List<ExpenseDetailVO> objects1 = new ArrayList<>();
        ExpenseDetailVO expenseDetail = new ExpenseDetailVO();
        expenseDetail.setStartAddress("123");
        List<String> objects = new ArrayList<>();
        objects.add("test/nz7gmiyo59833umvr.jfif");
        objects.add("test/32arljwzo6l33upu5.jfif");
        expenseDetail.setPictures(objects);
        expenseDetail.setEndAddress("浙江省杭州市拱墅区台州路1号拱墅区政府1号楼3F 拱墅区农业局");
        expenseDetail.setMileage(BigDecimal.valueOf(4.12222));
        expenseDetail.setAmount(BigDecimal.valueOf(200.12222));
        expenseDetail.setRemark("仓配测试");
        objects1.add(expenseDetail);
        expense.setExpenseDetails(objects1);
        expenseService.insertExpenseDetail(expense);
    }
//
//    @Test
//    public void selectDeliveryPath(){
//        tmsTaskService.selectDeliveryPath(6);
//    }
//
//    @Test
//    public void selectDeliveryPathDetail(){
//        tmsTaskService.selectDeliveryPathDetail(1474);
//    }
//
//    @Test
//    public void finishPickDetail(){
//        TmsTaskVO tmsTaskVO = new TmsTaskVO();
//        List<PickDetail> pickDetails = new ArrayList<>();
//        PickDetail pickDetail = new PickDetail();
//        pickDetail.setId(36);
//        pickDetail.setDetailStatus(1);
//        pickDetail.setShortCnt(1);
//        pickDetails.add(pickDetail);
//        //tmsTaskVO.setPickDetails(pickDetails);
//        tmsTaskVO.setId(36);
//        Object obj = JSONArray.toJSON(tmsTaskVO);
//        System.out.println(obj.toString());
//        tmsTaskService.finishPickDetail(tmsTaskVO);
//    }
//
//    @Test
//    public void finishDeliveryPath(){
//        DeliveryPathVO deliveryPathVO = new DeliveryPathVO();
//        deliveryPathVO.setId(1450);
//        List<DeliveryPathShortSku> deliveryPathShortSkus = new ArrayList<>();
//        DeliveryPathShortSku deliveryPathShortSku = new DeliveryPathShortSku();
//        deliveryPathShortSku.setDeliveryPathId(1450);
//        deliveryPathShortSku.setShortCnt(1);
//        deliveryPathShortSku.setSku("1235567");
//        deliveryPathShortSkus.add(deliveryPathShortSku);
//        deliveryPathVO.setDeliveryPathShortSkuList(deliveryPathShortSkus);
//        deliveryPathVO.setFinishPoi("120.074394,30.283358");
//        tmsTaskService.finishDeliveryPath(deliveryPathVO);
//    }
//
//    @Resource
//    InventoryMapper inventoryMapper;
//
//    @Test
//    public void testInventory(){
//        InventoryVO inventoryVO = inventoryMapper.selectInventoryBySku("5430586807");
//        System.out.println(inventoryVO);
//    }
}
