package tms.batch;

import com.alibaba.fastjson.JSON;
import net.summerfarm.tms.base.TmsResult;
import net.summerfarm.tms.delivery.DeliveryBatchService;
import net.summerfarm.tms.delivery.dto.DeliveryOrderSaveCommond;
import net.summerfarm.tms.delivery.dto.DeliverySiteDTO;
import net.summerfarm.tms.dist.DistOrderService;
import net.summerfarm.tms.dist.dto.DistOrderCommand;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/9/22 14:25<br/>
 *
 * <AUTHOR> />
 */
@SpringBootTest
public class DeliveryBatchServiceTest {
    @Resource
    private DeliveryBatchService deliveryBatchService;
   /* @Resource
    private DeliveryBatchDomainService deliveryBatchDomainService;*/

    @Test
    public void deliveryBatchSaveTest(){
        DeliveryOrderSaveCommond deliveryOrderSaveCommond = new DeliveryOrderSaveCommond();
        deliveryOrderSaveCommond.setType(DeliveryBatchTypeEnum.trunk.getCode());
        deliveryOrderSaveCommond.setDeliveryTime(LocalDateTime.now());
        deliveryOrderSaveCommond.setBeginTime(LocalDateTime.now());
        deliveryOrderSaveCommond.setCarrierId(1L);
        deliveryOrderSaveCommond.setCarId(2L);
        deliveryOrderSaveCommond.setDriverId(395L);

        List<DeliverySiteDTO> deliverySiteList = new ArrayList<>();
        DeliverySiteDTO deliverySiteDTO1 = new DeliverySiteDTO();
        deliverySiteDTO1.setSiteId(295L);
        deliverySiteDTO1.setPlanArriveTime(LocalDateTime.now());

        DeliverySiteDTO deliverySiteDTO2 = new DeliverySiteDTO();
        deliverySiteDTO2.setSiteId(297L);
        deliverySiteDTO2.setPlanArriveTime(LocalDateTime.now());

        DeliverySiteDTO deliverySiteDTO3 = new DeliverySiteDTO();
        deliverySiteDTO3.setSiteId(207L);
        deliverySiteDTO3.setPlanArriveTime(LocalDateTime.now());

        deliverySiteList.add(deliverySiteDTO1);
        deliverySiteList.add(deliverySiteDTO2);
        deliverySiteList.add(deliverySiteDTO3);

        deliveryOrderSaveCommond.setDeliverySiteList(deliverySiteList);
        deliveryOrderSaveCommond.setOrderIdList(Arrays.asList(10L));

        TmsResult<Void> voidTmsResult = deliveryBatchService.deliveryBatchSave(deliveryOrderSaveCommond);
        System.out.println(voidTmsResult);
    }

    @Test
    public void complete(){
        /*Boolean aBoolean = deliveryBatchDomainService.queryBatchFinishByDistId(143L);
        System.out.println(aBoolean);*/
    }
}
