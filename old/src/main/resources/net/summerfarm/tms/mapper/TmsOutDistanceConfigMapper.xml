<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsOutDistanceConfigMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.tms.domain.TmsOutDistanceConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_no" jdbcType="INTEGER" property="storeNo" />
    <result column="out_distance" jdbcType="REAL" property="outDistance" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="admin_id" jdbcType="INTEGER" property="adminId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, store_no, out_distance, state, admin_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tms_out_distance_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tms_out_distance_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="net.summerfarm.tms.domain.TmsOutDistanceConfig">
    insert into tms_out_distance_config (id, store_no, out_distance, 
      state, admin_id, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{storeNo,jdbcType=INTEGER}, #{outDistance,jdbcType=REAL}, 
      #{state,jdbcType=TINYINT}, #{adminId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="net.summerfarm.tms.domain.TmsOutDistanceConfig">
    insert into tms_out_distance_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="outDistance != null">
        out_distance,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="adminId != null">
        admin_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="outDistance != null">
        #{outDistance,jdbcType=REAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="adminId != null">
        #{adminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.tms.domain.TmsOutDistanceConfig">
    update tms_out_distance_config
    <set>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=INTEGER},
      </if>
      <if test="outDistance != null">
        out_distance = #{outDistance,jdbcType=REAL},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="adminId != null">
        admin_id = #{adminId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.summerfarm.tms.domain.TmsOutDistanceConfig">
    update tms_out_distance_config
    set store_no = #{storeNo,jdbcType=INTEGER},
      out_distance = #{outDistance,jdbcType=REAL},
      state = #{state,jdbcType=TINYINT},
      admin_id = #{adminId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStoreNo" resultMap="BaseResultMap">
    SELECT
      *
    FROM
      tms_out_distance_config t
    where
      t.store_no = #{storeNo}
      and t.state = 0
  </select>
</mapper>