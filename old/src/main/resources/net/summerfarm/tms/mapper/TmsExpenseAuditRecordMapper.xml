<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsExpenseAuditRecordMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.tms.domain.TmsExpenseAuditRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="mname" jdbcType="VARCHAR" property="mname" />
    <result column="audit_name" jdbcType="VARCHAR" property="auditName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="delivery_path_id" jdbcType="INTEGER" property="deliveryPathId" />
    <result column="expense_id" jdbcType="INTEGER" property="expenseId" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="tms_delivery_site_id" property="deliverySiteId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, mname, audit_name, `status`, driver_id, delivery_path_id, 
    expense_id, submit_time,reason,tms_delivery_site_id
  </sql>
  <select id="selectByDriverId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tms_expense_audit_record where driver_id = #{id}
    order by create_time desc
  </select>

</mapper>