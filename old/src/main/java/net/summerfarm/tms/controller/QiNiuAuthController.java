package net.summerfarm.tms.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import net.summerfarm.tms.common.AjaxResult;
import net.summerfarm.tms.common.util.qiNiu.UploadTokenFactory;
import net.summerfarm.tms.contexts.QiNiuConstant;
import net.summerfarm.tms.contexts.ResultConstant;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * @Package: net.summerfarm.controller
 * @Description: 七牛资源
 * @author: <EMAIL>
 * @Date: 2016/7/21
 */
@Api(tags = "七牛资源")
@RestController
@RequestMapping(value = "/qiniu")
public class QiNiuAuthController {
    /**
     * 获取上传图片到七牛的token和key值（前端自由上传）
     * @return
     */
    @ApiOperation(value = "获取上传图片到七牛的token和key值（前端自由上传）",httpMethod = "POST",tags = "七牛资源")
    @ApiImplicitParam(name = "fileName",value = "文件名称",paramType = "query")
    @RequestMapping(value = "upload-token/one", method = RequestMethod.POST)
    public AjaxResult uploadTokenByFileName(String fileName){
        Map<String, String> data=UploadTokenFactory.createToken(fileName, QiNiuConstant.DEFAULT_EXPIRES);
        if (data == null) {
            return AjaxResult.getError(ResultConstant.UPLOAD_TYPE_NOT_EXIST);
        }
        return AjaxResult.getOK(data);
    }
}
