package net.summerfarm.tms.contexts;

/**
 * @Package: com.manageSystem.contexts
 * @Description: 正则变量
 * @author: <EMAIL>
 * @Date: 2016/7/24
 */
public class RegConstant {

    /**
     * 商户名
     */
    public static final String MNAME_REG = "^[\\w\u4e00-\u9fa5]{1,20}$";
    /**
     * 手机号码
     */
    public static final String PHONE_REG = "^([0-9]{11})$";
    /**
     * 邮箱
     */
    public static final String EMAIL_REG = "\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";
    /**
     * 15位身份证
     */
    public static final String FIFTEEN_IDENTITY_CARD_REG = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
    /**
     * 18位身份证
     */
    public static final String EIGHTEEN_IDENTITY_CARD_REG = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$";
    /**
     * 真实姓名
     */
    public static final String REALNAME_REG = "^[\u4e00-\u9fa5]{2,5}$";
    /**
     * 后台密码：*
     * 数字+字母+特殊字符
     */
    public static final String BACKSTAGE_PASSWORD_REG = "^(?=.*\\d.*)(?=.*[a-zA-Z].*)(?=.*[-`~!@#$%^&*()_+\\|\\\\=,./?><\\{\\}\\[\\]].*).*$";
    /**
     * 网址
     */
    public static final String URL_REG = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w- ./?%&=]*)?";
    /**
     * 数字
     */
    public static final String NUMBER_REG = "^[0-9]{1,}";
    /**
     * 省心送订单号组成 业务代码：02  + 13位时间戳 +2位随机数
     */
    public static final String TIMING_ORDER_REG = "^02[0-9]{15}";

    /**
     * 预售订单号组成 业务代码：05  + 13位时间戳 +2位随机数
     */
    public static final String PRE_SALE_ORDER_REG = "^05[0-9]{15}";

    public static final String ALL_NUMBER = "[0-9]{10,}";
    /**
     * 容量*数量 规格校验
     */
    public static final String WEIGHT_REG1 = "[0,1]{1}\\_\\d{1,5}\\.{0,1}\\d{0,2}[\\w\\u4e00-\\u9fa5]{1,2}\\*\\d{1,5}[\\u4e00-\\u9fa5]{1}";
    /**
     * 区间规格校验
     */
    public static final String WEIGHT_REG2 = "[0,1]{1}\\_毛重\\d{1,5}\\.{0,1}\\d{0,2}\\-\\d{1,5}\\.{0,1}\\d{0,2}[\\w\\u4e00-\\u9fa5]{1,2}";
    /**
     * 体积格式校验
     */
    public static final String VOLUME_REG = "[0-9]+\\.{0,1}[0-9]{0,2}\\*[0-9]+\\.{0,1}[0-9]{0,2}\\*[0-9]+\\.{0,1}[0-9]{0,2}";
    /**
     * 大写字母校验
     */
    public static final String UPPERCASE_LETTER_REG = "[A-Z]{1}";
}
