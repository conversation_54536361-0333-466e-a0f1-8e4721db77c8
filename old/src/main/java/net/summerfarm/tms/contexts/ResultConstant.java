package net.summerfarm.tms.contexts;

/**
 * AjaxResult返回code值
 *
 * @author: <EMAIL>
 * @Date: 2016/7/18
 */
public class ResultConstant {
    /**
     * 默认值 - 执行失败时ReturnContext的code
     */
    public static final String DEFAULT_FAILED = "DEFAULT_FAILED";
    /**
     * 未请求到openid
     */
    public static final String OPENID_NOT_FOUND = "OPENID_NOT_FOUND";
    /**
     * 未登录
     */
    public static final String LOGIN_FIRST = "LOGIN_FIRST";
    /**
     * 账号被冻结
     */
    public static final String IN_LOCK = "IN_LOCK";

    /**
     * 获取openid 异常
     */
    public static final String GET_OPEN_ID_CODE_ERR = "GET_OPEN_ID_CODE_ERR";

    /**
     * 用户名或密码错误
     */
    public static final String USER_OR_PASSWORD_WRONG = "USER_OR_PASSWORD_WRONG";
    /**
     * UPLOAD_TYPE_NOT_EXIST = 不存在此上传类型
     */
    public static final String UPLOAD_TYPE_NOT_EXIST = "UPLOAD_TYPE_NOT_EXIST";


    public static final String REPLACE_LOGIN = "REPLACE_LOGIN";

    /**
    * 条形码异常
    */
    public static final String CODE_ERR = "CODE_ERR";

}
