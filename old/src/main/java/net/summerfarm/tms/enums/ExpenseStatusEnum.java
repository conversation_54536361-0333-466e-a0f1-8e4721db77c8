package net.summerfarm.tms.enums;

import java.util.Objects;

/**
 * @Classname ExpenseEnum
 * @Description 费用报销枚举.
 * @Date 2022/2/18 17:01
 * @Created by hx
 */
public enum ExpenseStatusEnum {


    AUDIT(0, "待审核"),
    COMPLETE(1, "审核成功"),
    FAIL(2, "审核失败");

    public static String getById(Integer id) {
        if (Objects.isNull(id)) {
            return null;
        }
        for (ExpenseStatusEnum expenseStatusEnum : values()) {
            if (expenseStatusEnum.getId().equals(id) ) {
                return expenseStatusEnum.getMsg();
            }
        }
        return null;
    }

    private Integer id;

    private String msg;

    ExpenseStatusEnum(Integer id, String msg) {
        this.id = id;
        this.msg = msg;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

}
