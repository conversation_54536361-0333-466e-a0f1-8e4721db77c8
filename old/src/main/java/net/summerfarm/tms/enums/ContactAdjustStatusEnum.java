package net.summerfarm.tms.enums;

/**
 * <AUTHOR> ct
 * create at:  2021/6/28  10:56
 */
public enum  ContactAdjustStatusEnum {

    WAIT_HANDLE(0,"待审核"),
    SUCCESS(1,"成功"),
    RE_COMMIT(2,"重新提交"),
    FAIL(3,"失败");


    private int status;

    private String state;

    ContactAdjustStatusEnum(int status, String state) {
        this.status = status;
        this.state = state;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
