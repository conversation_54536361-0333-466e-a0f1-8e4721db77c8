package net.summerfarm.tms.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/8/5 17:28<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PunchVO implements Serializable {

    /**
     * 0 不需要打卡  1需要打卡 2 已打卡
     */
    private Integer state;
    /**
     * 打卡距离
     */
    private BigDecimal punchDistance;
    /**
     * 实际打卡距离
     */
    private BigDecimal realPunchDistance;
    /**
     * 打卡时间
     */
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime punchTime;
    /**
     * 司机打卡地址
     */
    private String punchAddress;
    /**
     * 司机打卡地址Poi
     */
    private String punchAddressPoi;
    /**
     * 城配仓poi
     */
    private String poi;
    /**
     * 需要打卡的id
     */
    private Long deliverySiteId;
}
