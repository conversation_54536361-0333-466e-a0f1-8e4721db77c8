package net.summerfarm.tms.domain;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.common.util.validation.groups.Update;
import net.summerfarm.tms.common.util.DateUtils;
import net.summerfarm.tms.delivery.group.ValidationGroups;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2020/7/15  11:22
 */
@Data
public class DeliveryPath {
    @NotNull(groups = {ValidationGroups.UpdateSiteBatch.class},message = "id.not.null")
    private Integer id;

    @ApiModelProperty("仓库编号")
    private Integer storeNo;

    @ApiModelProperty("配送时间")
    @DateTimeFormat(pattern = DateUtils.DEFAULT_DATE_FORMAT)
    private LocalDate deliveryTime;

    @ApiModelProperty("配送地址id")
    private Long contactId;

    @ApiModelProperty("精准送")
    private String timeFrame;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("总体积")
    private BigDecimal totalVolume;

    @NotNull(groups = {ValidationGroups.UpdateSiteBatch.class},message = "path.not.null")
    @ApiModelProperty("路线")
    private String path;

    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("添加时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime addtime;

    @ApiModelProperty("总货值")
    private BigDecimal totalPrice;

    @ApiModelProperty("是否含有式样sku")
    private Integer type;

    @ApiModelProperty("状态")
    private Integer pathStatus;

    @ApiModelProperty("完成配送poi")
    private String finishPoi;

    @ApiModelProperty("完成配送地点名称")
    private String finishPoiName;

    @ApiModelProperty("完成配送图片")
    private String deliveryPic;

    @ApiModelProperty("完成配送地点poi与地址poi的距离")
    private BigDecimal finishDistance;

    @ApiModelProperty("是否是正常签收 0 正常 1 不正常")
    private Integer signForStatus;

    @ApiModelProperty("签收备注")
    private String signForRemarks;

    @ApiModelProperty("完成配送时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    @ApiModelProperty("拦截类型 0正常 1部分拦截 2全部拦截")
    private  Integer interceptType;

    @ApiModelProperty("品牌类型 0内部，1外部")
    private Integer brandType;

    @ApiModelProperty("是否超出距离 0 正常 1超出")
    private Integer outDistance;

    @ApiModelProperty("超出距离备注")
    private String outRemark;

    @ApiModelProperty("配送类型 配送 回收 配送/回收")
    private  Integer deliveryType;
}
