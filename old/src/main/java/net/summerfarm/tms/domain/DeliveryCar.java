package net.summerfarm.tms.domain;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * delivery_car
 * <AUTHOR>
@Data
public class DeliveryCar implements Serializable {
    private Integer id;

    /**
     * 一级城市区域
     */
    private Integer no;

    /**
     * 区域
     */
    private Integer areaNo;

    /**
     * 编号
     */
    private String number;

    /**
     * 车牌号
     */
    private String plateNumber;

    /**
     * 司机
     */
    private String driver;

    /**
     * 电话
     */
    private String phone;

    /**
     * 图片 ; 分割
     */
    private String pic;

    /**
     * 状态：0、冻结 1、正常（默认）
     */
    private Integer status;

    /**
     * 驾驶证照片
     */
    private String driverPic;

    /**
     * 行驶证照片
     */
    private String drivingPic;

    /**
     * 仓名
     */
    private String storeName;

    /**
     * 城市名称
     */
    private String areaName;

    /**
     * 账号类型
     */
    private Integer accountType;

    /**
     * 密码
     */
    private String passwd;

    /**
     * 小程序id
     */
    private String mpOpenid;

    /**
    * 账号名称
    */
    private String accountName;

    private String volume;

    private Float loadVolume;

    /**
     * 车型
     * 0、小型面包车
     * 1、中型面包车
     * 2、依维柯
     * 3、小型货车
     * 4、4米2
     * 5、5米2
     **/
    private Integer model;


    /**
     * 承运商ID
     */
    private Long carrierId;

    private static final long serialVersionUID = 1L;
}