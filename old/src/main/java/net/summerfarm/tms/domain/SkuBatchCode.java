package net.summerfarm.tms.domain;

import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> ct
 * create at:  2021/12/10  15:51
 * sku 批次 生产日期 唯一码
 */
@Data
public class SkuBatchCode implements Serializable {

    private Integer id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * sku
     */
    private String sku;

    /**
     * 批次
     */
    private String batch;

    /**
     * 生产日期
     */
    private LocalDate productionDate;

    /**
     * 保质期
     */
    private LocalDate qualityDate;

    /**
     * 唯一码 批次唯一码 根据id生成前方补0 共10位 1位分隔符 "S"
     */
    private String skuBatchOnlyCode;

    /**
     * 打印次数
     */
    private Integer printNumber;

    public SkuBatchCode(){}

    public SkuBatchCode(String sku,String batch,LocalDate productionDate){
        this.sku = sku;
        this.batch = batch;
        this.productionDate = productionDate;
    }


}
