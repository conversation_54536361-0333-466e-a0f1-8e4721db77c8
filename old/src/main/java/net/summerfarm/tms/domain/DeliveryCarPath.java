package net.summerfarm.tms.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * delivery_car_path
 * <AUTHOR>
@Data
public class DeliveryCarPath implements Serializable {
    private Integer id;

    /**
     * 配送车辆
     */
    private Integer deliveryCarId;

    /**
     * 仓库编号
     */
    private Integer storeNo;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 配送路线
     */
    private String path;

    private LocalDateTime addtime;

    /**
     * 总距离
     */
    private BigDecimal totalDistance;

    /**
     * 路线名称
     */
    private String pathName;

    /**
     * 排线次数
     */
    private Integer type;

    /**
     * 实际总距离
     */
    private BigDecimal realTotalDistance;

    private static final long serialVersionUID = 1L;
}