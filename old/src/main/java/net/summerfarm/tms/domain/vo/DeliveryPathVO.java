package net.summerfarm.tms.domain.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.cosfo.summerfarm.model.dto.order.OrderItemVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.summerfarm.tms.domain.DeliveryPath;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> ct
 * create at:  2020/7/15  14:14
 */
@Data
public class DeliveryPathVO extends DeliveryPath {

    @ApiModelProperty("店铺名称")
    private String mName;

    @ApiModelProperty("配送地址")
    private String address;

    @ApiModelProperty("配送地址手机号")
    private String phone;

    @ApiModelProperty("bd名称")
    private String bdName;

    @ApiModelProperty("bd手机号")
    private String bdPhone;

    @ApiModelProperty("订单编号 多个,分割")
    private String orderNo;

    @ApiModelProperty("地址poi")
    private String poiNote;

    @ApiModelProperty("大客户名称")
    private String adminName;

    @ApiModelProperty("钉钉登陆userId")
    private String userId;

    @ApiModelProperty("用户名id")
    private Integer mId;

    @ApiModelProperty("完成配送时间")
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishDeliveryTime;

    @ApiModelProperty("配送类型")
    private Integer deliveryType;

    @ApiModelProperty("城市编号")
    private Integer areaNo;

    @ApiModelProperty(value = "大客户名称备注")
    private String nameRemakes;

    @ApiModelProperty(value = "sku类型 0 自营 1 代仓")
    private Integer skuType;

    @ApiModelProperty("地址状态")
    private Integer contactStatus;

    @ApiModelProperty("报销单id")
    private Integer expenseId;

    @ApiModelProperty("报销单状态")
    private Integer expenseState;

    @ApiModelProperty("扫码信息")
    private List<OrderItemVO> scanOrderItemList;

    @ApiModelProperty("不扫码信息 不包含回收商品信心")
    private List<OrderItemVO> noScanOrderItemList;

    @ApiModelProperty("获取所有的条形码信息")
    private String codeList;

    @ApiModelProperty("审核")
    private Integer status;

    /**
    * 是否有完成扫码
    */
    private Boolean finishScanCode;

}
