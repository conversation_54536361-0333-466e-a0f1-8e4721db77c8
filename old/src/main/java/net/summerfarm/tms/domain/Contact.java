package net.summerfarm.tms.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> ct
 * create at:  2020/7/15  15:51
 */
@Data
public class Contact {

    @ApiModelProperty(value = "联系人id")
    private Long contactId;

    @ApiModelProperty(value = "商户id")
    private Long mId;

    @ApiModelProperty(value = "联系人")
    private String contact;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "性别")
    private Boolean gender;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信号")
    private String weixincode;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区域")
    private String area;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "状态(1正常或审核通过、2删除、3待审核、4审核不通过)")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "配送车辆")
    private String deliveryCar;

    @ApiModelProperty(value = "1默认地址 与merchat中一致")
    private Integer isDefault;

    @ApiModelProperty(value = "经纬度")
    private String poiNote;

    @ApiModelProperty(value = "到仓库距离")
    private BigDecimal distance;

    @ApiModelProperty(value = "预排路线")
    private String path;

    /** 商家名 */
    private String mname;

    /**
     * 城配仓
     **/
    private Integer storeNo;
}
