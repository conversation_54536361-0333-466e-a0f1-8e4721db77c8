package net.summerfarm.tms.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * tms_expense_audit_record
 * <AUTHOR>
@Data
public class TmsExpenseAuditRecord implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private LocalDateTime createTime;

    /**
     * update time
     */
    private LocalDateTime updateTime;

    /**
     * 店铺名称
     */
    private String mname;

    /**
     * 审核人
     */
    private String auditName;

    /**
     * 审核状态
     */
    private Integer status;

    /**
     * 司机id
     */
    private Integer driverId;

    /**
     * 配送路线id
     */
    private Integer deliveryPathId;

    /**
     * 费用报销id
     */
    private Integer expenseId;

    /**
     * 提交时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;
    /**
     * 原因
     */
    private String reason;
    /**
     * 地址
     */
    private String address;

    private Long deliverySiteId;

    private static final long serialVersionUID = 1L;
}