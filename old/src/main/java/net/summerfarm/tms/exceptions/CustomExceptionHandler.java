package net.summerfarm.tms.exceptions;

import net.summerfarm.tms.common.AjaxResult;
import net.summerfarm.tms.common.util.StringUtils;
import net.summerfarm.tms.contexts.ResultConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Package: com.manageSystem.common.exceptions
 * @Description: 自定义异常处理类
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
/*@RestControllerAdvice*/
public class CustomExceptionHandler {

    private static Logger logger = LoggerFactory.getLogger(CustomExceptionHandler.class);

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public AjaxResult methodArgumentNotValidExceptionHandle(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();

        String info = fieldErrors.stream().map(FieldError::getDefaultMessage).collect(Collectors.joining(","));

        return AjaxResult.getErrorWithMsg(info);
    }

    @ExceptionHandler(Exception.class)
    public AjaxResult handle(Exception e) {
        e.printStackTrace();
        logger.info("【异常】:", e);
        AjaxResult result = AjaxResult.getError(ResultConstant.DEFAULT_FAILED);

        if (e instanceof DefaultServiceException) {
            Object[] params = ((DefaultServiceException) e).getParams();
            if (StringUtils.isBlank(params)) {
                //兼容模式如果e.getMessage()包含中文判断使用哪种构造函数
                //若e.getMessage()字节码长度等于本身长度则不包含中文
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().getBytes().length != e.getMessage().length()) {
                    result = AjaxResult.getErrorWithMsg(e.getMessage());
                } else {
                    result = AjaxResult.getError(e.getMessage());
                }
            } else {
                result = AjaxResult.getErrorWithParam(e.getMessage(), params);
            }
        }
        if (e instanceof net.summerfarm.common.exceptions.DefaultServiceException) {
            Object[] params = ((net.summerfarm.common.exceptions.DefaultServiceException) e).getParams();
            if (StringUtils.isBlank(params)) {
                //兼容模式如果e.getMessage()包含中文判断使用哪种构造函数
                //若e.getMessage()字节码长度等于本身长度则不包含中文
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().getBytes().length != e.getMessage().length()) {
                    result = AjaxResult.getErrorWithMsg(e.getMessage());
                } else {
                    result = AjaxResult.getError(e.getMessage());
                }
            } else {
                result = AjaxResult.getErrorWithParam(e.getMessage(), params);
            }
        }
        return result;
    }


}
