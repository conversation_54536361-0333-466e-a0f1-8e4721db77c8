package net.summerfarm.tms.exceptions;

/**
 * @Package: com.manageSystem.common.exceptions
 * @Description: 自定义异常类
 * @author: <EMAIL>
 * @Date: 2016/8/15
 */
public class DefaultServiceException extends RuntimeException {

    private String[] params;

    private int level = 0;

    public DefaultServiceException() {
    }

    public DefaultServiceException(String message) {
        super(message);
    }

    public DefaultServiceException(int level) {
        this.level = level;
    }

    public DefaultServiceException(int level, String message) {
        super(message);
        this.level = level;
    }

    public DefaultServiceException(int level, String message, String... param) {
        super(message);
        this.params = param;
        this.level = level;
    }

    public DefaultServiceException(Throwable cause) {
        super(cause);
    }

    public DefaultServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    public String[] getParams() {
        return params;
    }

    public int getLevel() {
        return level;
    }
}
