package net.summerfarm.tms.mapper;

import net.summerfarm.tms.domain.TmsOutDistanceConfig;
import org.apache.ibatis.annotations.Param;

public interface TmsOutDistanceConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TmsOutDistanceConfig record);

    int insertSelective(TmsOutDistanceConfig record);

    TmsOutDistanceConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TmsOutDistanceConfig record);

    int updateByPrimaryKey(TmsOutDistanceConfig record);

    TmsOutDistanceConfig selectByStoreNo(@Param("storeNo") Integer storeNo);
}