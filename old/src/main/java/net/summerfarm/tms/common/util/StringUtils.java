package net.summerfarm.tms.common.util;


import net.summerfarm.tms.contexts.RegConstant;

import java.util.*;
import java.util.regex.Pattern;

/**
 * @Package: net.summerfarm.common.utils
 * @Description: String工具类
 * @author: <EMAIL>
 * @Date: 2016/7/24
 */
public class StringUtils extends org.springframework.util.StringUtils {

    private static final String SEPARATING_SYMBOL = ",";

    /**
     * 检查email是否是邮箱格式，返回true表示是，反之为否
     *
     * @param email
     * @return
     */
    public static boolean isEmail(String email) {
        if (isEmpty(email)) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegConstant.EMAIL_REG);
        return pattern.matcher(email).matches();
    }

    /**
     * 检验手机号码格式
     *
     * @param mobiles
     * @return
     */
    public static boolean isMobile(String mobiles) {
        if (isEmpty(mobiles)) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegConstant.PHONE_REG);
        return pattern.matcher(mobiles).matches();
    }

    /**
     * 检查身份证的格式，返回true表示是，反之为否
     *
     * @param idcard
     * @return
     */
    public static boolean isCard(String idcard) {
        if (isEmpty(idcard)) {
            return false;
        }
        //身份证正则表达式(15位)
        Pattern pattern15 = Pattern.compile(RegConstant.FIFTEEN_IDENTITY_CARD_REG);
        //身份证正则表达式(18位)
        Pattern pattern18 = Pattern.compile(RegConstant.EIGHTEEN_IDENTITY_CARD_REG);
        return pattern15.matcher(idcard).matches() || pattern18.matcher(idcard).matches();
    }

    /**
     * 检查真实姓名的格式
     *
     * @param realName
     * @return
     */
    public static boolean isRealName(String realName) {
        if (isEmpty(realName)) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegConstant.REALNAME_REG);
        return pattern.matcher(realName).matches();
    }

    /**
     * 检验商户名格式
     *
     * @param mname
     * @return
     */
    public static boolean isMname(String mname) {
        if (isEmpty(mname)) {
            return false;
        }
        return mname.matches(RegConstant.MNAME_REG);
    }

    /**
     * 效验后台密码格式:数字+字母+特殊字符
     *
     * @param password
     * @return
     */
    public static boolean isAdminPassword(String password) {
        if (isEmpty(password)) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegConstant.BACKSTAGE_PASSWORD_REG);
        return pattern.matcher(password).matches();
    }

    /**
     * 效验是否网址url
     *
     * @param url
     * @return
     */
    public static boolean isUrl(String url) {
        if (isEmpty(url)) {
            return false;
        }
        Pattern pattern = Pattern.compile(RegConstant.URL_REG);
        return pattern.matcher(url).matches();
    }

    /**
     * 判断字符串是否为整数
     *
     * @param str
     * @return
     */
    public static boolean isInteger(String str) {
        if (isEmpty(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("\\d*");
        return pattern.matcher(str).matches();
    }

    /**
     * 判断字符串是否为数字
     *
     * @param str
     * @return
     */
    public static boolean isNumber(String str) {
        if (isEmpty(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("\\d*(.\\d*)?");
        return pattern.matcher(str).matches();
    }

    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * 一次性判断多个或单个对象为空。
     *
     * @param objects
     * @return 只要有一个元素为Blank，则返回true
     * <AUTHOR>
     */
    public static boolean isBlank(Object... objects) {
        boolean result = false;
        if (null == objects) {
            return true;
        }
        for (Object object : objects) {
            if (null == object || "".equals(object.toString().trim())
                    || "null".equals(object.toString().trim())) {
                result = true;
                break;
            }
        }
        return result;
    }

    /**
     * 把数组的空数据去掉
     *
     * @param array
     * @return
     */
    public static List<String> array2Empty(String[] array) {
        List<String> list = new ArrayList<String>();
        for (String string : array) {
            if (StringUtils.isNotBlank(string)) {
                list.add(string);
            }
        }
        return list;
    }

    /**
     * 把数组转换成set
     *
     * @param array
     * @return
     */
    public static Set<?> array2Set(Object[] array) {
        Set<Object> set = new TreeSet<>();
        for (Object id : array) {
            if (null != id) {
                set.add(id);
            }
        }
        return set;
    }

    /**
     * 首字母大写
     *
     * @param s
     * @return
     */
    public static String firstCharUpperCase(String s) {
        StringBuffer sb = new StringBuffer(s.substring(0, 1).toUpperCase());
        sb.append(s.substring(1));
        return sb.toString();
    }

    /**
     * 将带,分割符的字符串转为List
     * 此处用Arrays.asList()方法转换时，后前要是调用removeAll()方法时，会出现UnsupportedOperationException。
     *
     * @param s
     * @return
     */
    public static Set<String> split(String s) {
        if (s == null || s.length() < 1) {
            return null;
        }
        Set<String> sCollection = new HashSet();
        Collections.addAll(sCollection, s.split(SEPARATING_SYMBOL));
        return sCollection;
    }

    public static final String orderRandomNum() {
        Random random = new Random();
        int rs = random.nextInt(99);
        return rs < 10 ? "0" + rs : rs + "";
    }

    public static String getRandomNumber(int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(9));
        }
        return sb.toString();
    }

    /**
     * 数字转换成对应位数的str
     *
     * @param in
     * @param aimLength
     * @return
     */
    public static String strFormat(String in, int aimLength) {
        return in.length() < aimLength ? "0" + strFormat(in, aimLength - 1) : in;
    }

    /**
     * 数字转中文数字
     *
     * @param src
     * @return
     */
    public static String int2chineseNum(int src) {
        final String[] num = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
        final String[] unit = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千"};
        String dst = "";
        int count = 0;
        while (src > 0) {
            dst = (num[src % 10] + unit[count]) + dst;
            src = src / 10;
            count++;
        }
        return dst.replaceAll("零[千百十]", "零").replaceAll("零+万", "万")
                .replaceAll("零+亿", "亿").replaceAll("亿万", "亿零")
                .replaceAll("零+", "零").replaceAll("零$", "");
    }

    /**
     * String转int集合
     * @param s 字符串
     * @param split 分隔符
     * @return 集合
     */
    public static List<Integer> stringToIntList(String s,  String split) {
        List<Integer> list = new ArrayList<>();
        if (isBlank(s)) {
            return null;
        }
        String[] strings = s.split(split);
        for (String i : strings) {
            list.add(Integer.parseInt(i));
        }
        return list;
    }

    /**
     * String转long集合
     * @param s 字符串
     * @param split 分隔符
     * @return 集合
     */
    public static List<Long> stringToLongList(String s,  String split) {
        List<Long> list = new ArrayList<>();
        if (isBlank(s)) {
            return null;
        }
        String[] strings = s.split(split);
        for (String i : strings) {
            list.add(Long.parseLong(i));
        }
        return list;
    }
}
