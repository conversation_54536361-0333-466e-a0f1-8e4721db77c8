package net.summerfarm.tms.common.util.qiNiu;

import com.qiniu.common.QiniuException;
import com.qiniu.common.Zone;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import net.summerfarm.tms.contexts.QiNiuConstant;
import net.summerfarm.tms.enums.QiNiuUploadEnum;


import java.util.HashMap;
import java.util.Map;


/**
 * @Package: net.summerfarm.common.utils.qiNiu
 * @Description: 获取七牛Token工具类
 * @author: <EMAIL>
 * @Date: 2016/7/21
 */
public class UploadTokenFactory {

    /**
     * 生成token
     * @param type
     * @param fileName
     * @param expires
     * @return
     */
    public static Map<String, String> createToken(String type, String fileName, long expires){
        Map<String, String> result = new HashMap<>();
        Auth auth = Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        //判断是否存在此类型
        QiNiuUploadEnum typeEnum = QiNiuUploadEnum.valueOf(type.toUpperCase());
        if (typeEnum == null) {
            return null;
        }
//        String key = createKey(typeEnum.getCode(),fileName);
        String key = fileName;
        String token = auth.uploadToken(QiNiuConstant.DEFAULT_BUCKET, key, expires, null);
        result.put("token", token);
        result.put("key", key);
        return result;
    }

    public static Map<String, String> createToken(String fileName, long expires){
        Map<String, String> result = new HashMap<>();
        Auth auth = Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        String key = fileName;
        String token = auth.uploadToken(QiNiuConstant.DEFAULT_BUCKET, key, expires, null);
        result.put("token", token);
        result.put("key", key);
        return result;
    }
    /**
     * 文件保存名称
     * 根据实际业务需求生成filename参数
     * @param type
     * @param fileName
     * @return
     */
    private static String createKey(String type, String fileName){
        return type+fileName;
    }


    public static void deleteKey(String fileName){
        com.qiniu.util.Auth auth = com.qiniu.util.Auth.create(QiNiuConstant.ACCESS_KEY, QiNiuConstant.SECRET_KEY);
        Configuration config = new Configuration(Zone.autoZone());
        BucketManager bucketMgr = new BucketManager(auth, config);

        //指定需要删除的文件，和文件所在的存储空间
        String bucketName = QiNiuConstant.DEFAULT_BUCKET;
        try {
            //当前为7.2.1；  7.2.2后才能传多个key ，即：第二个参数为数组 (String... deleteTargets)
            bucketMgr.delete(bucketName, fileName);
        } catch (QiniuException e) {
            e.printStackTrace();
        }
    }
}
