package net.summerfarm.tms.common.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.summerfarm.common.util.RequestHolder;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.client.dto.ShiroUser;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.InputStreamSource;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Package: net.summerfarm.common.aspect
 * @Description:
 * @author: <EMAIL>
 * @Date: 2017/8/17
 */

@Aspect
@Component
public class OperateAspect {
    private static final Logger logger = LoggerFactory.getLogger(OperateAspect.class);

    private static final Logger resCodeLog = LoggerFactory.getLogger("resCodeLog");

    @Before(value = "net.summerfarm.tms.common.aspect.OperateAspect.PointCut.systemLogPointCut()")
    public void before(JoinPoint joinPoint) {
        //获取方法名
        String methodName = joinPoint.getSignature().getName();
        String clazz = joinPoint.getTarget().getClass().getName();
        Object[] args = joinPoint.getArgs();

        if ("login".equalsIgnoreCase(methodName)) {
            logger.info("管理员{}登录了",args[0]);
            return;
        }

        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        String adminName ;
        if (user== null){
            adminName = "未知";
        }else {
            adminName = user.getPhone();
        }
        //判断参数
        if(args == null){//没有参数
            logger.info("管理员：{},于{},访问了{}接口",adminName, LocalDateTime.now(),methodName);
            return ;
        }
        List<Object>  objects=new ArrayList<>();
        for (Object obj: args){
            if(obj !=null){
                if (!(obj instanceof BeanPropertyBindingResult || "org.apache.catalina.connector.ResponseFacade".equals(obj.getClass().getName()))){
                    objects.add(obj);
                }
            }
        }

        objects = objects.stream().filter(el -> !(el instanceof MultipartFile || el instanceof HttpServletResponse)).collect(Collectors.toList());
        logger.info("管理员：{},于{},访问了{},{}接口,参数为:{}",adminName, LocalDateTime.now(),clazz,methodName,JSON.toJSONString(objects));

    }

    @AfterReturning(value = "within(net.summerfarm.tms.controller.*)", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            HttpServletRequest request = RequestHolder.getRequest();
            String tab = request.getHeader("tab");
            String part = request.getHeader("part");
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();

            String paraStr = "";
            if (joinPoint.getArgs().length != 0) {
                List<Object> logArgs = Arrays.stream(joinPoint.getArgs())
                        .filter(arg -> !(arg instanceof HttpServletRequest))
                        .filter(arg -> !(arg instanceof HttpServletResponse))
                        .filter(arg -> !(arg instanceof BeanPropertyBindingResult))
                        .map(arg -> {
                            if(arg instanceof InputStreamSource){
                               return "文件流";
                            }
                            return arg;
                        })
                        .collect(Collectors.toList());
                paraStr = JSON.toJSONString(logArgs);
            } else if (request.getParameterMap().size() != 0) {
                paraStr = JSON.toJSONString(request.getParameterMap());
            }

            String resultStr = JSON.toJSONString(result);

            try {
                if (StringUtils.isNotBlank(resultStr)) {
                    resultStr = resultStr.trim();
                    if (resultStr.startsWith("{") && resultStr.endsWith("}")) {
                        JSONObject param = JSONObject.parseObject(resultStr);
                        String code = param.getString("code");
                        String msg = param.getString("msg");
                        resCodeLog.info("{}|{}|{}|{}|{}", user.getPhone(), request.getRequestURI(), request.getMethod(), code, msg);
                    }
                }
            } catch (Exception e) {
            }

        } catch (Exception e) {
            logger.warn("数据埋点日志处理异常", e);
            e.printStackTrace();
        }
    }

    /**
     * 使用Java反射来获取被拦截方法(insert、update)的参数值，
     * 将参数值拼接为操作内容
     * @param args
     * @param mName
     * @return
     */
    public String optionContent(Object[] args, String mName){
        if(args == null){
            return null;
        }
        StringBuffer rs = new StringBuffer();
        rs.append(mName);
        String className = null;
        int index = 1;
        //遍历参数对象
        for(Object info : args){
            //获取对象类型
            className = info.getClass().getName();
            className = className.substring(className.lastIndexOf(".") + 1);
            rs.append("[参数"+index+"，类型:" + className + "，值:");
            //获取对象的所有方法
            Method[] methods = info.getClass().getDeclaredMethods();
            // 遍历方法，判断get方法
            for(Method method : methods){
                String methodName = method.getName();
                // 判断是不是get方法
                if(methodName.indexOf("get") == -1){//不是get方法
                    continue;//不处理
                }
                Object rsValue = null;
                try{
                    // 调用get方法，获取返回值
                    rsValue = method.invoke(info);
                }catch (Exception e) {
                    continue;
                }
                //将值加入内容中
                rs.append("(" + methodName+ ":" + rsValue + ")");
            }
            rs.append("]");
            index ++;
        }
        return rs.toString();
    }



    class PointCut{

        @Pointcut(value = "within(net.summerfarm.tms.controller..*)")
        public void systemLogPointCut() {

        }
    }
}
