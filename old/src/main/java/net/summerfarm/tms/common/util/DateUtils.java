package net.summerfarm.tms.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * @Package: net.summerfarm.common.utils
 * @Description: 日期工具类
 * @author: <EMAIL>
 * @Date: 2016/8/30
 */
public class DateUtils {

    private static Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public static final String YEAR = "YEAR";

    public static final String MONTH = "MONTH";

    public static final String DAY = "DAY";

    public static final String HOUR = "HOUR";

    public static final String SHORT_DATE_FORMAT = "yyyy-MM";

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    public static final String MIDDLE_DATE_FORMAT = "yyyy-MM-dd HH:mm";

    public static final String HOUR_DATE_FORMAT = "yyyy-MM-dd HH";

    public static final String LONG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String NONE_DATE_FORMAT = "00-00-00 00:00:00";

    public static final String QUARTZ_DATE_FORMAT = " ss mm HH dd MM ? yyyy ";

    public static final String NUMBER_DATE_FORMAT = "yyyyMMddHHmmss";

    //一天毫秒数
    public static final Long ONE_DATE_TIME = 86400000L;

    public  static  final  String[] WEEK = {"星期一","星期二","星期三","星期四","星期五","星期六","星期日"};

    /**
     * Date转String (默认日期格式)
     *
     * @param date
     * @return
     */
    public static String date2String(Date date) {
        return date2String(date, DEFAULT_DATE_FORMAT);
    }

    /**
     * 按指定格式 Date转String
     *
     * @param date
     * @param dateFormat
     * @return
     */
    public static String date2String(Date date, String dateFormat) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf;
        if (dateFormat != null && !dateFormat.equals("")) {
            try {
                sdf = new SimpleDateFormat(dateFormat);
            } catch (Exception e) {
                logger.error("异常：", e);
                sdf = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
            }
        } else {
            sdf = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        }
        return sdf.format(date);
    }

    public static Date string2Date(String date, String dateFormat) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        if (StringUtils.isNotBlank(dateFormat)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
                return sdf.parse(date);
            } catch (Exception e) {
                logger.error("异常：", e);
            }
        }
        return null;

    }


    /**
     * Date转LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate date2LocalDate(Date date) {
        return LocalDate.parse(date2String(date), DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT));
    }

    /**
     * localDateTime2Date
     *
     * @param localDateTime
     * @return
     */
    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * localDate2Date
     *
     * @param localDate
     * @return
     */
    public static Date localDate2Date(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * Date转LocalDateTime
     * @param date
     * @return
     */
    public static LocalDateTime date2LocalDateTime(Date date){
        return LocalDateTime.parse(date2String(date,LONG_DATE_FORMAT), DateTimeFormatter.ofPattern(LONG_DATE_FORMAT));
    }

    /**
     * 截单时间
     *
     * @param
     * @return
     */
    public static Date localDateForTime (Integer date) {
        long l = System.currentTimeMillis();
        long c = l - ONE_DATE_TIME * date;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(c);
        Date time = calendar.getTime();
        return time;
    }

    public static LocalDateTime getDayStart(LocalDateTime time) {
        return time.withHour(0)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);
    }

    //获取一天的结束时间，2017,7,22 23:59:59.999999999
    public static LocalDateTime getDayEnd(LocalDateTime time) {
        return time.withHour(23)
                .withMinute(59)
                .withSecond(59)
                .withNano(999999999);
    }


    public static String getStringWeek(LocalDate localDate){
        int value = localDate.getDayOfWeek().getValue();
        return WEEK[value-1];
    }

    /**
    * 计算两个星期时间的差值 例如 周一和周三 差 2天
    */
    public static Integer getIntegerWeek(String startWeek){
        int week = 0;
        for (int i = 0; i < WEEK.length; i++) {
            if(Objects.equals(startWeek,WEEK[i])){
                week = i+1;
            }
        }
        return week ;
    }

    /**
     * 比较两个时间的小时分钟秒
     */
    public static Boolean compareToDateTime(LocalDateTime startTime ,LocalDateTime endTime){
        //转换成同一天比较
        LocalTime start = LocalTime.of(startTime.getHour(), startTime.getMinute(), startTime.getSecond());
        LocalTime end = LocalTime.of(endTime.getHour(), endTime.getMinute(), endTime.getSecond());
        return start.isBefore(end);
    }

    public static String localDateTimeToString(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return "";
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(LONG_DATE_FORMAT);
        return df.format(localDateTime);
    }

}
