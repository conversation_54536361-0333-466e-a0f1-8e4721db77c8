package net.summerfarm.tms.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import net.summerfarm.tms.common.util.Prompt;
import net.summerfarm.tms.contexts.ResultConstant;

import java.io.Serializable;
import java.text.MessageFormat;

/**
 * Created by holyq on 2016/7/9.
 */
@ApiModel(description = "统一返回result")
public class AjaxResult<T> implements Serializable {

    /**
     * 默认值KEY - 执行成功时ReturnContext的code
     */
    public static final String DEFAULT_SUCCESS = "SUCCESS";

    @ApiModelProperty(value = "响应状态码")
    private String code = DEFAULT_SUCCESS;

    @ApiModelProperty(value = "响应消息")
    private String msg = "";

    @ApiModelProperty(value = "data")
    private T data;

    public static boolean isSuccess(AjaxResult result) {
        if (DEFAULT_SUCCESS.equals(result.code)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取正确结果模板
     *
     * @param data 请求结果
     * @return AjaxResult
     */
    public static <T> AjaxResult<T> getOK(T data) {
        AjaxResult<T> result = new AjaxResult<>();
        result.setData(data);
        return result;
    }

    /**
     * 获取正确结果模板
     *
     * @return AjaxResult
     */
    public static AjaxResult getOK() {
        return getOK(null);
    }

    /**
     * 获取正确结果模板
     *
     * @param message 请求返回信息
     * @param obj     请求结果
     * @return AjaxResult
     */
    public static <T> AjaxResult getOK(String message, T obj) {
        AjaxResult result = new AjaxResult();
        result.setMsg(message);
        result.setData(obj);
        return result;
    }


    /**
     * 获取错误结果模板
     *
     * @param message 请求返回信息
     * @param data    请求结果
     * @return AjaxResult
     */
    public static <T> AjaxResult getError(String code, String message, T data) {
        AjaxResult result = new AjaxResult();
        result.setCode(code);
        result.setMsg(message);
        result.setData(data);
        return result;
    }

    public static AjaxResult getError(String code, String message) {
        AjaxResult result = new AjaxResult();
        result.setCode(code);
        result.setMsg(message);
        return result;
    }

    public static AjaxResult getError() {
        AjaxResult result = new AjaxResult();
        result.setCode(ResultConstant.DEFAULT_FAILED);
        result.setMsg(Prompt.getVal(ResultConstant.DEFAULT_FAILED));
        return result;
    }

    /**
     * 获取错误结果模板
     *
     * @return AjaxResult
     */
    public static AjaxResult getError(String code) {
        AjaxResult result = new AjaxResult();
        result.setCode(code);
        result.setMsg(Prompt.getVal(code));
        return result;
    }

    public static AjaxResult getErrorWithMsg(String msg) {
        AjaxResult result = new AjaxResult();
        result.setCode(ResultConstant.DEFAULT_FAILED);
        result.setMsg(msg);
        return result;
    }

    /**
     * 获取错误结果模板
     *
     * @param code
     * @param message
     * @return
     */
    public static AjaxResult getErrorWithParam(String code, Object... message) {
        AjaxResult result = new AjaxResult();
        result.setCode(code);
        result.setMsg(MessageFormat.format(Prompt.getVal(code), message));
        return result;
    }

    public static <T> AjaxResult getError(String code, T data) {
        AjaxResult result = new AjaxResult();
        result.setCode(code);
        result.setMsg(Prompt.getVal(code));
        result.setData(data);
        return result;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
