package net.summerfarm.tms.common.util;

import org.springframework.beans.BeansException;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 加载配置的提示语给前端
 *
 * @author: <EMAIL>
 * @Date: 2016/7/18
 */
@Component
public class Prompt {
    private static Map<String, String> ctxPropertiesMap = new HashMap<>();

    @PostConstruct
    public static void processProperties() throws BeansException {
        try {
            Properties props = PropertiesLoaderUtils.loadProperties(new EncodedResource(new ClassPathResource("locale/prompt_constants.properties"), StandardCharsets.UTF_8));
            for (Object key : props.keySet()) {
                String keyStr = key.toString();
                String value = props.getProperty(keyStr);
                ctxPropertiesMap.put(keyStr, value);
            }

            Properties validProps = PropertiesLoaderUtils.loadProperties(new EncodedResource(new ClassPathResource("locale/prompt_valida.properties"), StandardCharsets.UTF_8));
            for (Object key : validProps.keySet()) {
                String keyStr = key.toString();
                String value = validProps.getProperty(keyStr);
                ctxPropertiesMap.put(keyStr, value);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取配置文件里的内容
     *
     * @param name
     * @return
     */
    public static String getVal(String name) {
        if (!StringUtils.isEmpty(name) && ctxPropertiesMap.containsKey(name)) {
            return ctxPropertiesMap.get(name);
        } else {
            return "";
        }
    }
}
