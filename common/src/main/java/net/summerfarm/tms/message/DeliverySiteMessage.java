package net.summerfarm.tms.message;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.enums.DeliverySiteStatusEnum;
import net.summerfarm.tms.enums.DeliverySiteTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/11/2 11:39<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteMessage extends BaseObject {

    /**
     * 点位sku维度签收详情
     */
    private List<DeliverySiteItemMessage> deliverySiteItemMessages;

    /**
     * 联系人id
     */
    private String contactId;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 地址
     */
    private String address;
    /**
     * poi
     */
    private String poi;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 名称
     */
    private String name;
    /**
     * 点位类型
     *
     * @see net.summerfarm.tms.enums.TmsSiteTypeEnum
     */
    private Integer type;
    /**
     * 监管仓对应的库存仓
     */
    private Long superviseSiteId;

    /**
     * 到仓距离
     */
    private BigDecimal distance;

    /**
     * 客户名 mname
     */
    private String clientName;

    /**
     * primary key
     */
    private Long id;

    /**
     * 批次ID
     */
    private Long deliveryBatchId;

    /**
     * 运输点位名称
     */
    private String siteName;

    /**
     * 运输点位类型
     * @see DeliverySiteTypeEnum
     */
    private Integer siteType;

    /**
     * 点位在路线上的顺序 --sort
     */
    private Integer sequence;

    /**
     * 运输点位状态
     * @see DeliverySiteStatusEnum
     * 10未到站,20已到站,30已出发
     */
    private Integer status;

    /**
     * 计划到站时间 --deliveryTime
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planArriveTime;

    /**
     * 实际到达时间 --finishTime
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signInTime;

    /**
     * 实际到达地点
     */
    private String signInPoi;

    /**
     * 实际打卡和预计到达时间的差值
     */
    private Integer signInDiffMinute;

    /**
     * 实际打卡地和点位的差值
     */
    private BigDecimal signInDiffKm;

    /**
     * 签到打卡照片
     */
    private String signInPics;

    /**
     * 签到备注
     */
    private String signInRemark;

    /**
     * 超出距离备注
     */
    private String outReason;

    /**
     * 是否超出距离
     */
    private Integer outDistance;

    /**
     * 出发打卡时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime signOutTime;

    /**
     * 出发打卡拍照
     */
    private String signOutPics;

    /**
     * 出发打卡备注
     */
    private String signOutRemark;

    /**
     * 出发温度
     */
    private BigDecimal signOutTemperature;

    /**
     * 状态，0：正常，1：部分拦截，2：全部拦截
     */
    private Integer interceptState;

    /**
     * 外部客户名 mName
     */
    private String outerClientName;

    /**
     * 签到打卡距离
     */
    private BigDecimal signInDistance;

    /**
     * 是否正常签收，0：正常，1：不正常
     */
    private Integer signInStatus;

    /**
     * 外部客户号 mId
     */
    private String outerClientId;

    /**
     * 签发状态
     */
    private Integer signOutStatus;

    /**
     * 外部业务编号 -- storeNo
     */
    private String outBusinsessNo;

    /**
     * 配送站点扫码信息
     */
    private List<DeliverySiteItemCodeMessage> deliverySiteItemCodeMessages;

}
