package net.summerfarm.tms.message.out;

import lombok.Builder;
import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.time.LocalDateTime;

/**
 * Description: 干线委托单装填消息<br/>
 * date: 2024/4/28 15:14<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
public class TrunkDistOrderStateMessage extends BaseObject {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 外部单号
     */
    private String outerOrderId;

    /**
     * 来源，
     * 100：调拨，
     * 101：采购，
     * 102：销售出库，
     * 103，出样出库，
     * 104，补货出库，
     * 105，自提销售
     * 150，外单-干线
     * 151，外单-干配
     * 200：鲜沐商城，
     * 201：鲜沐售后
     * 202：鲜沐样品
     * 203：鲜沐商城省心送
     * 210：Saas商城
     * 211：Saas售后
     * 220：外单-城配
     */
    private Integer source;

    /**
     * 10:待排线 18:已关闭 19:取消 30:配送中 40:已完成
     */
    private Integer state;

    /**
     * 外部租户号
     */
    private String outerTenantId;

    /**
     * 外部品牌名
     */
    private String outerBrandName;

    /**
     * 外部客户号
     */
    private String outerClientId;

    /**
     * 外部客户名
     */
    private String outerClientName;

    /**
     * 外部联系人ID
     */
    private String outerContactId;

    /**
     * 期望开始时间
     */
    private LocalDateTime expectBeginTime;
}
