package net.summerfarm.tms.message;

import lombok.Data;

/**
 * Description: <br/>
 * date: 2022/11/2 11:51<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliverySiteItemMessage {

    /**
     * primary key
     */
    private Long id;

    /**
     * 运输单id
     */
    private Long deliverySiteId;

    /**
     * 外部条目id --sku
     */
    private String outItemId;

    /**
     * 计划签收数量
     */
    private Integer planReceiptCount;

    /**
     * 实际签收数量
     */
    private Integer realReceiptCount;

    /**
     * 缺货数量
     */
    private Integer shortCount;

    /**
     * 拦截数量
     */
    private Integer interceptCount;

    /**
     * 拒收数量
     */
    private Integer rejectCount;

    /**
     * 拒收原因
     */
    private String rejectReason;

    /**
     * 扫码数量
     */
    private Integer scanCount;

    /**
     * 无码数量
     */
    private Integer noscanCount;

    /**
     * 无码原因
     */
    private String noscanReason;

    /**
     * 无码货物照片
     */
    private String noscanPics;

    /**
     * 配送类型 0配送1回收
     */
    private Integer type;

    /**
     * 备注 --shortMessage
     */
    private String remark;

    /**
     * 商品名称
     */
    private String outItemName;

    /**
     * 配送/回收物品状态，0：正常，1：异常
     */
    private Integer status;

    /**
     * 物品回收详情消息
     */
    private DeliverySiteItemRecycleMessage deliverySiteItemRecycleMessage;
}
