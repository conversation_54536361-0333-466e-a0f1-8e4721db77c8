package net.summerfarm.tms.message;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;
import net.summerfarm.tms.enums.CarStorageEnum;
import net.summerfarm.tms.enums.CarTypeEnum;
import net.summerfarm.tms.enums.DeliveryBatchStatusEnum;
import net.summerfarm.tms.enums.DeliveryBatchTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/11/2 15:51<br/>
 *
 * <AUTHOR> />
 */
@Data
public class BatchMessage extends BaseObject {

    /**
     * 调度单主键
     */
    private Long id;
    /**
     * 调度单状态
     * @see DeliveryBatchStatusEnum
     * 10待排线、20待捡货、30配送中、40配送完成、50关闭
     */
    private Integer status;
    /**
     * 调度单类型
     * @see DeliveryBatchTypeEnum
     * -1干线城配用车、0干线用车、1调拨用车、2采购用车、3大客户用车、4仓库用车、5城配用车
     */
    private Integer type;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机联系电话
     */
    private String driverPhone;

    /**
     * 承运商ID
     */
    private Long carrierId;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 车牌号
     */
    private String carNumber;

    /**
     * 车储藏条件
     * @see CarStorageEnum
     */
    private String carStorage;

    /**
     * 车型
     * @see CarTypeEnum
     */
    private String carType;

    /**
     * 预估费用
     */
    private BigDecimal estimateFare;

    /**
     * 承运时间(开始)
     */
    private LocalDateTime beginTime;

    /**
     * 履约时间
     */
    private LocalDateTime deliveryTime;

    /**
     * 点位集合
     */
    private List<DeliverySiteMessage> deliverySiteMessages;

    /**
     * 区域
     */
    private String area;

    /**
     * 班次 0正常 1加班
     */
    private Integer classes;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预估总距离 km
     */
    private BigDecimal planTotalDistance;

    /**
     * 总重量
     */
    private BigDecimal totalWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 总数量
     */
    private Integer totalQuantity;

    /**
     * 路线编码
     */
    private String pathCode;
}
