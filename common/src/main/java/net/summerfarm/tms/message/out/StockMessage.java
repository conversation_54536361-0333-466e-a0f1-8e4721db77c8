package net.summerfarm.tms.message.out;

import lombok.Data;
import net.summerfarm.tms.base.BaseObject;

import java.util.List;

/**
 * Description: <br/>
 * date: 2022/11/10 16:44<br/>
 *
 * <AUTHOR> />
 */
@Data
public class StockMessage extends BaseObject {
    /**
     * 来源id
     */
    private String sourceId;

    /**
     * 入库任务类型
     */
    private Integer operationType;

    /**
     * 退货入库类型
     */
    private Integer taskType;

    /**
     * 城配仓
     */
    private Integer storeNo;
    /**
     * 店铺名称
     */
    private String merchantName;
    /**
     * sku操作信息
     */
    private List<StockItemInfoMessage> skuInfoList;
}
