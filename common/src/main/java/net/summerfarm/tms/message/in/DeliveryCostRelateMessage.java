package net.summerfarm.tms.message.in;

import lombok.Data;
import net.summerfarm.tms.enums.DistOrderSourceEnum;

import java.util.List;
import java.util.Map;

/**
 * Description:配送成本关系消息
 * date: 2023/2/14 14:03
 *
 * <AUTHOR>
 */
@Data
public class DeliveryCostRelateMessage {

    /**
     * 外部订单号
     */
    private String outerOrderId;

    /**
     * 委托单来源
     *
     * @see net.summerfarm.tms.enums.DistOrderSourceEnum
     */
    private DistOrderSourceEnum source;

    /**
     * 外部货品ID(sku)-外部成本批次ID关系
     */
    private Map<String, List<Long>> skuOutBatchIdMap;
}
