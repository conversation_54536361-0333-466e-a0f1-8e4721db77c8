package net.summerfarm.tms.message.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: 干线批次提货缺货消息<br/>
 * date: 2024/7/24 14:32<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrunkBatchPickUpShortGoodsMsg {
    /**
     * 调度单号
     */
    private Long deliveryBatchId;

    /**
     * SKU缺货信息
     */
    private List<TrunkBatchPickUpSkuShort> trunkPickUpSkuShorts;

    /**
     * 提货站点名称
     */
    private String siteName;

    /**
     * 调度单所有承运单关联单据信息
     */
    private List<TrunkBatchPickUpDistOrder> distOrders;

}
