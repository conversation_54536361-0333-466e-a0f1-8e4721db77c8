package net.summerfarm.tms.message.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * Description:计算批次装载率消息
 * date: 2023/8/4 16:01
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CalcBatchLoadRatioMessage implements Serializable {

    private static final long serialVersionUID = -3518365357945013182L;

    /**
     * 配送批次ID集合
     */
    private Collection<Long> deliveryBatchIds;
}
