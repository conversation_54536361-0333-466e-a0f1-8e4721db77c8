package net.summerfarm.tms.message.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.enums.DeliverySectionEnums;
import net.xianmu.gaode.support.service.input.WaypointsInput;

import java.io.Serializable;
import java.util.List;

/**
 * 计算距离请求对象参数
 *
 * <AUTHOR>
 * @Date 2023-06-02
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CalcTmsPathDistanceMessage implements Serializable {

	private static final long serialVersionUID = -3357030027036477441L;
	/**
	 * 路径信息
	 */
	private List<WaypointsInput> waypointsInputList;

	/**
	 * 批次id
	 */
	private Long batchId;

	/**
	 * 类型
	 */
	private DeliverySectionEnums.Type type;
}
