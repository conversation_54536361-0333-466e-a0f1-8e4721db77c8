package net.summerfarm.tms.message.in;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: 履约审核申诉创建消息<br/>
 * date: 2024/9/4 14:58<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryPerformanceReviewAppealCreateMessage {

    /**
     * 履约审核任务id
     */
    private Long performanceReviewTaskId;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    private Integer state;

}
