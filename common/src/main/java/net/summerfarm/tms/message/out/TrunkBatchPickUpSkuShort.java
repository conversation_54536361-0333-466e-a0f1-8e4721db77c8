package net.summerfarm.tms.message.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: 干线拣货缺货SKU信息<br/>
 * date: 2024/7/30 10:28<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrunkBatchPickUpSkuShort {

    /**
     * SKU
     */
    private String sku;

    /**
     * 缺货数量
     */
    private Integer shortQuantity;
}
