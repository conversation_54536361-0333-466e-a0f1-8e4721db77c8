package net.summerfarm.tms.message.out;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * Description: 城配仓完成排线通知<br/>
 * date: 2024/8/13 14:34<br/>
 *
 * <AUTHOR> />
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreCompletePathMessage {
    /**
     * 城配仓编号
     */
    private Integer storeNo;

    /**
     * 配送日期
     */
    private LocalDate deliveryTime;
}
