package net.summerfarm.tms.message;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:物品回收详情消息
 * date: 2023/12/15 18:55
 *
 * <AUTHOR>
 */
@Data
public class DeliverySiteItemRecycleMessage {

    /**
     * 主键
     */
    private Long id;

    /**
     * 配送点位物品ID
     */
    private Long deliverySiteItemId;

    /**
     * 外部货品ID(sku)
     */
    private String outItemId;

    /**
     * 回收照片
     */
    private String recyclePics;

    /**
     * 回收状态，0：正常，1：异常
     */
    private Integer status;

    /**
     * 回收状态描述
     */
    private String statusDesc;

    /**
     * 规格单位数量
     */
    private BigDecimal specificationQuantity;

    /**
     * 规格单位
     */
    private String specificationUnit;

    /**
     * 基础规格单位数量(小规格单位)
     */
    private BigDecimal basicSpecQuantity;

    /**
     * 基础规格单位(小规格单位)
     */
    private String basicSpecUnit;

    /**
     * 回收原因类型，1：回收数量不符，2：商品已损坏，3：店铺未开门下次回收，4：包装破损，5：客户已用不退了，6：其它
     */
    private Integer reasonType;

    /**
     * 回收原因类型描述
     */
    private String reasonTypeDesc;

    /**
     * 异常说明备注
     */
    private String remark;
}
