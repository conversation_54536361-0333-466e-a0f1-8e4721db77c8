package net.summerfarm.tms.query.delivery;

import com.alibaba.excel.util.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.summerfarm.tms.anno.CityStoreNo;
import net.summerfarm.tms.base.AbstractPageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 查询缺货核准请求参数
 *
 * <AUTHOR>
 * @Date 2023-03-14
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LackApprovedQuery extends AbstractPageQuery implements Serializable {
    private static final long serialVersionUID = -2663841687266980024L;

    /**
     * 任务编号
     */

    private Long id;
    /**
     * 城配仓
     */
    @CityStoreNo
    private Integer storeNo;
    /**
     * 库存仓
     */
    private Integer warehouseNo;


    /**
     * 城配仓
     */
    @CityStoreNo(needPerStoreNos = true)
    private List<Integer> storeNos;

    /**
     * 库存仓
     */
    private List<Integer> areaNos;

    /**
     * 任务编号集合
     */
    private List<Long> ids;


    /**
     * 状态 1 待核准 2待判责 3 已完成
     */
    private Integer state;

    /**
     * 配送开始时间
     */
    @DateTimeFormat(pattern = DateUtils.DATE_FORMAT_10)
    @NotNull(message = "配送查询开始时间不能为空")
    private Date startTime;

    /**
     * 配送结束时间
     */
    @DateTimeFormat(pattern = DateUtils.DATE_FORMAT_10)
    @NotNull(message = "配送查询结束时间不能为空")
    private Date endTime;

    /**
     * 店铺名称
     */
    private String mname;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String pdName;


    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它
     */
    private List<Integer> lackTypes;


    /**
     * 缺货类型1.总仓-少发;2.总仓-库存不足;3.总仓-发错货;4.司机-误操作;5.司机-配送丢失;6.干线运输破损;7.其它
     */
    private String lackType;

    /**
     * 是否申诉 0未申诉 1已申诉
     */
    private Integer appealFlag;

    /**
     * 不在缺货核准ID集合里面
     */
    private List<Long> idsNotIn;

}
