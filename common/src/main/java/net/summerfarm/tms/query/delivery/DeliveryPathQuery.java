package net.summerfarm.tms.query.delivery;

import net.summerfarm.tms.anno.CityStoreNo;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * Description: <br/>
 * date: 2022/10/18 10:20<br/>
 *
 * <AUTHOR> />
 */
@Data
public class DeliveryPathQuery {
    /**
     * 店铺名
     */
    private String mname;
    /**
     * 城配仓编号啊
     */
    @CityStoreNo
    private Integer storeNo;

    /**
     * 配送时间
     */
    private LocalDate deliveryTime;

    /**
     * 配送结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 配送开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 是否是查询页面，true是查询页面，false是排线页面
     */
    private boolean oldFlag;

    /**
     * 类型
     */
    private Integer deliveryBatchType;

    /**
     * 品牌客户地址id
     */
    private List<Integer> contactIds;

    /**
     * 品牌名称
     */
    private String outerBrandName;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 任务状态 0：正常 2：部分拦截 3：全部拦截 4拦截关闭
     */
    private Integer interceptState;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 0配送 1回收 2配送回收
     */
    private Integer deliveryType;

    /**
     * 配送方式 0正常配送 1专车配送
     */
    private Integer sendWay;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 10 待拣货 22.已配送 25配送中
     */
    private Integer status;

    /**
     * 10 待拣货 22.已配送 25配送中
     */
    private List<Integer> statusList;

    /**
     * 是否缺货 true缺货 false不缺
     */
    private Boolean lackFlag;
}
