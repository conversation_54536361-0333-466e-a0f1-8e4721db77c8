package net.summerfarm.tms.query.delivery;

import lombok.Data;
import net.summerfarm.tms.base.AbstractPageQuery;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/6/26 18:07<br/>
 *
 * <AUTHOR> />
 */
@Data
public class PerformanceReviewTaskQuery extends AbstractPageQuery {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 审核类型 0城配-出仓、1城配-签收、2干线-签收
     */
    private List<Integer> reviewTaskTypeList;

    /**
     * 状态0审核中、1审核完成、2已关闭
     */
    private List<Integer> stateList;

    /**
     * 创建开始时间
     */
    private LocalDateTime beginCreateTime;

    /**
     * 创建结束时间
     */
    private LocalDateTime endCreateTime;

    /**
     * 任务ID集合
     */
    List<Long> performanceReviewTaskIds;

    /**
     * 审核模式
     * 0-人工审核，1-AI审核
     */
    private Integer reviewMode;
}
