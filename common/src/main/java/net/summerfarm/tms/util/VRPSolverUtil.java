package net.summerfarm.tms.util;

import com.google.ortools.Loader;
import com.google.ortools.constraintsolver.*;
import com.google.protobuf.Duration;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

// 使用 SLF4J 日志框架
@SuppressWarnings("all")
@Component
@Slf4j
public class VRPSolverUtil {
    private static final Logger log = LoggerFactory.getLogger(VRPSolverUtil.class);

    /**
     * 求解时间限制（秒），默认为2秒
     */
    private Long timeLimit;

    /**
     * 获取时间限制，如果为空则返回默认值2
     * @return 时间限制（秒）
     */
    public Long getTimeLimit() {
        return timeLimit != null ? timeLimit : 2L;
    }

    /**
     * 设置时间限制
     * @param timeLimit 时间限制（秒）
     */
    public void setTimeLimit(Long timeLimit) {
        this.timeLimit = timeLimit;
    }

    /**
     * 解决车辆路径问题并返回有序 ID 列表
     *
     * @param pois     POI 经纬度列表，格式：[[经度, 纬度], ...]
     * @param ids      对应 POI 的唯一标识列表
     * @param depotId  仓库的 ID（必须在 ids 中存在）
     * @return 按访问顺序排列的 ID 列表（包含仓库作为起点）
     * @throws IllegalArgumentException 输入参数不合法时抛出
     */
    public List<Long> solve(List<double[]> pois, List<Long> ids, Long depotId) {
        validateInput(pois, ids, depotId);
        int depotIndex = ids.indexOf(depotId);
        long[][] distanceMatrix = computeDistanceMatrix(pois);

        // 添加虚拟节点
        int virtualNodeIndex = pois.size();
        long[][] newDistanceMatrix = new long[distanceMatrix.length + 1][distanceMatrix.length + 1];
        for (int i = 0; i < distanceMatrix.length; i++) {
            System.arraycopy(distanceMatrix[i], 0, newDistanceMatrix[i], 0, distanceMatrix[i].length);
            newDistanceMatrix[i][virtualNodeIndex] = 0;
        }
        Arrays.fill(newDistanceMatrix[virtualNodeIndex], 0);

        // 路由参数配置
        int vehicleNumber = 1;
        int[] starts = new int[vehicleNumber];
        int[] ends = new int[vehicleNumber];
        Arrays.fill(starts, depotIndex);
        Arrays.fill(ends, virtualNodeIndex);

        RoutingIndexManager manager = new RoutingIndexManager(
                newDistanceMatrix.length,
                vehicleNumber,
                starts,
                ends);

        RoutingModel routing = new RoutingModel(manager);

        // 注册距离回调
        final int transitCallbackIndex = routing.registerTransitCallback(
                (long fromIndex, long toIndex) -> {
                    int fromNode = manager.indexToNode(fromIndex);
                    int toNode = manager.indexToNode(toIndex);
                    return newDistanceMatrix[fromNode][toNode];
                });

        routing.setArcCostEvaluatorOfAllVehicles(transitCallbackIndex);

        // 添加距离维度
        routing.addDimension(
                transitCallbackIndex,
                0,          // 无等待时间
                **********L, // 最大行驶距离（********** 公里）50
                false,      // 不强制结束时的累积距离归零
                "Distance");

        // 配置求解参数
        RoutingSearchParameters searchParameters = main.defaultRoutingSearchParameters()
               .toBuilder()
               .setFirstSolutionStrategy(FirstSolutionStrategy.Value.SAVINGS)
               .setLocalSearchMetaheuristic(LocalSearchMetaheuristic.Value.GUIDED_LOCAL_SEARCH)
               .setTimeLimit(Duration.newBuilder().setSeconds(getTimeLimit()).build())
               .build();

        Assignment solution = routing.solveWithParameters(searchParameters);

        if (solution == null) {
            throw new RuntimeException("未找到可行路径");
        }

        return extractRoute(manager, routing, solution, ids);
    }

    private void validateInput(List<double[]> pois, List<Long> ids, Long depotId) {
        if (pois == null || ids == null) {
            throw new IllegalArgumentException("POIs 和 IDs 不能为 null");
        }
        if (pois.size() != ids.size()) {
            throw new IllegalArgumentException("POIs 和 IDs 长度必须一致");
        }
        if (!ids.contains(depotId)) {
            throw new IllegalArgumentException("仓库 ID 不存在于 IDs 中");
        }
        for (double[] coord : pois) {
            if (coord.length != 2) {
                throw new IllegalArgumentException("坐标格式错误，应为[经度, 纬度]");
            }
        }
    }

    private long[][] computeDistanceMatrix(List<double[]> pois) {
        int size = pois.size();
        long[][] matrix = new long[size][size];

        for (int i = 0; i < size; i++) {
            double[] from = pois.get(i);
            for (int j = 0; j < size; j++) {
                if (i == j) {
                    matrix[i][j] = 0;
                    continue;
                }
                double[] to = pois.get(j);
                double dx = (from[0] - to[0]) * 111319.9; // 经度差转米
                double dy = (from[1] - to[1]) * 111319.9; // 纬度差转米
                matrix[i][j] = Math.round(Math.sqrt(dx * dx + dy * dy));
            }
        }
        return matrix;
    }

    private List<Long> extractRoute(RoutingIndexManager manager,
                                           RoutingModel routing,
                                           Assignment solution,
                                           List<Long> ids) {

        log.info("总行驶距离: " + solution.objectiveValue() + " 米");
        log.info("--------------------------------");
        List<Long> route = new ArrayList<>();
        long index = routing.start(0);

        while (!routing.isEnd(index)) {
            int nodeIndex = manager.indexToNode(index);
            if (nodeIndex < ids.size()) {
                route.add(ids.get(nodeIndex));
            }
            index = solution.value(routing.nextVar(index));
        }

        return route;
    }
/*
    public static void main(String[] args) {
        // 测试数据
        List<double[]> pois = Arrays.asList(
                new double[]{114.36594,27.849444},  // 仓库
                new double[]{114.662677,27.813501},
                new double[]{114.678799,27.80759},
                new double[]{114.685817,27.816777},
                new double[]{114.688434,27.821017},  // 仓库
                new double[]{114.694571,27.819678},
                new double[]{114.897386,27.816184},
                new double[]{114.909701,27.811595},
                new double[]{114.910810,27.822836},  // 仓库
                new double[]{114.910812,27.822842},
                new double[]{114.901625,27.828150},
                new double[]{114.*************,27.***************},
                new double[]{114.921885,27.828911},  // 仓库
                new double[]{114.921604,27.828590},
                new double[]{114.931824,27.840677},
                new double[]{114.929528,27.796311},
                new double[]{114.944076,27.820920},  // 仓库
                new double[]{114.965771,27.834014},
                new double[]{115.00018880208333,27.859832356770834},
                new double[]{115.389324,27.735932},
                new double[]{115.387692,27.737342},
                new double[]{115.395818,27.734683},  // 仓库
                new double[]{115.318737,27.579678}
        );

        List<Long> ids = new ArrayList<>();
        long a = 23;
        for (long i = 0; i < a; i++) {
            ids.add(i);
        }

        try {
            List<Long> route = VRPSolverUtil.solve(pois, ids, 0L);
            log.info("最优路径顺序：");
            log.info(String.join(" -> ", route.toString()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/
}    