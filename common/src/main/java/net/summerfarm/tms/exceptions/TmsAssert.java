package net.summerfarm.tms.exceptions;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;

public class TmsAssert {


    public static void notNull(Object object, ErrorCodeEnum errorCode) {
        if (object == null) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void isTrue(boolean expression, ErrorCodeEnum errorCode) {
        if (!expression) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void notEmpty(Collection<?> collection, ErrorCodeEnum errorCode) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void notEmpty(Map<?, ?> map, ErrorCodeEnum errorCode) {
        if (CollectionUtils.isEmpty(map)) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void notEmpty(Object[] array, ErrorCodeEnum errorCode) {
        if (ObjectUtils.isEmpty(array)) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void hasText(String text, ErrorCodeEnum errorCode) {
        if (!StringUtils.hasText(text)) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void isNull(Object object, ErrorCodeEnum errorCode) {
        if (object != null) {
            throw new TmsRuntimeException(errorCode);
        }
    }

    public static void isEmpty(Collection<?> collection, ErrorCodeEnum errorCode) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new TmsRuntimeException(errorCode);
        }
    }


    public static void notNull(Object object, ErrorCodeEnum errorCode, String... errorParams) {
        if (object == null) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void isTrue(boolean expression, ErrorCodeEnum errorCode, String... errorParams) {
        if (!expression) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void notEmpty(Collection<?> collection, ErrorCodeEnum errorCode, String errorParams) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void notEmpty(Map<?, ?> map, ErrorCodeEnum errorCode, String errorParams) {
        if (CollectionUtils.isEmpty(map)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void notEmpty(Object[] array, ErrorCodeEnum errorCode, String errorParams) {
        if (ObjectUtils.isEmpty(array)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void notEmpty(String text, ErrorCodeEnum errorCode, String errorParams) {
        if (StringUtils.isEmpty(text)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void hasText(String text, ErrorCodeEnum errorCode, String errorParams) {
        if (!StringUtils.hasText(text)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void isNull(Object object, ErrorCodeEnum errorCode, String errorParams) {
        if (object != null) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void isEmpty(Collection<?> collection, ErrorCodeEnum errorCode, String errorParams) {
        if (!CollectionUtils.isEmpty(collection)) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }

    public static void condition(Boolean flag, ErrorCodeEnum errorCode, String errorParams) {
        if (flag) {
            throw new TmsRuntimeException(errorCode, errorParams);
        }
    }
}
