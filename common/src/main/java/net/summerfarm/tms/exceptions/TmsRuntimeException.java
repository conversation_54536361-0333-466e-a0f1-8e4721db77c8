package net.summerfarm.tms.exceptions;

public class TmsRuntimeException extends RuntimeException {
    private static final long serialVersionUID = 9155666669044776012L;
    private final ErrorCodeEnum errorCode;
    private final String errorMessage;

    public TmsRuntimeException(String errorMessage) {
        this.errorCode = ErrorCodeEnum.BIZ_EXCEPTION;
        this.errorMessage = errorMessage;
    }

    public TmsRuntimeException(ErrorCodeEnum errorCode) {
        this.errorCode = errorCode;
        this.errorMessage = errorCode.msg;
    }

    public TmsRuntimeException(Throwable t, ErrorCodeEnum errorCode) {
        super(t);
        this.errorCode = errorCode;
        this.errorMessage = super.getMessage();
    }

    public TmsRuntimeException(ErrorCodeEnum errorCode, Object... errorParams) {
        this.errorCode = errorCode;
        this.errorMessage = String.format(errorCode.msg, errorParams);
    }

    public ErrorCodeEnum getErrorCode() {
        return errorCode;
    }

    @Override
    public String getMessage() {
        return this.errorMessage;
    }
}
