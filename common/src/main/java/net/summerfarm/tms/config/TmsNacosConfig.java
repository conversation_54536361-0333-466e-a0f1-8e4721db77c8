package net.summerfarm.tms.config;

import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2023/9/1 13:48<br/>
 *
 * <AUTHOR> />
 */
@Slf4j
@Data
@Configuration
@NacosConfigurationProperties(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class TmsNacosConfig {

    /**
     * 鲜小配鲜沐无需扫码的品牌ID
     */
    private String noScanXianMuOuterBrandIds;

    /**
     * 鲜小配Saas无需扫码的品牌ID
     */
    private String noScanSaasOuterBrandIds;

    /**
     * net.summerfarm.tms.client.dist.provider.TmsDistSiteQueryProvider#queryPurchaseOwnSaleOutSite 天数配置
     */
    private Integer purchaseOwnSaleOutSiteDay;

    /**
     * 飞书通知缺货核准链接地址
     */
    private String lackApprovedSendMsgUrl;

    /**
     * 缺货核准下载链接
     */
    private String lackApprovedDownUrl;

    /**
     * 鲜沐需要到店打卡品牌ID
     */
    private String xianMuCheakInPunchBrandIds;

    /**
     * 到店打卡距离
     */
    private BigDecimal punchRange;

    /**
     * 方法访问开关 true 不能进入方法
     */
    private boolean methodStop;

    /**
     * 默认最晚送达时间
     */
    private String defaultLastDelivertTime;

    /**
     * 高德最短距离城配仓配置
     */
    private String gaodeMinDistanceStoreNos;

    /**
     * 签收照片关键字
     */
    private String signInPicKeywords;

    /**
     * 配送单配送站点限制
     */
    private String deliveryNoteSiteNumLimit;

    /**
     * ortools算法的城配仓配置
     */
    private String ortoolsStoreNos;

    /**
     * 是否初始化干线单据的配送方式
     */
    private String doInitTrunkFulfillmentWay;

    /**
     * 多履约方式机器人通知地址
     */
    private String manyFulfillmentWayRobotUrl;

    /**
     * 干线转运委托单未匹配到路由机器人通知地址
     */
    private String trunkTransportDistNoRouteRobotUrl;

    public List<String> getSignInPicKeywordList(){
        if(StringUtil.isBlank(signInPicKeywords)){
            return Collections.emptyList();
        }
        return Arrays.asList(signInPicKeywords.split(","));
    }

    public List<String> queryNoScanXianMuBrandIds(){
        String brandIds = this.getNoScanXianMuOuterBrandIds();
        log.info("鲜小配鲜沐无需扫码的品牌ID:{}",brandIds);
        if(StringUtil.isBlank(brandIds)){
            return Collections.emptyList();
        }
        return Arrays.asList(brandIds.split(","));
    }

    public List<String> queryNoScanSaasBrandIds(){
        String brandIds = this.getNoScanSaasOuterBrandIds();
        log.info("鲜小配Saas无需扫码的品牌ID:{}",brandIds);

        if(StringUtil.isBlank(brandIds)){
            return Collections.emptyList();
        }
        return Arrays.asList(brandIds.split(","));
    }

    public Integer queryPurchaseOwnSaleOutSiteDay(){
        if(this.purchaseOwnSaleOutSiteDay == null){
            return 30;
        }
        return this.purchaseOwnSaleOutSiteDay;
    }

    public List<String> queryCheakInPunchXmBrandIds(){
        String brandIds = this.getXianMuCheakInPunchBrandIds();
        log.info("鲜沐到店打卡品牌ID:{}",brandIds);
        if(StringUtil.isBlank(brandIds)){
            return Collections.emptyList();
        }
        return Arrays.asList(brandIds.split(","));
    }

    public LocalTime queryDefaultLastDelivertTime(){
        LocalTime salfDeliveryTime = LocalTime.of(14, 0);
        if(this.defaultLastDelivertTime == null){
            return salfDeliveryTime;
        }
        try {
            return LocalTime.parse(this.defaultLastDelivertTime);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解析最晚配送时间异常",e);
            return salfDeliveryTime;
        }
    }

    /**
     * 高德最短距离城配仓配置
     * @return 城配仓编号集合
     */
    public List<String> queryGaodeMinDistanceStoreNos(){
        String gaodeMinDistanceStoreNos = this.getGaodeMinDistanceStoreNos();
        log.info("高德最短距离城配仓配置:{}",gaodeMinDistanceStoreNos);
        if(StringUtil.isBlank(gaodeMinDistanceStoreNos)){
            return Collections.emptyList();
        }
        return Arrays.asList(gaodeMinDistanceStoreNos.split(","));
    }

    /**
     * 配送单配送站点限制
     * @return 配送站点限制
     */
    public Integer queryDeliveryNoteSiteNumLimit(){
        if(StringUtil.isBlank(this.deliveryNoteSiteNumLimit)){
            return 80;
        }

        Integer currentDeliveryNoteSiteNumLimit = 80;
        try {
            return Integer.parseInt(deliveryNoteSiteNumLimit);
        } catch (NumberFormatException e) {
            return currentDeliveryNoteSiteNumLimit;
        }
    }

    /**
     * ortools算法的城配仓配置
     * @return 城配仓编号集合
     */
    public List<String> queryOrtoolsStoreNos(){
        if(StringUtil.isBlank(this.ortoolsStoreNos)){
            return Collections.emptyList();
        }

        return Arrays.asList(this.ortoolsStoreNos.split(","));
    }

    /**
     * 是否初始化干线单据的配送方式
     * @return true 初始化 false 不初始化
     */
    public boolean getDoInitTrunkFulfillmentWay() {
        try {
            return Boolean.parseBoolean(doInitTrunkFulfillmentWay);
        } catch (Exception e) {
            return false;
        }
    }
}
