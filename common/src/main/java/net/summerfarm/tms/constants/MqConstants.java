package net.summerfarm.tms.constants;

/**
 * Description:MQ常量类
 * date: 2022/9/23 10:46
 *
 * <AUTHOR>
 */
public class MqConstants {

    /**
     * TMS 主题
     */
    public interface Topic {
        String TMS_LIST = "tms-list";
        String TMS_DELIVERY = "tms-delivery";
        String MYSQL_BINLOG_ORDERLY = "mysql-binlog-orderly";
        String MALL_LIST = "mall-list";
        String TMS_DIST_REQUIREMENT = "topic-tms-dist-requirement";
        String TMS_DIST_RESULT = "topic-tms-dist-result";
        String TMS_PATH = "topic_tms_path";
        String WMS_STOCK_TASK = "topic_wms_stock_task";
        String TOPIC_WNC_WAREHOUSE = "topic_wnc_warehouse";
    }

    /**
     * TMS 标签
     */
    public interface Tag {
        String TMS_DELIVERY_EVENT = "tms_delivery_event";
        String TRUNK_DELIVERY_EVENT = "tag_trunk_delivery_event";
        String DELIVERY_PICK_LACK_GOODS = "tag_delivery_pick_lack_goods";
        String SYNC_2_OLD_TMS = "sync-old-tms";
        String DIST_ORDER_CREATE = "tag_dist_order_create";
        String DIST_ORDER_CHANGE = "tag_dist_order_change";
        String DIST_ORDER_CANCEL = "tag_dist_order_cancel";
        String DIST_ORDER_DETAIL_CANCEL = "tag_dist_order_detail_cancel";
        String DIST_ORDER_HANDLE = DIST_ORDER_CREATE + "||" + DIST_ORDER_CHANGE + "||" + DIST_ORDER_CANCEL + "||" + DIST_ORDER_DETAIL_CANCEL;
        String RESULT_TO_OFC = "tag_result_to_ofc";
        String TAG_DELIVERY_COMPLETE_PATH = "tag_delivery_complete_path";
        String TAG_DELIVERY_COMPLETE_PATH_SELF = "tag_delivery_complete_path_self";
        String IN_STORE_COST = "in-store-msg-10||in-store-msg-11";
        String TAG_DELIVERY_COMPLETE_PATH_BUS_MSG = "tag_delivery_complete_path_bus_msg";
        String TAG_CALC_PATH_DISTANCE = "tag_calc_path_distance";
        String TAG_TMS_DELIVERY_SITE_SEND_REMARK = "tag_tms_delivery_site_send_remark";
        String TAG_CALC_BATCH_LOAD_RATIO = "tag_calc_batch_load_ratio";
        String TAG_WNC_WAREHOUSE_CREATE = "tag_wnc_warehouse_create";
        String TAG_WNC_WAREHOUSE_UPDATE = "tag_wnc_warehouse_update";
        String TAG_WNC_WAREHOUSE_LOGISTICS_UPSERT = "tag_wnc_warehouse_logistics_upsert";
        String TAG_TMS_TRUNK_OUT_DIST_ORDER = "tag_tms_trunk_out_dist_order";
        String TAG_TMS_TRUNK_BATCH_PICK_UP_SHORT_GOODS = "tag_tms_trunk_batch_pick_up_short_goods";
        String TAG_DELIVERY_STORE_COMPLETE_PATH = "tag_delivery_store_complete_path";

        String TAG_TMS_DELIVERY_PERFORMANCE_REVIEW_APPEAL_CREATE = "tag_tms_delivery_performance_review_appeal_create";
    }

    /**
     * TMS 消费组
     */
    public interface ConsumeGroup {
        String GID_TMS = "GID_tms";
        String GID_TMS_SYNC_2_OLD = "GID_tms_sync_2_old";
        String GID_TMS_BINLOG_ORDERLY = "GID_tms_binlog_orderly";
        String GID_TMS_DIST_ORDER_HANDLE = "GID_tms_dist_order_handle";
        String GID_TMS_DELIVERY_COMPLETE_PATH = "GID_tms_delivery_complete_path";
        String GID_TMS_DELIVERY_COMPLETE_PATH_SELF = "GID_tms_delivery_complete_path_self";
        String GID_TMS_BINLOG_COST_ORDERLY = "GID_tms_binlog_cost_orderly";
        String GID_TMS_COST_RELATE = "GID_tms_cost_relate";
        String GID_TMS_DELIVERY_COMPLETE_PATH_BUS_MSG = "GID_tms_delivery_complete_path_bus_msg";
        String GID_TMS_DELIVERY_CALC_DISTANCE = "GID_tms_delivery_calc_distance";
        String GID_TMS_DELIVERY_SITE_SEND_REMARK = "GID_tms_delivery_site_send_remark";
        String GID_TMS_TEMPERATURE_CONDITIONS = "GID_tms_temperature_conditions";
        String GID_TMS_CALC_BATCH_LOAD_RATIO = "GID_tms_calc_batch_load_ratio";
        String GID_TMS_DIST_SITE_WAREHOUSE_CREATE = "GID_tms_dist_site_warehouse_create";
        String GID_TMS_DIST_SITE_WAREHOUSE_UPDATE = "GID_tms_dist_site_warehouse_update";
        String GID_TMS_DIST_SITE_LOGISTICS_UPSERT = "GID_tms_dist_site_logistics_upsert";
        String GID_TMS_DELIVERY_COMMON = "GID_tms_delivery_common";
        String GID_TMS_BINLOG_DIST_ORDER_ORDERLY = "GID_tms_binlog_dist_order_orderly";


    }

    /**
     * TMS 事件
     */
    public enum Event {
        /**
         * 仓库任务完成事件
         */
        WAREHOUSE_TASK_COMPLETE,
        /**
         * 仓库入库任务完成事件
         */
        WAREHOUSE_IN_STOCK_COMPLETE,
        /**
         * 委托单创建事件
         */
        DIST_ORDER_CREATE,
        /**
         * 委托单变更事件
         */
        DIST_ORDER_CHANGE,
        /**
         * 委托单取消事件
         */
        DIST_ORDER_CANCEL,
        /**
         * 委托单明细取消事件
         */
        DIST_ORDER_DETAIL_CANCEL,
        ;
    }

    /**
     * WMS 消息类型
     */
    public interface WMSType {
        String TMS_STOCK_IN_STORE = "TMS_STOCK_IN_STORE";
    }
}
