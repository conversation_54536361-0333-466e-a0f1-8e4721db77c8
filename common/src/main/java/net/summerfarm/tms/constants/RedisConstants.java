package net.summerfarm.tms.constants;

/**
 * Description: <br/>
 * date: 2022/11/24 14:50<br/>
 *
 * <AUTHOR> />
 */
public class RedisConstants {

    private static final String PREFIX = "tms:";

    public interface Key {
        //POP城配仓缓存
        String POP_STORE_CACHE = PREFIX + "pop_store_cache" + ":cache";
    }
    /**
     * 配送
     */
    public interface Delivery {
        /**
         * 完成排线
         */
        String TMS_COMPLETE_PATH = "tms_complete_path";
        /**
         * 完成配送
         */
        String TMS_FINISH_DELIVERY_SITE = "TMS_FINISH_DELIVERY_SITE";
        /**
         * 点位排线
         */
        String CHANGE_SITE_BATCH = "CHANGE_SITE_BATCH";
        /**
         * 智能排线
         */
        String INTELLIGENT_PATH = "intelligent_path";

        /**
         * 扫码
         */
        String SCAN_CODE = "TMS_SCAN_CODE";

        /**
         * 点位审核
         */
        String TMS_DELIVERY_SITE_AUDIT = "tms_delivery_site_audit:";

        /**
         * 城配仓新增路线
         */
        String TMS_CITY_DELIVERY_BATCH_PATH_SAVE = "tms_city_delivery_batch_path_save:";

        /**
         * 城配仓拣货扫码
         */
        String CITY_PICK_SCAN_CODE = "city_pick_scan_code";

        /**
         * 城配仓拣货缺货
         */
        String CITY_PICK_SHORT = "city_pick_short";

        /**
         * 干线到仓打卡
         */
        String TRUNK_IN_PUNCH = "trunk_in_punch:";

        /**
         * 配送单下载
         */
        String TMS_DELIVERY_NOTE_DOWN = "tms_delivery_note_down:";

        /**
         * 上传保温措施图片
         */
        String UPLOAD_KEEP_TEMPERATURE_METHOD_PICS = "upload_keep_temperature_method_pics";
    }

    public interface Site{
        /**
         * 仓库新增点位
         */
        String SITE_CREATE = "SITE_CREATE";
        /**
         * 城配仓新增更新点位
         */
        String STORE_SITE_UPSERT = "STORE_SITE_UPSERT";
    }

    /**
     * 委托单推送
     */
    public interface DistRequirement{
        /**
         * 委托单联系人
         */
        String TMS_DIST_REQUIREMENT_CONTACT_ID = "tms_dist_requirement_contact_id";
    }

    /**
     * 排线操作
     */
    public interface PathOpearation{
        /**
         * 路线操作通用
         */
        String TMS_PATH_OPERATION_COMMON = "TMS_PATH_OPERATION_COMMON";

        /**
         * 路线计算距离
         */
        String TMS_CALC_PATH_DISTANCE = "TMS_CALC_PATH_DISTANCE";
    }


    /**
     * 履约审核
     */
    public interface Performance{
        /**
         * 审核申诉
         */
        String DELIVERY_PERFORMANCE_REVIEW_DETAIL_APPEAL_CREATE = PREFIX + "DELIVERY_PERFORMANCE_REVIEW_DETAIL_APPEAL_CREATE:";
    }
}
