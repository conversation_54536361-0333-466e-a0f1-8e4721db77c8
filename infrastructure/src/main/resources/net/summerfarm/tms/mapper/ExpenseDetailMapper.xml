<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.ExpenseDetailMapper">
  <resultMap id="BaseResultMap" type="net.summerfarm.tms.expense.dto.ExpenseDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="expense_id" jdbcType="INTEGER" property="expenseId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="photos" jdbcType="VARCHAR" property="photos" />
    <result column="is_review" jdbcType="VARCHAR" property="isReview" />
    <result column="start_address" jdbcType="VARCHAR" property="startAddress" />
    <result column="end_address" jdbcType="VARCHAR" property="endAddress" />
    <result column="mileage" jdbcType="INTEGER" property="mileage" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="update_time" jdbcType="VARCHAR" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, expense_id, state, type, photos, is_review, start_address, end_address, mileage,amount,remark,updater, update_time,creator, create_time
  </sql>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.expense.dto.Expense" useGeneratedKeys="true">
    insert into expense_detail (expense_id, state, type, photos, is_review, start_address,
     end_address, mileage,amount,remark,updater, update_time,creator, create_time
      )
    values (#{expenseId,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{type,jdbcType=INTEGER},
      #{photos,jdbcType=INTEGER}, #{isReview,jdbcType=INTEGER}, #{startAddress,jdbcType=INTEGER},
      #{endAddress,jdbcType=INTEGER}, #{mileage}, #{amount}, #{remark}, #{updater}, #{updateTime}
      , #{creator}, now()
      )
  </insert>
    <delete id="deleteByExpenseId">
      delete from expense_detail where expense_id = #{id}
    </delete>

    <select id="selectByExpenseId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from expense_detail
    where expense_id = #{id,jdbcType=INTEGER}
  </select>

  <update id="updateById" parameterType="net.summerfarm.tms.expense.dto.Expense">
    update expense_detail
    <set>
      <if test="endAddress != null">
        end_address = #{endAddress,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        updater = #{updater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = now(),
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>