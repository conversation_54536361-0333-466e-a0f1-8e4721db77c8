<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDriverCarCarrierMappingMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tms_car_id" jdbcType="BIGINT" property="tmsCarId"/>
        <result column="tms_driver_id" jdbcType="BIGINT" property="tmsDriverId"/>
        <result column="carrier_id" jdbcType="INTEGER" property="carrierId"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="warehouse_site_id" jdbcType="BIGINT" property="warehouseSiteId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tms_car_id, tms_driver_id, carrier_id, state, create_time, update_time, business_type,
        warehouse_site_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tms_driver_car_carrier_mapping
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tms_driver_car_carrier_mapping
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping">
        insert into tms_driver_car_carrier_mapping (id, tms_car_id, tms_driver_id,
        carrier_id, state, create_time,
        update_time, business_type, warehouse_site_id
        )
        values (#{id,jdbcType=BIGINT}, #{tmsCarId,jdbcType=BIGINT}, #{tmsDriverId,jdbcType=BIGINT},
        #{carrierId,jdbcType=INTEGER}, #{state,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{businessType,jdbcType=TINYINT}, #{warehouseSiteId,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping">
        insert into tms_driver_car_carrier_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tmsCarId != null">
                tms_car_id,
            </if>
            <if test="tmsDriverId != null">
                tms_driver_id,
            </if>
            <if test="carrierId != null">
                carrier_id,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="warehouseSiteId != null">
                warehouse_site_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tmsCarId != null">
                #{tmsCarId,jdbcType=BIGINT},
            </if>
            <if test="tmsDriverId != null">
                #{tmsDriverId,jdbcType=BIGINT},
            </if>
            <if test="carrierId != null">
                #{carrierId,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="warehouseSiteId != null">
                #{warehouseSiteId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping">
        update tms_driver_car_carrier_mapping
        <set>
            <if test="tmsCarId != null">
                tms_car_id = #{tmsCarId,jdbcType=BIGINT},
            </if>
            <if test="tmsDriverId != null">
                tms_driver_id = #{tmsDriverId,jdbcType=BIGINT},
            </if>
            <if test="carrierId != null">
                carrier_id = #{carrierId,jdbcType=INTEGER},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="warehouseSiteId != null">
                warehouse_site_id = #{warehouseSiteId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping">
        update tms_driver_car_carrier_mapping
        set tms_car_id = #{tmsCarId,jdbcType=BIGINT},
        tms_driver_id = #{tmsDriverId,jdbcType=BIGINT},
        carrier_id = #{carrierId,jdbcType=INTEGER},
        state = #{state,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        business_type = #{businessType,jdbcType=TINYINT},
        warehouse_site_id = #{warehouseSiteId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByDriverCarCarrierMapping" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tms_driver_car_carrier_mapping
        <where>
            <if test="tmsDriverId != null">
                and tms_driver_id = #{tmsDriverId}
            </if>
            <if test="tmsCarId != null">
                and tms_car_id = #{tmsCarId}
            </if>
            <if test="warehouseSiteId != null">
                and warehouse_site_id = #{warehouseSiteId}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
        </where>
    </select>

    <select id="selectListByDriverCarCarrierMapping" parameterType="net.summerfarm.tms.dao.TmsDriverCarCarrierMapping"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tms_driver_car_carrier_mapping
        <where>
            <if test="tmsDriverId != null">
                and tms_driver_id = #{tmsDriverId}
            </if>
            <if test="tmsCarId != null">
                and tms_car_id = #{tmsCarId}
            </if>
            <if test="warehouseSiteId != null">
                and warehouse_site_id = #{warehouseSiteId}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
        </where>
    </select>
</mapper>