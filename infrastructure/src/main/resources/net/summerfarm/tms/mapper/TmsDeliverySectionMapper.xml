<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliverySectionMapper">
    <insert id="saveBatch" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="net.summerfarm.tms.dao.TmsDeliverySection">
        INSERT INTO `tms_delivery_section`
            (
             `batch_id`, `begin_site_id`, `end_site_id`, `distance`, `type`
             )
             VALUES
        <foreach collection="tmsDeliverySections" item="item" separator=",">
            (
            #{item.batchId}, #{item.beginSiteId}, #{item.endSiteId}, #{item.distance}, #{item.type}
            )
        </foreach>
    </insert>
</mapper>
