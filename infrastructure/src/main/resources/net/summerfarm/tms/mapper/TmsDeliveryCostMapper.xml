<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.summerfarm.tms.mapper.TmsDeliveryCostMapper">
    <resultMap id="BaseResultMap" type="net.summerfarm.tms.dao.TmsDeliveryCost">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="dist_order_id" jdbcType="BIGINT" property="distOrderId"/>
        <result column="out_item_id" jdbcType="VARCHAR" property="outItemId"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="average_cost" jdbcType="DECIMAL" property="averageCost"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <resultMap id="CostResultMap" type="net.summerfarm.tms.cost.entity.DeliveryCostEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="out_batch_id" jdbcType="VARCHAR" property="outBatchId"/>
        <result column="dist_order_id" jdbcType="BIGINT" property="distOrderId"/>
        <result column="out_item_id" jdbcType="VARCHAR" property="outItemId"/>
        <result column="source"
                javaType="net.summerfarm.tms.enums.DistOrderSourceEnum"
                jdbcType="INTEGER"
                typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"
                property="source"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="average_cost" jdbcType="DECIMAL" property="averageCost"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, dist_order_id, out_item_id,`source`, quantity, average_cost, create_time, update_time
    </sql>

    <select id="queryCostList" resultMap="CostResultMap" parameterType="net.summerfarm.tms.query.delivery.DeliveryCostQuery">
        SELECT
        tdc.id,
        tdc.dist_order_id,
        tdcr.out_batch_id,
        tdc.out_item_id,
        tdc.`source` - 100 AS source,
        tdc.quantity,
        IFNULL(tdc.average_cost,0) AS average_cost,
        tdc.create_time,
        tdc.update_time
        FROM tms_delivery_cost_relation tdcr
        LEFT JOIN tms_delivery_cost tdc ON tdcr.dist_order_id = tdc.dist_order_id AND tdcr.out_item_id = tdc.out_item_id
        <where>
            <if test="outBatchIds != null and outBatchIds.size > 0">
                AND tdcr.out_batch_id IN
                <foreach collection="outBatchIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
