package net.summerfarm.tms.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import net.summerfarm.tms.converter.TmsDeliveryPickConverter;
import net.summerfarm.tms.dao.TmsDeliveryPick;
import net.summerfarm.tms.dataobj.DeliverySiteStandardTempConditionDataObj;
import net.summerfarm.tms.dataobj.converter.DeliverySiteStandardTempConditionDataConverter;
import net.summerfarm.tms.delivery.DeliveryPickRepository;
import net.summerfarm.tms.delivery.entity.DeliveryPickEntity;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempCondition;
import net.summerfarm.tms.delivery.helper.DeliverySiteStandardTempUnit;
import net.summerfarm.tms.mapper.TmsDeliveryPickMapper;
import net.summerfarm.tms.delivery.entity.BatchPickDetailEntity;
import net.summerfarm.tms.query.delivery.DeliveryPickQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 签发(拣货)详情
 */
@Service
public class DeliveryPickRepositoryImpl implements DeliveryPickRepository {

    @Resource
    TmsDeliveryPickMapper tmsDeliveryPickMapper;

    @Override
    public void update(DeliveryPickEntity deliveryPickEntity) {
        tmsDeliveryPickMapper.updateById(TmsDeliveryPickConverter.entity2Do(deliveryPickEntity));
    }

    @Override
    public DeliveryPickEntity query(Long id) {
        return TmsDeliveryPickConverter.do2Entity(tmsDeliveryPickMapper.selectById(id));
    }

    @Override
    public List<DeliveryPickEntity> queryList(DeliveryPickQuery deliveryPickQuery) {
        List<TmsDeliveryPick> tmsDeliveryPicks = tmsDeliveryPickMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPick>()
            .eq(deliveryPickQuery.getBatchId() != null, TmsDeliveryPick::getDeliveryBatchId, deliveryPickQuery.getBatchId())
            .eq(deliveryPickQuery.getSiteId() != null, TmsDeliveryPick::getSiteId, deliveryPickQuery.getSiteId())
            .eq(!StringUtils.isEmpty(deliveryPickQuery.getParticle()), TmsDeliveryPick::getParticle, deliveryPickQuery.getParticle())
            .in(!CollectionUtils.isEmpty(deliveryPickQuery.getOutItemIds()), TmsDeliveryPick::getOutItemId, deliveryPickQuery.getOutItemIds())
            .in(!CollectionUtils.isEmpty(deliveryPickQuery.getBatchIdList()), TmsDeliveryPick::getDeliveryBatchId, deliveryPickQuery.getBatchIdList())
            .orderByDesc(TmsDeliveryPick::getCategoryType).orderByDesc(TmsDeliveryPick::getItemDesc)
        );

        return tmsDeliveryPicks.stream().map(TmsDeliveryPickConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public void save(DeliveryPickEntity deliveryPickEntity) {
        tmsDeliveryPickMapper.insert(TmsDeliveryPickConverter.entity2Do(deliveryPickEntity));
    }

    @Override
    public void saveBatch(ArrayList<DeliveryPickEntity> deliveryPickEntities) {
        if(CollectionUtils.isEmpty(deliveryPickEntities)){
            return;
        }
        List<TmsDeliveryPick> tmsDeliveryPicks = deliveryPickEntities.stream().map(TmsDeliveryPickConverter::entity2Do).collect(Collectors.toList());
        for (TmsDeliveryPick tmsDeliveryPick : tmsDeliveryPicks) {
            tmsDeliveryPick.setQuantity(tmsDeliveryPick.getQuantity() == null ? 0 : tmsDeliveryPick.getQuantity());
            tmsDeliveryPick.setPickQuantity(tmsDeliveryPick.getPickQuantity()== null ? 0 : tmsDeliveryPick.getPickQuantity());
            tmsDeliveryPick.setShortQuantity(tmsDeliveryPick.getShortQuantity() == null ? 0 : tmsDeliveryPick.getShortQuantity());
            tmsDeliveryPick.setInterceptQuantity(tmsDeliveryPick.getInterceptQuantity() == null ? 0 : tmsDeliveryPick.getInterceptQuantity());
        }
        //过滤小于等于0的拣货数据
        tmsDeliveryPicks = tmsDeliveryPicks.stream()
                .filter(tmsDeliveryPick -> tmsDeliveryPick.getQuantity() > 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(tmsDeliveryPicks)){
            return;
        }
        tmsDeliveryPickMapper.saveBatch(tmsDeliveryPicks);
    }

    @Override
    public void removeByIdList(List<Long> idList) {
        if(CollectionUtils.isEmpty(idList)){
            return;
        }
        tmsDeliveryPickMapper.deleteBatchIds(idList);
    }

    @Override
    public void removeByBatchId(Long batchId) {
        if(batchId == null){
            return;
        }
        tmsDeliveryPickMapper.delete(new LambdaQueryWrapper<TmsDeliveryPick>().eq(TmsDeliveryPick::getDeliveryBatchId, batchId));
        /*List<DeliveryPickEntity> deliveryPickEntityList = this.queryList(DeliveryPickQuery.builder().batchId(batchId).build());
        if(CollectionUtils.isEmpty(deliveryPickEntityList)){
            return;
        }
        removeByIdList(deliveryPickEntityList.stream().map(DeliveryPickEntity::getId).collect(Collectors.toList()));*/
    }

    @Override
    public List<BatchPickDetailEntity> queryByBatch(List<Long> batchIds) {
        return tmsDeliveryPickMapper.queryByBatch(batchIds);
    }

    @Override
    public List<DeliveryPickEntity> queryByBatchIdSku(Long deliveryBatchId, String sku) {
        if(deliveryBatchId == null || StrUtil.isBlank(sku)){
            return Collections.emptyList();
        }

        List<TmsDeliveryPick> tmsDeliveryPicks = tmsDeliveryPickMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPick>()
                .eq(TmsDeliveryPick::getDeliveryBatchId, deliveryBatchId)
                .eq(TmsDeliveryPick::getOutItemId, sku)
        );

        return tmsDeliveryPicks.stream().map(TmsDeliveryPickConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<DeliverySiteStandardTempCondition> queryPickStandardItemTemperatureConditionsByDelSiteIds(List<Long> deliverySiteIds) {
        if(CollectionUtils.isEmpty(deliverySiteIds)){
            return Collections.emptyList();
        }
        List<DeliverySiteStandardTempConditionDataObj> deliverySiteStandardTempConditions = tmsDeliveryPickMapper.queryPickStandardItemTemperatureConditionsByDelSiteIds(deliverySiteIds);
        return DeliverySiteStandardTempConditionDataConverter.objList2DTO(deliverySiteStandardTempConditions);
    }

    @Override
    public Map<Long, List<DeliverySiteStandardTempUnit>> queryPickStandardItemTemperatureUnitByDelSiteIds(List<Long> deliverySiteIds) {
        if(CollectionUtils.isEmpty(deliverySiteIds)){
            return new HashMap<>();
        }
        List<DeliverySiteStandardTempConditionDataObj> deliverySiteStandardTempConditions =
                tmsDeliveryPickMapper.queryPickStandardItemTemperatureUnitByDelSiteIds(deliverySiteIds);
        List<DeliverySiteStandardTempUnit> list =  DeliverySiteStandardTempConditionDataConverter
                .objList2DTOForUnit(deliverySiteStandardTempConditions);

        return list.stream().collect(Collectors.groupingBy(DeliverySiteStandardTempUnit::getDeliverySiteId));
    }


    @Override
    public void scanCodeAdd(DeliveryPickEntity deliveryPickEntity) {
        if(deliveryPickEntity == null || deliveryPickEntity.getId() == null){
            return;
        }
        // 更新扫码次数
        LambdaUpdateWrapper<TmsDeliveryPick> pickLambdaUpdateWrapper = new LambdaUpdateWrapper<TmsDeliveryPick>()
                .eq(TmsDeliveryPick::getId, deliveryPickEntity.getId())
                .eq(TmsDeliveryPick::getScanCount, deliveryPickEntity.getScanCount())
                .set(TmsDeliveryPick::getScanCount, deliveryPickEntity.getScanCount()+ 1);

        int update = tmsDeliveryPickMapper.update(null, pickLambdaUpdateWrapper);
        if (update == 0){
            throw new BizException("扫码失败，请重新扫码");
        }
    }

    @Override
    public List<DeliveryPickEntity> queryByBatchIdList(List<Long> deliveryBatchIdList) {
        if(CollectionUtils.isEmpty(deliveryBatchIdList)){
            return Collections.emptyList();
        }

        List<TmsDeliveryPick> tmsDeliveryPicks = tmsDeliveryPickMapper.selectList(new LambdaQueryWrapper<TmsDeliveryPick>()
                .in(TmsDeliveryPick::getDeliveryBatchId, deliveryBatchIdList)
        );

        return tmsDeliveryPicks.stream().map(TmsDeliveryPickConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public void batchUpdate(List<DeliveryPickEntity> deliveryPickEntityList) {
        if(CollectionUtils.isEmpty(deliveryPickEntityList)){
            return;
        }
        List<TmsDeliveryPick> tmsDeliveryPicks = deliveryPickEntityList.stream().map(TmsDeliveryPickConverter::entity2Do).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsDeliveryPicks,TmsDeliveryPick.class);
    }
}
