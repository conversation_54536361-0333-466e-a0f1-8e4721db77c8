package net.summerfarm.tms.repository;

import net.summerfarm.tms.after.ExpenseAuditRecordRepository;
import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.gray.mapper.Expense;
import net.summerfarm.tms.gray.mapper.ExpenseGrayMapper;
import net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord;
import net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecordGrayMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * Description: <br/>
 * date: 2022/11/11 16:58<br/>
 *
 * <AUTHOR> />
 */
@Service
public class ExpenseAuditRecordRepositoryImpl implements ExpenseAuditRecordRepository {

    @Resource
    TmsExpenseAuditRecordGrayMapper tmsExpenseAuditRecordGrayMapper;
    @Resource
    ExpenseGrayMapper expenseGrayMapper;

    @Override
    public Long save(ExpenseEntity expenseEntity) {
        Expense expense = expenseGrayMapper.selectById(expenseEntity.getId());
        expense.setStatus(expenseEntity.getStatus());
        expense.setReason(expenseEntity.getReason());
        expenseGrayMapper.updateById(expense);

        TmsExpenseAuditRecord tmsExpenseAuditRecord = new TmsExpenseAuditRecord();
        tmsExpenseAuditRecord.setMname(expense.getMname());
        tmsExpenseAuditRecord.setStatus(expense.getStatus());
        tmsExpenseAuditRecord.setAuditName(expenseEntity.getUsername());
        tmsExpenseAuditRecord.setReason(expense.getReason());
        tmsExpenseAuditRecord.setDriverId(expense.getDriverId());
        tmsExpenseAuditRecord.setDeliveryPathId(expense.getDeliveryPathId());
        tmsExpenseAuditRecord.setExpenseId(expense.getId());
        tmsExpenseAuditRecord.setSubmitTime(expense.getCreateTime());
        tmsExpenseAuditRecord.setCreateTime(LocalDateTime.now());
        tmsExpenseAuditRecord.setTmsDeliverySiteId(expense.getTmsDeliverySiteId());

        tmsExpenseAuditRecordGrayMapper.insert(tmsExpenseAuditRecord);

        return expense.getTmsDeliverySiteId();
    }
}
