package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.NonNull;
import net.summerfarm.tms.base.path.PathSectionRepository;
import net.summerfarm.tms.base.path.entity.TmsPathSectionEntity;
import net.summerfarm.tms.converter.PathConverter;
import net.summerfarm.tms.converter.TmsPathSectionConverter;
import net.summerfarm.tms.dao.TmsPath;
import net.summerfarm.tms.dao.TmsPathSection;
import net.summerfarm.tms.mapper.TmsPathMapper;
import net.summerfarm.tms.mapper.TmsPathSectionMapper;
import net.summerfarm.tms.query.path.PathSectionQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PathSectionRepositoryImpl implements PathSectionRepository {
    @Resource
    TmsPathSectionMapper tmsPathSectionMapper;
    @Resource
    TmsPathMapper tmsPathMapper;

    @Override
    public void saveOrUpdate(TmsPathSectionEntity tmsPathSectionEntity) {
        TmsPathSection tmsPathSection = tmsPathSectionMapper.selectOne(new LambdaQueryWrapper<TmsPathSection>()
                .eq(TmsPathSection::getBeginSiteId, tmsPathSectionEntity.getBeginSiteId())
                .eq(TmsPathSection::getEndSiteId, tmsPathSectionEntity.getEndSiteId())
                .orderByDesc(TmsPathSection::getId)
                .last("limit 1"));
        if (tmsPathSection == null) {
            //新增
            if (tmsPathSectionEntity.getPathId() == -1) {
                return;
            }
            tmsPathSectionMapper.insert(PathConverter.entity2SectionDo(tmsPathSectionEntity, null));
        } else {
            //更新
            tmsPathSectionEntity.setId(tmsPathSection.getId());
            if (tmsPathSectionEntity.getPathId() == -1) {
                tmsPathSectionMapper.deleteById(PathConverter.entity2SectionDo(tmsPathSectionEntity, null));
            } else {
                tmsPathSectionMapper.updateById(PathConverter.entity2SectionDo(tmsPathSectionEntity, null));
            }
        }
    }

    @Override
    public List<TmsPathSectionEntity> queryList(PathSectionQuery pathSectionQuery) {
        List<TmsPathSection> tmsPathSections = tmsPathSectionMapper.selectList(new LambdaQueryWrapper<TmsPathSection>()
                .eq(pathSectionQuery.getBeginSiteId() != null, TmsPathSection::getBeginSiteId, pathSectionQuery.getBeginSiteId())
                .in(!CollectionUtils.isEmpty(pathSectionQuery.getEndSiteIds()), TmsPathSection::getEndSiteId, pathSectionQuery.getEndSiteIds())
                .eq(pathSectionQuery.getType() != null, TmsPathSection::getType, pathSectionQuery.getType())
        );
        return tmsPathSections.stream().map(TmsPathSectionConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public TmsPathSectionEntity query(PathSectionQuery pathSectionQuery) {
        TmsPathSection tmsPathSection = tmsPathSectionMapper.selectOne(new LambdaQueryWrapper<TmsPathSection>()
                .eq(pathSectionQuery.getBeginSiteId() != null,TmsPathSection::getBeginSiteId, pathSectionQuery.getBeginSiteId())
                .eq(pathSectionQuery.getEndSiteId() != null ,TmsPathSection::getEndSiteId, pathSectionQuery.getEndSiteId())
                .eq(pathSectionQuery.getType() != null, TmsPathSection::getType, pathSectionQuery.getType())
        );
        return TmsPathSectionConverter.do2Entity(tmsPathSection);
    }

    @Override
    public List<TmsPathSectionEntity> queryWithPath(PathSectionQuery pathSectionQuery) {
        List<TmsPathSection> tmsPathSections = tmsPathSectionMapper.selectList(new LambdaQueryWrapper<TmsPathSection>()
                .eq(pathSectionQuery.getBeginSiteId() != null, TmsPathSection::getBeginSiteId, pathSectionQuery.getBeginSiteId())
                .in(!CollectionUtils.isEmpty(pathSectionQuery.getEndSiteIds()), TmsPathSection::getEndSiteId, pathSectionQuery.getEndSiteIds())
                .eq(pathSectionQuery.getType() != null, TmsPathSection::getType, pathSectionQuery.getType())
        );

        List<TmsPathSectionEntity> pathSectionEntities = tmsPathSections.stream().map(TmsPathSectionConverter::do2Entity).collect(Collectors.toList());

        if(!CollectionUtils.isEmpty(tmsPathSections)){
            List<Long> pathIdList = tmsPathSections.stream().map(TmsPathSection::getPathId).distinct().collect(Collectors.toList());
            List<TmsPath> tmsPaths = tmsPathMapper.selectList(new LambdaQueryWrapper<TmsPath>().in(TmsPath::getId, pathIdList));
            Map<Long, TmsPath> pathMap = tmsPaths.stream().collect(Collectors.toMap(TmsPath::getId, Function.identity()));

            for (TmsPathSectionEntity pathSectionEntity : pathSectionEntities) {
                pathSectionEntity.setPathEntity(PathConverter.do2Entity(pathMap.get(pathSectionEntity.getPathId())));
            }
        }

        return pathSectionEntities;
    }

    @Override
    public void removeList(PathSectionQuery pathSectionQuery) {
        tmsPathSectionMapper.delete(new LambdaQueryWrapper<TmsPathSection>()
                .eq(TmsPathSection::getBeginSiteId, pathSectionQuery.getBeginSiteId())
                .in(TmsPathSection::getEndSiteId, pathSectionQuery.getEndSiteIds())
                .eq(TmsPathSection::getType, pathSectionQuery.getType())
        );
    }

    @Override
    public void removeByPathId(@NonNull Long pathId) {
        tmsPathSectionMapper.delete(new LambdaQueryWrapper<TmsPathSection>()
                .eq(TmsPathSection::getPathId, pathId));

    }

    @Override
    public List<TmsPathSectionEntity> queryListByPathId(@NonNull Long pathId) {
        List<TmsPathSection> tmsPathSections = tmsPathSectionMapper.selectList(new LambdaQueryWrapper<TmsPathSection>()
                .eq(TmsPathSection::getPathId, pathId).orderByAsc(TmsPathSection::getSequence));
        return TmsPathSectionConverter.doList2EntityList(tmsPathSections);
    }

    @Override
    public Map<Long, List<TmsPathSectionEntity>> queryByPathIds(List<Long> validPathIds){
        if(CollectionUtils.isEmpty(validPathIds)){
            return Collections.emptyMap();
        }
        List<TmsPathSection> tmsPathSections = tmsPathSectionMapper.selectList(new LambdaQueryWrapper<TmsPathSection>()
               .in(TmsPathSection::getPathId, validPathIds));

        return tmsPathSections.stream().collect(Collectors.groupingBy(TmsPathSection::getPathId))
                .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, a -> TmsPathSectionConverter.doList2EntityList(a.getValue())));
    }

    @Override
    public List<TmsPathSectionEntity> queryMatchEndSiteTrunkPath(Long endSiteId) {
        if (endSiteId == null) {
            return Collections.emptyList();
        }
        List<TmsPathSection> tmsPathSections =  tmsPathSectionMapper.queryMatchEndSiteTrunkPath(endSiteId);
        return TmsPathSectionConverter.doList2EntityList(tmsPathSections);    }
}
