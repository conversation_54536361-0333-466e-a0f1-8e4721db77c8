package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.converter.TmsDeliverySiteItemCodeConverter;
import net.summerfarm.tms.dao.TmsDeliveryOrder;
import net.summerfarm.tms.dao.TmsDeliverySiteItem;
import net.summerfarm.tms.dao.TmsDeliverySiteItemCode;
import net.summerfarm.tms.delivery.DeliverySiteItemCodeRepository;
import net.summerfarm.tms.delivery.entity.DeliverySiteItemCodeEntity;
import net.summerfarm.tms.enums.DeliverySiteItemTypeEnum;
import net.summerfarm.tms.exceptions.ErrorCodeEnum;
import net.summerfarm.tms.exceptions.TmsAssert;
import net.summerfarm.tms.mapper.TmsDeliveryPickMapper;
import net.summerfarm.tms.mapper.TmsDeliverySiteItemCodeMapper;
import net.summerfarm.tms.mapper.TmsDeliverySiteItemMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class DeliverySiteItemCodeRepositoryImpl implements DeliverySiteItemCodeRepository {
    @Resource
    private TmsDeliverySiteItemCodeMapper tmsDeliverySiteItemCodeMapper;
    @Resource
    private TmsDeliverySiteItemMapper tmsDeliverySiteItemMapper;

    @Override
    public void save(DeliverySiteItemCodeEntity deliverySiteItemCodeEntity) {
        Long count = tmsDeliverySiteItemCodeMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(TmsDeliverySiteItemCode::getOnlyCode, deliverySiteItemCodeEntity.getOnlyCode()));
        TmsAssert.isTrue(count == 0, ErrorCodeEnum.SITE_ITEM_CODE_ERROR, "已经存在");
        List<TmsDeliverySiteItem> itemList = tmsDeliverySiteItemMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItem>()
                .eq(TmsDeliverySiteItem::getDeliverySiteId, deliverySiteItemCodeEntity.getDeliverySiteId())
                .eq(TmsDeliverySiteItem::getOutItemId, deliverySiteItemCodeEntity.getOutItemId())
                .eq(TmsDeliverySiteItem::getType, DeliverySiteItemTypeEnum.DELIVERY.getCode())
        );
        TmsAssert.isTrue(itemList.size() == 1, ErrorCodeEnum.SITE_ITEM_ERROR, "不存在");

        TmsDeliverySiteItemCode tmsDeliverySiteItemCode = new TmsDeliverySiteItemCode();
        tmsDeliverySiteItemCode.setDeliverySiteItemId(itemList.get(0).getId());
        tmsDeliverySiteItemCode.setDeliverySiteId(deliverySiteItemCodeEntity.getDeliverySiteId());
        tmsDeliverySiteItemCode.setOutItemId(deliverySiteItemCodeEntity.getOutItemId());
        tmsDeliverySiteItemCode.setOnlyCode(deliverySiteItemCodeEntity.getOnlyCode());
        tmsDeliverySiteItemCodeMapper.insert(tmsDeliverySiteItemCode);
        deliverySiteItemCodeEntity.setId(tmsDeliverySiteItemCode.getId());
    }

    @Override
    public Long countByDeliverySiteItemId(Long deliverySiteItemId) {
       return tmsDeliverySiteItemCodeMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(TmsDeliverySiteItemCode::getDeliverySiteItemId,deliverySiteItemId));
    }

    @Override
    public Long counyByOnlyCode(String onlyCode) {
        return tmsDeliverySiteItemCodeMapper.selectCount(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(TmsDeliverySiteItemCode::getOnlyCode,onlyCode));
    }

    @Override
    public DeliverySiteItemCodeEntity queryByOnlyCode(String code) {
        TmsDeliverySiteItemCode tmsDeliverySiteItemCode = tmsDeliverySiteItemCodeMapper.selectOne(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(TmsDeliverySiteItemCode::getOnlyCode, code)
                .last("limit 1"));
        return TmsDeliverySiteItemCodeConverter.do2Entity(tmsDeliverySiteItemCode);
    }

    @Override
    public Map<Long, Long> delSiteItemId2ScanCountMapByDelSiteItemIds(List<Long> deliverySiteItemIds) {
        if(CollectionUtils.isEmpty(deliverySiteItemIds)){
            return Collections.emptyMap();
        }
        List<TmsDeliverySiteItemCode> tmsDeliverySiteItemCodes = tmsDeliverySiteItemCodeMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .in(TmsDeliverySiteItemCode::getDeliverySiteItemId, deliverySiteItemIds));

        return tmsDeliverySiteItemCodes.stream().collect(Collectors.groupingBy(TmsDeliverySiteItemCode::getDeliverySiteItemId,Collectors.counting()));
    }

    @Override
    public List<DeliverySiteItemCodeEntity> queryListByDeliverySiteId(Long deliverySiteId) {
        if(deliverySiteId == null){
            return Collections.emptyList();
        }
        List<TmsDeliverySiteItemCode> tmsDeliverySiteItemCodes = tmsDeliverySiteItemCodeMapper.selectList(new LambdaQueryWrapper<TmsDeliverySiteItemCode>()
                .eq(TmsDeliverySiteItemCode::getDeliverySiteId, deliverySiteId));

        return tmsDeliverySiteItemCodes.stream().map(TmsDeliverySiteItemCodeConverter::do2Entity).collect(Collectors.toList());
    }
}
