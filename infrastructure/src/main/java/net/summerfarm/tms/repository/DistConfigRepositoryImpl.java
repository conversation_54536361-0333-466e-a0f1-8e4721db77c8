package net.summerfarm.tms.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.dao.TmsDistConfig;
import net.summerfarm.tms.dao.TmsDistSite;
import net.summerfarm.tms.dist.DistConfigRepository;
import net.summerfarm.tms.enums.DistConfigTypeEnum;
import net.summerfarm.tms.mapper.TmsDistConfigMapper;
import net.summerfarm.tms.mapper.TmsDistSiteMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Description:委托单配置仓库实现类
 * date: 2022/9/16 18:22
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DistConfigRepositoryImpl implements DistConfigRepository {

    private final TmsDistConfigMapper tmsDistConfigMapper;

    private final TmsDistSiteMapper tmsDistSiteMapper;

    @Override
    public String findConfigValue(Long beginSiteId, Long endSiteId, DistConfigTypeEnum distConfigTypeEnum) {
        TmsDistConfig tmsDistConfig = tmsDistConfigMapper.selectOne(new LambdaQueryWrapper<TmsDistConfig>()
                .eq(TmsDistConfig::getBeginSiteId, beginSiteId)
                .eq(TmsDistConfig::getEndSiteId, endSiteId)
                .eq(TmsDistConfig::getType, distConfigTypeEnum.getCode())
                .eq(TmsDistConfig::getDeleteFlag, 0));
        if (tmsDistConfig == null) {
            return null;
        }
        return tmsDistConfig.getConfigValue();
    }

    @Override
    public Long queryCount(Long beginSiteId, Long endSiteId, DistConfigTypeEnum distConfigTypeEnum) {
        return tmsDistConfigMapper.selectCount(new LambdaQueryWrapper<TmsDistConfig>()
                .eq(TmsDistConfig::getBeginSiteId, beginSiteId)
                .eq(TmsDistConfig::getEndSiteId, endSiteId)
                .eq(TmsDistConfig::getType, distConfigTypeEnum.getCode())
                .eq(TmsDistConfig::getDeleteFlag, 0));
    }

    @Override
    public void configInit(List<String> configs) {
        if (configs == null || configs.isEmpty()) {
            configs = getDefaultConfigs();
        }
        log.info("委托单配置初始化数据：{}", JSON.toJSONString(configs));
        List<String> errorMsgList = new ArrayList<>();
        for (String config : configs) {
            String[] configProperties = config.split(",");
            String warehouseConfigStr = configProperties[0];
            String cityConfigStr = configProperties[1];
            String[] warehouseConfigs = warehouseConfigStr.split("-");
            String[] cityConfigs = cityConfigStr.split("-");
            String[] midConfigs = null;
            if (configProperties.length > 2) {
                //中转站额外配置
                String midConfigStr = configProperties[2];
                midConfigs = midConfigStr.split("-");
            }
            TmsDistConfig tmsDistConfig = new TmsDistConfig();
            tmsDistConfig.setType(DistConfigTypeEnum.BLACK_LIST.getCode());
            tmsDistConfig.setCreateId(0);
            tmsDistConfig.setCreator("系统");
            //查询仓库编号对应点位ID
            TmsDistSite warehouseSite = getTmsDistSite(warehouseConfigs);
            if (warehouseSite == null) {
                String errorMsg = String.format("tms_dist_site表不存在type:%s,out_business_no:%s的初始化数据", warehouseConfigs[0], warehouseConfigs[1]);
                errorMsgList.add(errorMsg);
                continue;
            }
            tmsDistConfig.setBeginSiteId(warehouseSite.getId());
            //查询城配编号对应点位ID
            TmsDistSite citySite = getTmsDistSite(cityConfigs);
            if (citySite == null) {
                String errorMsg = String.format("tms_dist_site表不存在type:%s,out_business_no:%s的初始化数据", cityConfigs[0], cityConfigs[1]);
                errorMsgList.add(errorMsg);
                continue;
            }
            tmsDistConfig.setEndSiteId(citySite.getId());
            //查询中转站编号对应点位ID
            if (midConfigs != null) {
                TmsDistSite midSite = getTmsDistSite(midConfigs);
                if (midSite == null) {
                    String errorMsg = String.format("tms_dist_site表不存在type:%s,out_business_no:%s的初始化数据", midConfigs[0], midConfigs[1]);
                    errorMsgList.add(errorMsg);
                    continue;
                }
                tmsDistConfig.setType(DistConfigTypeEnum.TRANSFER_SITE.getCode());
                tmsDistConfig.setConfigValue(String.valueOf(midSite.getId()));
            }
            //查询对应配置是否已经存在
            Long count = queryCount(tmsDistConfig.getBeginSiteId(), tmsDistConfig.getEndSiteId(), DistConfigTypeEnum.getDistConfigTypeByCode(tmsDistConfig.getType()));
            if (count >= 1) {
                String errorMsg = String.format("tms_dist_config表已存在type:%s,begin_site_id:%s,end_site_id:%s的初始化数据", tmsDistConfig.getType(), tmsDistConfig.getBeginSiteId(), tmsDistConfig.getEndSiteId());
                errorMsgList.add(errorMsg);
                continue;
            }
            tmsDistConfigMapper.insert(tmsDistConfig);
        }
        if (errorMsgList.isEmpty()){
            log.info("委托单配置初始化数据成功");
        }else {
            log.error("委托单配置初始化数据-异常信息:{}",JSON.toJSONString(errorMsgList));
        }
    }

    private TmsDistSite getTmsDistSite(String[] configs) {
        TmsDistSite warehouseSite = tmsDistSiteMapper.selectOne(new LambdaQueryWrapper<TmsDistSite>()
                .eq(TmsDistSite::getType, configs[0])
                .eq(TmsDistSite::getOutBusinessNo, configs[1]));
        return warehouseSite;
    }

    private List<String> getDefaultConfigs() {
        List<String> defaultConfigs = new ArrayList<>();
        defaultConfigs.add("2-10,1-10");
        defaultConfigs.add("2-1,1-1");
        defaultConfigs.add("2-2,1-2");
        defaultConfigs.add("2-2,1-57");
        defaultConfigs.add("2-2,1-95");
        defaultConfigs.add("2-62,1-9");
        defaultConfigs.add("2-38,1-38");
        defaultConfigs.add("2-38,1-49");
        defaultConfigs.add("2-14,1-14");
        defaultConfigs.add("2-14,1-97");
        defaultConfigs.add("2-14,1-77");
        defaultConfigs.add("2-59,1-75");
        defaultConfigs.add("2-24,1-24");
        defaultConfigs.add("2-24,1-45");
        defaultConfigs.add("2-29,1-29");
        defaultConfigs.add("2-48,1-50");
        defaultConfigs.add("2-48,1-62");
        defaultConfigs.add("2-60,1-76");
        defaultConfigs.add("2-63,1-83");
        defaultConfigs.add("2-64,1-87");
        defaultConfigs.add("2-38,1-73,1-40");
        return defaultConfigs;
    }

    @Override
    public void update(List<String> storeNos, DistConfigTypeEnum distConfigTypeEnum) {
        //先查询
        TmsDistConfig tmsDistConfig = tmsDistConfigMapper.selectOne(new LambdaQueryWrapper<TmsDistConfig>()
                .eq(TmsDistConfig::getBeginSiteId, distConfigTypeEnum.getCode())
                .eq(TmsDistConfig::getEndSiteId, distConfigTypeEnum.getCode())
                .eq(TmsDistConfig::getType, distConfigTypeEnum.getCode())
        );

        if(!CollectionUtils.isEmpty(storeNos)){
            StringJoiner stringJoiner = new StringJoiner(",");
            for (String storeNo : storeNos) {
                stringJoiner.add(storeNo);
            }
            tmsDistConfig.setConfigValue(stringJoiner.toString());
        }else{
            tmsDistConfig.setConfigValue("-100");
        }
        tmsDistConfigMapper.updateById(tmsDistConfig);
    }
    

    @Override
    public void add(List<String> grayStoreNos, DistConfigTypeEnum distConfigTypeEnum) {
        TmsDistConfig tmsDistConfig = new TmsDistConfig();
        tmsDistConfig.setCreateId(0);
        tmsDistConfig.setCreator("系统");
        tmsDistConfig.setType(distConfigTypeEnum.getCode());
        tmsDistConfig.setBeginSiteId(Long.parseLong(String.valueOf(distConfigTypeEnum.getCode())));
        tmsDistConfig.setEndSiteId(Long.parseLong(String.valueOf(distConfigTypeEnum.getCode())));
        if(CollectionUtils.isEmpty(grayStoreNos)){
            String configValue = grayStoreNos.stream().collect(Collectors.joining(","));
            tmsDistConfig.setConfigValue(configValue);
        }else{
            tmsDistConfig.setConfigValue("-100");
        }

        tmsDistConfigMapper.insert(tmsDistConfig);
    }
}
