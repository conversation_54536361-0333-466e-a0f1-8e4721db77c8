package net.summerfarm.tms.repository;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import net.summerfarm.tms.delivery.DeliveryOrderRepository;
import net.summerfarm.tms.delivery.entity.DeliveryOrderEntity;
import net.summerfarm.tms.lack.LackAppealRepository;
import net.summerfarm.tms.lack.LackApprovedRepository;
import net.summerfarm.tms.lack.LackResponsibleRepository;
import net.summerfarm.tms.lack.entity.LackApprovedAppealEntity;
import net.summerfarm.tms.lack.entity.LackApprovedEntity;
import net.summerfarm.tms.converter.TmsLackGoodsApprovedConverter;
import net.summerfarm.tms.dao.TmsLackGoodsApproved;
import net.summerfarm.tms.lack.entity.LackApprovedResponsibleEntity;
import net.summerfarm.tms.mapper.TmsLackGoodsApprovedMapper;
import net.summerfarm.tms.query.delivery.DeliveryLackApprovedQuery;
import net.summerfarm.tms.query.delivery.DeliveryOrderQuery;
import net.summerfarm.tms.query.delivery.LackApprovedQuery;
import net.summerfarm.tms.utils.MybatisPlusUtil;
import net.summerfarm.tms.utils.PageInfoHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/10/21 17:02<br/>
 *
 * <AUTHOR> />
 */
@Service
public class LackApprovedRepositoryImpl implements LackApprovedRepository {
    @Resource
    private TmsLackGoodsApprovedMapper tmsLackGoodsApprovedMapper;
    @Resource
    private LackAppealRepository lackAppealRepository;
    @Resource
    private LackResponsibleRepository lackResponsibleRepository;
    @Resource
    private DeliveryOrderRepository deliveryOrderRepository;

    @Override
    public void save(LackApprovedEntity lackApprovedEntity) {
        TmsLackGoodsApproved lackGoodsApproved = TmsLackGoodsApprovedConverter.entity2do(lackApprovedEntity);
        tmsLackGoodsApprovedMapper.insert(lackGoodsApproved);
        lackApprovedEntity.setId(lackGoodsApproved.getId());
    }

    @Override
    public LackApprovedEntity query(Long id) {
        if(id == null){
            return null;
        }
        return TmsLackGoodsApprovedConverter.do2Entity(tmsLackGoodsApprovedMapper.selectById(id));
    }

    @Override
    public void update(LackApprovedEntity lackApprovedEntity) {
        if(lackApprovedEntity == null){
            return;
        }
        TmsLackGoodsApproved lackGoodsApproved = TmsLackGoodsApprovedConverter.entity2do(lackApprovedEntity);
        tmsLackGoodsApprovedMapper.updateById(lackGoodsApproved);
    }

    @Override
    public long queryCount(DeliveryLackApprovedQuery lackApprovedQuery) {
        return tmsLackGoodsApprovedMapper.selectCount(new LambdaQueryWrapper<TmsLackGoodsApproved>()
                .eq(!StringUtils.isEmpty(lackApprovedQuery.getSku()), TmsLackGoodsApproved::getSku, lackApprovedQuery.getSku())
                .eq(lackApprovedQuery.getAmount() != null, TmsLackGoodsApproved::getAmount, lackApprovedQuery.getAmount())
                .eq(lackApprovedQuery.getDeliverySiteId() != null, TmsLackGoodsApproved::getTmsDeliverySiteId, lackApprovedQuery.getDeliverySiteId())
                .eq(lackApprovedQuery.getOrderNo() != null, TmsLackGoodsApproved::getOrderNo, lackApprovedQuery.getOrderNo())
                .in(!CollectionUtils.isEmpty(lackApprovedQuery.getIds()),TmsLackGoodsApproved::getId,lackApprovedQuery.getIds())
                .eq(lackApprovedQuery.getState() != null,TmsLackGoodsApproved::getState,lackApprovedQuery.getState())
        );
    }

    @Override
    public PageInfo<LackApprovedEntity> queryPageList(LackApprovedQuery queryLackApprovedDTO) {
        PageHelper.startPage(queryLackApprovedDTO.getPageIndex(), queryLackApprovedDTO.getPageSize());
        Date startTime = queryLackApprovedDTO.getStartTime();
        Date endTime = queryLackApprovedDTO.getEndTime();
        if (startTime != null && endTime != null) {
            queryLackApprovedDTO.setStartTime(DateUtil.beginOfDay(startTime));
            queryLackApprovedDTO.setEndTime(DateUtil.endOfDay(endTime));
        }
        List<LackApprovedEntity> query = tmsLackGoodsApprovedMapper.query(queryLackApprovedDTO);

        return PageInfoHelper.createPageInfo(query);
    }

    @Override
    public PageInfo<LackApprovedEntity> queryListWithAppealPage(LackApprovedQuery lackApprovedQuery) {
        PageHelper.startPage(lackApprovedQuery.getPageIndex(), lackApprovedQuery.getPageSize());
        lackApprovedQuery.setStartTime(lackApprovedQuery.getStartTime() == null ? null : DateUtil.beginOfDay(lackApprovedQuery.getStartTime()));
        lackApprovedQuery.setEndTime(lackApprovedQuery.getStartTime() == null ? null : DateUtil.endOfDay(lackApprovedQuery.getEndTime()));
        List<LackApprovedEntity> tmsLackApprovedEntities = tmsLackGoodsApprovedMapper.query(lackApprovedQuery);
        //查询申诉信息
        if(!CollectionUtils.isEmpty(tmsLackApprovedEntities)){
            List<Long> lackGoodsApprovedIds = tmsLackApprovedEntities.stream().map(LackApprovedEntity::getId).collect(Collectors.toList());
            //查询最新申诉信息
            List<LackApprovedAppealEntity> lackApprovedAppealEntities = lackAppealRepository.queryByApprovedIds(lackGoodsApprovedIds);
            Map<Long, List<LackApprovedAppealEntity>> lackApprovedAppealListMap = lackApprovedAppealEntities.stream().collect(Collectors.groupingBy(LackApprovedAppealEntity::getApprovedId));
            //查询判责信息
            List<LackApprovedResponsibleEntity> responsibleEntities = lackResponsibleRepository.queryListByApprovedIds(lackGoodsApprovedIds);
            Map<Long, List<LackApprovedResponsibleEntity>> lackApproveddResponsibleListMap = responsibleEntities.stream().collect(Collectors.groupingBy(LackApprovedResponsibleEntity::getApprovedId));

            for (LackApprovedEntity tmsLackApprovedEntity : tmsLackApprovedEntities) {
                if(lackApprovedAppealListMap.get(tmsLackApprovedEntity.getId()) != null){
                    List<LackApprovedAppealEntity> appealEntityList = lackApprovedAppealListMap.get(tmsLackApprovedEntity.getId());
                    tmsLackApprovedEntity.setLackApprovedAppealEntities(appealEntityList.stream().sorted(Comparator.comparingLong(LackApprovedAppealEntity::getId).reversed()).collect(Collectors.toList()));
                }
                if(lackApproveddResponsibleListMap.get(tmsLackApprovedEntity.getId()) != null){
                    tmsLackApprovedEntity.setLackApprovedResponsibleEntities(lackApproveddResponsibleListMap.get(tmsLackApprovedEntity.getId()));
                }
            }

        }
        return PageInfoHelper.createPageInfo(tmsLackApprovedEntities);
    }

    @Override
    public List<LackApprovedEntity> queryListByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        List<TmsLackGoodsApproved> tmsLackGoodsApproveds = tmsLackGoodsApprovedMapper.selectList(new LambdaQueryWrapper<TmsLackGoodsApproved>()
                .in(TmsLackGoodsApproved::getId, ids)
        );
        return tmsLackGoodsApproveds.stream().map(TmsLackGoodsApprovedConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public List<LackApprovedEntity> queryList(LackApprovedQuery lackApprovedQuery) {
        List<TmsLackGoodsApproved> tmsLackGoodsApproveds = tmsLackGoodsApprovedMapper.selectList(new LambdaQueryWrapper<TmsLackGoodsApproved>()
                .eq(lackApprovedQuery.getState() != null, TmsLackGoodsApproved::getState, lackApprovedQuery.getState())
                .in(!CollectionUtils.isEmpty(lackApprovedQuery.getIds()), TmsLackGoodsApproved::getId, lackApprovedQuery.getIds())
                .notIn(!CollectionUtils.isEmpty(lackApprovedQuery.getIdsNotIn()),TmsLackGoodsApproved::getId, lackApprovedQuery.getIdsNotIn())
        );

        return tmsLackGoodsApproveds.stream().map(TmsLackGoodsApprovedConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public void updateBatch(List<LackApprovedEntity> lackApprovedEntityList) {
        if(CollectionUtils.isEmpty(lackApprovedEntityList)){
            return;
        }
        List<TmsLackGoodsApproved> tmsLackGoodsApproveds = lackApprovedEntityList.stream().map(TmsLackGoodsApprovedConverter::entity2do).collect(Collectors.toList());
        MybatisPlusUtil.updateBatch(tmsLackGoodsApproveds,TmsLackGoodsApproved.class);
    }

    @Override
    public LackApprovedEntity queryDetailWithAppealResponsible(Long lackApprovedId) {
        LackApprovedEntity lackApprovedEntity = tmsLackGoodsApprovedMapper.queryDetailById(lackApprovedId);
        if(lackApprovedEntity == null){
            return null;
        }
        //查询申诉信息
        List<LackApprovedAppealEntity> lackApprovedAppealEntities = lackAppealRepository.queryByApprovedIds(Collections.singletonList(lackApprovedEntity.getId()));
        lackApprovedAppealEntities.sort(Comparator.comparing(LackApprovedAppealEntity::getCreateTime));
        lackApprovedEntity.setLackApprovedAppealEntities(lackApprovedAppealEntities);
        //查询判责信息
        lackApprovedEntity.setLackApprovedResponsibleEntities(lackResponsibleRepository.queryListByApprovedId(lackApprovedId));

        //查询单据来源
        if(lackApprovedEntity.getDeliveryBatchId() != null && StringUtils.isNotBlank(lackApprovedEntity.getOrderNo())){
            List<DeliveryOrderEntity> deliveryOrders = deliveryOrderRepository.queryList(DeliveryOrderQuery.builder()
                    .batchId(lackApprovedEntity.getDeliveryBatchId())
                    .outOrderId(lackApprovedEntity.getOrderNo())
                    .build());

            if(!CollectionUtils.isEmpty(deliveryOrders)){
                DeliveryOrderEntity deliveryOrder = deliveryOrders.get(0);
                lackApprovedEntity.setOrderSource(deliveryOrder.getSource().getCode());
            }
        }
        return lackApprovedEntity;
    }

    @Override
    public List<LackApprovedEntity> queryListWithAppealResponsible(LackApprovedQuery lackApprovedQuery) {
        List<LackApprovedEntity> lackApprovedEntityList = tmsLackGoodsApprovedMapper.query(lackApprovedQuery);
        if(CollectionUtils.isEmpty(lackApprovedEntityList)){
            return Collections.emptyList();
        }
        List<Long> approvedIdList = lackApprovedEntityList.stream().map(LackApprovedEntity::getId).collect(Collectors.toList());
        //查询申诉信息
        List<LackApprovedAppealEntity> lackApprovedAppealEntities = lackAppealRepository.queryByApprovedIds(approvedIdList);
        Map<Long, List<LackApprovedAppealEntity>> lackApprovedAppealListMap = lackApprovedAppealEntities.stream().collect(Collectors.groupingBy(LackApprovedAppealEntity::getApprovedId));
        //查询判责信息
        List<LackApprovedResponsibleEntity> responsibleEntityList = lackResponsibleRepository.queryListByApprovedIds(approvedIdList);
        Map<Long, List<LackApprovedResponsibleEntity>> responsibleApprovedListMap = responsibleEntityList.stream().collect(Collectors.groupingBy(LackApprovedResponsibleEntity::getApprovedId));

        for (LackApprovedEntity tmsLackApprovedEntity : lackApprovedEntityList) {
            if(lackApprovedAppealListMap.get(tmsLackApprovedEntity.getId()) != null){
                tmsLackApprovedEntity.setLackApprovedAppealEntities(lackApprovedAppealListMap.get(tmsLackApprovedEntity.getId()));
            }
            if(responsibleApprovedListMap.get(tmsLackApprovedEntity.getId()) != null){
                tmsLackApprovedEntity.setLackApprovedResponsibleEntities(responsibleApprovedListMap.get(tmsLackApprovedEntity.getId()));
            }
        }
        return lackApprovedEntityList;
    }
}
