package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import net.summerfarm.tms.after.ExpenseRepository;
import net.summerfarm.tms.after.entity.ExpenseDetailEntity;
import net.summerfarm.tms.after.entity.ExpenseEntity;
import net.summerfarm.tms.converter.ExpenseConverter;
import net.summerfarm.tms.converter.ExpenseDetailConverter;
import net.summerfarm.tms.gray.mapper.Expense;
import net.summerfarm.tms.gray.mapper.ExpenseDetail;
import net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecord;
import net.summerfarm.tms.gray.mapper.ExpenseDetailGrayMapper;
import net.summerfarm.tms.gray.mapper.ExpenseGrayMapper;
import net.summerfarm.tms.gray.mapper.TmsExpenseAuditRecordGrayMapper;
import net.summerfarm.tms.query.expense.ExpenseQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: <br/>
 * date: 2022/10/21 18:26<br/>
 *
 * <AUTHOR> />
 */
@Service
public class ExpenseRepositoryImpl implements ExpenseRepository {
    @Resource
    ExpenseGrayMapper expenseGrayMapper;
    @Resource
    ExpenseDetailGrayMapper expenseDetailGrayMapper;
    @Resource
    TmsExpenseAuditRecordGrayMapper tmsExpenseAuditRecordGrayMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(ExpenseEntity expenseEntity) {
        if (expenseEntity.getId() == null) {
            Expense expense = ExpenseConverter.entity2Do(expenseEntity);
            expenseGrayMapper.insert(expense);
            expenseEntity.setId(expense.getId());
            if (expenseEntity.getExpenseDetailEntityList() != null) {
                for (ExpenseDetailEntity expenseDetailEntity : expenseEntity.getExpenseDetailEntityList()) {
                    saveDetail(expenseDetailEntity, expenseEntity.getId());
                }
            }
        } else {
            expenseGrayMapper.updateById(ExpenseConverter.entity2Do(expenseEntity));
            expenseDetailGrayMapper.delete(new LambdaQueryWrapper<ExpenseDetail>()
                    .eq(ExpenseDetail::getExpenseId, expenseEntity.getId()));
            if (expenseEntity.getExpenseDetailEntityList() != null) {
                for (ExpenseDetailEntity expenseDetailEntity : expenseEntity.getExpenseDetailEntityList()) {
                    saveDetail(expenseDetailEntity, expenseEntity.getId());
                }
            }
        }

    }

    private void saveDetail(ExpenseDetailEntity expenseDetailEntity, Integer expenseId) {
        expenseDetailEntity.setExpenseId(expenseId);
        expenseDetailGrayMapper.insert(ExpenseDetailConverter.entity2Do(expenseDetailEntity));
    }

    @Override
    public List<ExpenseEntity> queryListByDeliverySiteId(Long deliverySiteId) {
        List<Expense> expensesList = expenseGrayMapper.selectList(new LambdaQueryWrapper<Expense>()
                .eq(Expense::getTmsDeliverySiteId, deliverySiteId));
        return expensesList.stream().map(ExpenseConverter::do2Entity).collect(Collectors.toList());
    }

    @Override
    public void audit(ExpenseEntity expenseEntity) {
        Expense expense = expenseGrayMapper.selectById(expenseEntity.getId());

        TmsExpenseAuditRecord tmsExpenseAuditRecord  = new TmsExpenseAuditRecord();
        tmsExpenseAuditRecord.setMname(expenseEntity.getMname());
        tmsExpenseAuditRecord.setStatus(expense.getStatus());
        tmsExpenseAuditRecord.setReason(expense.getReason());
        tmsExpenseAuditRecord.setDriverId(expenseEntity.getDriverId());
        tmsExpenseAuditRecord.setDeliveryPathId(expenseEntity.getDeliveryPathId());
        tmsExpenseAuditRecord.setExpenseId(expense.getId());
        tmsExpenseAuditRecord.setSubmitTime(expenseEntity.getCreateTime());

        tmsExpenseAuditRecordGrayMapper.insert(tmsExpenseAuditRecord);
    }

    @Override
    public ExpenseEntity query(Integer id) {
        return ExpenseConverter.do2Entity(expenseGrayMapper.selectById(id));
    }

    @Override
    public List<ExpenseEntity> queryListWithDetail(ExpenseQuery expenseQuery) {
        if(CollectionUtils.isEmpty(expenseQuery.getDeliverySiteIds())){
            return new ArrayList<>();
        }
        List<Expense> expenses = expenseGrayMapper.selectList(new LambdaQueryWrapper<Expense>()
                .in(!CollectionUtils.isEmpty(expenseQuery.getDeliverySiteIds()), Expense::getTmsDeliverySiteId, expenseQuery.getDeliverySiteIds())
        );

        List<ExpenseEntity> expenseEntities = new ArrayList<>();
        for (Expense expense : expenses) {
            ExpenseEntity expenseEntity = ExpenseConverter.do2Entity(expense);
            List<ExpenseDetail> expenseDetails = expenseDetailGrayMapper.selectList(new LambdaQueryWrapper<ExpenseDetail>()
                    .eq(ExpenseDetail::getExpenseId, expense.getId())
            );
            expenseEntity.setExpenseDetailEntityList(expenseDetails.stream().map(ExpenseDetailConverter::do2Entity).collect(Collectors.toList()));
            expenseEntities.add(expenseEntity);
        }
        
        return expenseEntities;
    }

    @Override
    public ExpenseEntity queryByDeliverySiteId(Long siteId) {
        if(siteId == null){
            return null;
        }
        Expense expense = expenseGrayMapper.selectOne(new LambdaQueryWrapper<Expense>()
                .eq(Expense::getTmsDeliverySiteId, siteId)
                .last(" limit 1")
        );

        return ExpenseConverter.do2Entity(expense);
    }
}
