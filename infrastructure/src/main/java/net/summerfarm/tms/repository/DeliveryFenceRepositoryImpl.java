package net.summerfarm.tms.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.tms.fence.DeliveryFenceRepository;
import net.summerfarm.tms.outland.mapper.OutLandConfig;
import net.summerfarm.tms.outland.mapper.OutLandConfigMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Description: <br/>
 * date: 2023/3/7 17:54<br/>
 *
 * <AUTHOR> />
 */
@Service
@Slf4j
public class DeliveryFenceRepositoryImpl implements DeliveryFenceRepository {

    @Resource
    private OutLandConfigMapper outLandConfigMapper;

    /**
     * 查询没有区域的城市
     * @return 结果
     */
    @Override
    public List<String> queryNoAreaCity() {
        OutLandConfig config = outLandConfigMapper.selectOne(new LambdaQueryWrapper<OutLandConfig>()
                .eq(OutLandConfig::getKey, "NO_AREA_CITY")
                .last("limit 1"));
        if(config != null && StringUtils.isNotBlank(config.getValue())){
            return Arrays.asList(config.getValue().split(","));
        }
        return Collections.emptyList();
    }
}
